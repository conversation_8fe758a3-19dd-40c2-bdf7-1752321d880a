define("local_offermanager/app/app-lazy",function(){"use strict";/**
* @vue/shared v3.5.13
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function er(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const st={}.NODE_ENV!=="production"?Object.freeze({}):{},Dn={}.NODE_ENV!=="production"?Object.freeze([]):[],St=()=>{},Fg=()=>!1,to=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),ni=e=>e.startsWith("onUpdate:"),pt=Object.assign,Ga=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},Bg=Object.prototype.hasOwnProperty,Ye=(e,t)=>Bg.call(e,t),me=Array.isArray,Kr=e=>so(e)==="[object Map]",xn=e=>so(e)==="[object Set]",Mc=e=>so(e)==="[object Date]",xe=e=>typeof e=="function",dt=e=>typeof e=="string",As=e=>typeof e=="symbol",Ze=e=>e!==null&&typeof e=="object",Ka=e=>(Ze(e)||xe(e))&&xe(e.then)&&xe(e.catch),Pc=Object.prototype.toString,so=e=>Pc.call(e),Ya=e=>so(e).slice(8,-1),kc=e=>so(e)==="[object Object]",Qa=e=>dt(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,ro=er(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),$g=er("bind,cloak,else-if,else,for,html,if,model,on,once,pre,show,slot,text,memo"),oi=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},jg=/-(\w)/g,Kt=oi(e=>e.replace(jg,(t,s)=>s?s.toUpperCase():"")),Hg=/\B([A-Z])/g,Or=oi(e=>e.replace(Hg,"-$1").toLowerCase()),Yr=oi(e=>e.charAt(0).toUpperCase()+e.slice(1)),Qr=oi(e=>e?`on${Yr(e)}`:""),Sr=(e,t)=>!Object.is(e,t),On=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},ii=(e,t,s,i=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:i,value:s})},ai=e=>{const t=parseFloat(e);return isNaN(t)?e:t},qg=e=>{const t=dt(e)?Number(e):NaN;return isNaN(t)?e:t};let Vc;const no=()=>Vc||(Vc=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function us(e){if(me(e)){const t={};for(let s=0;s<e.length;s++){const i=e[s],n=dt(i)?Kg(i):us(i);if(n)for(const a in n)t[a]=n[a]}return t}else if(dt(e)||Ze(e))return e}const zg=/;(?![^(]*\))/g,Wg=/:([^]+)/,Gg=/\/\*[^]*?\*\//g;function Kg(e){const t={};return e.replace(Gg,"").split(zg).forEach(s=>{if(s){const i=s.split(Wg);i.length>1&&(t[i[0].trim()]=i[1].trim())}}),t}function he(e){let t="";if(dt(e))t=e;else if(me(e))for(let s=0;s<e.length;s++){const i=he(e[s]);i&&(t+=i+" ")}else if(Ze(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const Yg="html,body,base,head,link,meta,style,title,address,article,aside,footer,header,hgroup,h1,h2,h3,h4,h5,h6,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,summary,template,blockquote,iframe,tfoot",Qg="svg,animate,animateMotion,animateTransform,circle,clipPath,color-profile,defs,desc,discard,ellipse,feBlend,feColorMatrix,feComponentTransfer,feComposite,feConvolveMatrix,feDiffuseLighting,feDisplacementMap,feDistantLight,feDropShadow,feFlood,feFuncA,feFuncB,feFuncG,feFuncR,feGaussianBlur,feImage,feMerge,feMergeNode,feMorphology,feOffset,fePointLight,feSpecularLighting,feSpotLight,feTile,feTurbulence,filter,foreignObject,g,hatch,hatchpath,image,line,linearGradient,marker,mask,mesh,meshgradient,meshpatch,meshrow,metadata,mpath,path,pattern,polygon,polyline,radialGradient,rect,set,solidcolor,stop,switch,symbol,text,textPath,title,tspan,unknown,use,view",Zg="annotation,annotation-xml,maction,maligngroup,malignmark,math,menclose,merror,mfenced,mfrac,mfraction,mglyph,mi,mlabeledtr,mlongdiv,mmultiscripts,mn,mo,mover,mpadded,mphantom,mprescripts,mroot,mrow,ms,mscarries,mscarry,msgroup,msline,mspace,msqrt,msrow,mstack,mstyle,msub,msubsup,msup,mtable,mtd,mtext,mtr,munder,munderover,none,semantics",Jg=er(Yg),Xg=er(Qg),ev=er(Zg),tv=er("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function Rc(e){return!!e||e===""}function sv(e,t){if(e.length!==t.length)return!1;let s=!0;for(let i=0;s&&i<e.length;i++)s=oo(e[i],t[i]);return s}function oo(e,t){if(e===t)return!0;let s=Mc(e),i=Mc(t);if(s||i)return s&&i?e.getTime()===t.getTime():!1;if(s=As(e),i=As(t),s||i)return e===t;if(s=me(e),i=me(t),s||i)return s&&i?sv(e,t):!1;if(s=Ze(e),i=Ze(t),s||i){if(!s||!i)return!1;const n=Object.keys(e).length,a=Object.keys(t).length;if(n!==a)return!1;for(const u in e){const c=e.hasOwnProperty(u),h=t.hasOwnProperty(u);if(c&&!h||!c&&h||!oo(e[u],t[u]))return!1}}return String(e)===String(t)}function Za(e,t){return e.findIndex(s=>oo(s,t))}const Uc=e=>!!(e&&e.__v_isRef===!0),q=e=>dt(e)?e:e==null?"":me(e)||Ze(e)&&(e.toString===Pc||!xe(e.toString))?Uc(e)?q(e.value):JSON.stringify(e,Lc,2):String(e),Lc=(e,t)=>Uc(t)?Lc(e,t.value):Kr(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[i,n],a)=>(s[Ja(i,a)+" =>"]=n,s),{})}:xn(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>Ja(s))}:As(t)?Ja(t):Ze(t)&&!me(t)&&!kc(t)?String(t):t,Ja=(e,t="")=>{var s;return As(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Hs(e,...t){console.warn(`[Vue warn] ${e}`,...t)}let cs;class Fc{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=cs,!t&&cs&&(this.index=(cs.scopes||(cs.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=cs;try{return cs=this,t()}finally{cs=s}}else({}).NODE_ENV!=="production"&&Hs("cannot run an inactive effect scope.")}on(){cs=this}off(){cs=this.parent}stop(t){if(this._active){this._active=!1;let s,i;for(s=0,i=this.effects.length;s<i;s++)this.effects[s].stop();for(this.effects.length=0,s=0,i=this.cleanups.length;s<i;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,i=this.scopes.length;s<i;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function rv(e){return new Fc(e)}function nv(){return cs}let rt;const Xa=new WeakSet;class Bc{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,cs&&cs.active&&cs.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Xa.has(this)&&(Xa.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||jc(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Gc(this),Hc(this);const t=rt,s=Ms;rt=this,Ms=!0;try{return this.fn()}finally{({}).NODE_ENV!=="production"&&rt!==this&&Hs("Active effect was not restored correctly - this is likely a Vue internal bug."),qc(this),rt=t,Ms=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)rl(t);this.deps=this.depsTail=void 0,Gc(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Xa.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){sl(this)&&this.run()}get dirty(){return sl(this)}}let $c=0,io,ao;function jc(e,t=!1){if(e.flags|=8,t){e.next=ao,ao=e;return}e.next=io,io=e}function el(){$c++}function tl(){if(--$c>0)return;if(ao){let t=ao;for(ao=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;io;){let t=io;for(io=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(i){e||(e=i)}t=s}}if(e)throw e}function Hc(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function qc(e){let t,s=e.depsTail,i=s;for(;i;){const n=i.prevDep;i.version===-1?(i===s&&(s=n),rl(i),ov(i)):t=i,i.dep.activeLink=i.prevActiveLink,i.prevActiveLink=void 0,i=n}e.deps=t,e.depsTail=s}function sl(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(zc(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function zc(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===lo))return;e.globalVersion=lo;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!sl(e)){e.flags&=-3;return}const s=rt,i=Ms;rt=e,Ms=!0;try{Hc(e);const n=e.fn(e._value);(t.version===0||Sr(n,e._value))&&(e._value=n,t.version++)}catch(n){throw t.version++,n}finally{rt=s,Ms=i,qc(e),e.flags&=-3}}function rl(e,t=!1){const{dep:s,prevSub:i,nextSub:n}=e;if(i&&(i.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=i,e.nextSub=void 0),{}.NODE_ENV!=="production"&&s.subsHead===e&&(s.subsHead=n),s.subs===e&&(s.subs=i,!i&&s.computed)){s.computed.flags&=-5;for(let a=s.computed.deps;a;a=a.nextDep)rl(a,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function ov(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let Ms=!0;const Wc=[];function tr(){Wc.push(Ms),Ms=!1}function sr(){const e=Wc.pop();Ms=e===void 0?!0:e}function Gc(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=rt;rt=void 0;try{t()}finally{rt=s}}}let lo=0;class iv{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class nl{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,{}.NODE_ENV!=="production"&&(this.subsHead=void 0)}track(t){if(!rt||!Ms||rt===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==rt)s=this.activeLink=new iv(rt,this),rt.deps?(s.prevDep=rt.depsTail,rt.depsTail.nextDep=s,rt.depsTail=s):rt.deps=rt.depsTail=s,Kc(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const i=s.nextDep;i.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=i),s.prevDep=rt.depsTail,s.nextDep=void 0,rt.depsTail.nextDep=s,rt.depsTail=s,rt.deps===s&&(rt.deps=i)}return{}.NODE_ENV!=="production"&&rt.onTrack&&rt.onTrack(pt({effect:rt},t)),s}trigger(t){this.version++,lo++,this.notify(t)}notify(t){el();try{if({}.NODE_ENV!=="production")for(let s=this.subsHead;s;s=s.nextSub)s.sub.onTrigger&&!(s.sub.flags&8)&&s.sub.onTrigger(pt({effect:s.sub},t));for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{tl()}}}function Kc(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let i=t.deps;i;i=i.nextDep)Kc(i)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),{}.NODE_ENV!=="production"&&e.dep.subsHead===void 0&&(e.dep.subsHead=e),e.dep.subs=e}}const ol=new WeakMap,Zr=Symbol({}.NODE_ENV!=="production"?"Object iterate":""),il=Symbol({}.NODE_ENV!=="production"?"Map keys iterate":""),uo=Symbol({}.NODE_ENV!=="production"?"Array iterate":"");function Tt(e,t,s){if(Ms&&rt){let i=ol.get(e);i||ol.set(e,i=new Map);let n=i.get(s);n||(i.set(s,n=new nl),n.map=i,n.key=s),{}.NODE_ENV!=="production"?n.track({target:e,type:t,key:s}):n.track()}}function qs(e,t,s,i,n,a){const u=ol.get(e);if(!u){lo++;return}const c=h=>{h&&({}.NODE_ENV!=="production"?h.trigger({target:e,type:t,key:s,newValue:i,oldValue:n,oldTarget:a}):h.trigger())};if(el(),t==="clear")u.forEach(c);else{const h=me(e),m=h&&Qa(s);if(h&&s==="length"){const p=Number(i);u.forEach((v,w)=>{(w==="length"||w===uo||!As(w)&&w>=p)&&c(v)})}else switch((s!==void 0||u.has(void 0))&&c(u.get(s)),m&&c(u.get(uo)),t){case"add":h?m&&c(u.get("length")):(c(u.get(Zr)),Kr(e)&&c(u.get(il)));break;case"delete":h||(c(u.get(Zr)),Kr(e)&&c(u.get(il)));break;case"set":Kr(e)&&c(u.get(Zr));break}}tl()}function Sn(e){const t=Me(e);return t===e?t:(Tt(t,"iterate",uo),Yt(e)?t:t.map(Ht))}function li(e){return Tt(e=Me(e),"iterate",uo),e}const av={__proto__:null,[Symbol.iterator](){return al(this,Symbol.iterator,Ht)},concat(...e){return Sn(this).concat(...e.map(t=>me(t)?Sn(t):t))},entries(){return al(this,"entries",e=>(e[1]=Ht(e[1]),e))},every(e,t){return rr(this,"every",e,t,void 0,arguments)},filter(e,t){return rr(this,"filter",e,t,s=>s.map(Ht),arguments)},find(e,t){return rr(this,"find",e,t,Ht,arguments)},findIndex(e,t){return rr(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return rr(this,"findLast",e,t,Ht,arguments)},findLastIndex(e,t){return rr(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return rr(this,"forEach",e,t,void 0,arguments)},includes(...e){return ll(this,"includes",e)},indexOf(...e){return ll(this,"indexOf",e)},join(e){return Sn(this).join(e)},lastIndexOf(...e){return ll(this,"lastIndexOf",e)},map(e,t){return rr(this,"map",e,t,void 0,arguments)},pop(){return co(this,"pop")},push(...e){return co(this,"push",e)},reduce(e,...t){return Yc(this,"reduce",e,t)},reduceRight(e,...t){return Yc(this,"reduceRight",e,t)},shift(){return co(this,"shift")},some(e,t){return rr(this,"some",e,t,void 0,arguments)},splice(...e){return co(this,"splice",e)},toReversed(){return Sn(this).toReversed()},toSorted(e){return Sn(this).toSorted(e)},toSpliced(...e){return Sn(this).toSpliced(...e)},unshift(...e){return co(this,"unshift",e)},values(){return al(this,"values",Ht)}};function al(e,t,s){const i=li(e),n=i[t]();return i!==e&&!Yt(e)&&(n._next=n.next,n.next=()=>{const a=n._next();return a.value&&(a.value=s(a.value)),a}),n}const lv=Array.prototype;function rr(e,t,s,i,n,a){const u=li(e),c=u!==e&&!Yt(e),h=u[t];if(h!==lv[t]){const v=h.apply(e,a);return c?Ht(v):v}let m=s;u!==e&&(c?m=function(v,w){return s.call(this,Ht(v),w,e)}:s.length>2&&(m=function(v,w){return s.call(this,v,w,e)}));const p=h.call(u,m,i);return c&&n?n(p):p}function Yc(e,t,s,i){const n=li(e);let a=s;return n!==e&&(Yt(e)?s.length>3&&(a=function(u,c,h){return s.call(this,u,c,h,e)}):a=function(u,c,h){return s.call(this,u,Ht(c),h,e)}),n[t](a,...i)}function ll(e,t,s){const i=Me(e);Tt(i,"iterate",uo);const n=i[t](...s);return(n===-1||n===!1)&&pi(s[0])?(s[0]=Me(s[0]),i[t](...s)):n}function co(e,t,s=[]){tr(),el();const i=Me(e)[t].apply(e,s);return tl(),sr(),i}const uv=er("__proto__,__v_isRef,__isVue"),Qc=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(As));function cv(e){As(e)||(e=String(e));const t=Me(this);return Tt(t,"has",e),t.hasOwnProperty(e)}class Zc{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,i){if(s==="__v_skip")return t.__v_skip;const n=this._isReadonly,a=this._isShallow;if(s==="__v_isReactive")return!n;if(s==="__v_isReadonly")return n;if(s==="__v_isShallow")return a;if(s==="__v_raw")return i===(n?a?nd:rd:a?sd:td).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(i)?t:void 0;const u=me(t);if(!n){let h;if(u&&(h=av[s]))return h;if(s==="hasOwnProperty")return cv}const c=Reflect.get(t,s,Dt(t)?t:i);return(As(s)?Qc.has(s):uv(s))||(n||Tt(t,"get",s),a)?c:Dt(c)?u&&Qa(s)?c:c.value:Ze(c)?n?id(c):fi(c):c}}class Jc extends Zc{constructor(t=!1){super(!1,t)}set(t,s,i,n){let a=t[s];if(!this._isShallow){const h=nr(a);if(!Yt(i)&&!nr(i)&&(a=Me(a),i=Me(i)),!me(t)&&Dt(a)&&!Dt(i))return h?!1:(a.value=i,!0)}const u=me(t)&&Qa(s)?Number(s)<t.length:Ye(t,s),c=Reflect.set(t,s,i,Dt(t)?t:n);return t===Me(n)&&(u?Sr(i,a)&&qs(t,"set",s,i,a):qs(t,"add",s,i)),c}deleteProperty(t,s){const i=Ye(t,s),n=t[s],a=Reflect.deleteProperty(t,s);return a&&i&&qs(t,"delete",s,void 0,n),a}has(t,s){const i=Reflect.has(t,s);return(!As(s)||!Qc.has(s))&&Tt(t,"has",s),i}ownKeys(t){return Tt(t,"iterate",me(t)?"length":Zr),Reflect.ownKeys(t)}}class Xc extends Zc{constructor(t=!1){super(!0,t)}set(t,s){return{}.NODE_ENV!=="production"&&Hs(`Set operation on key "${String(s)}" failed: target is readonly.`,t),!0}deleteProperty(t,s){return{}.NODE_ENV!=="production"&&Hs(`Delete operation on key "${String(s)}" failed: target is readonly.`,t),!0}}const dv=new Jc,fv=new Xc,hv=new Jc(!0),pv=new Xc(!0),ul=e=>e,ui=e=>Reflect.getPrototypeOf(e);function mv(e,t,s){return function(...i){const n=this.__v_raw,a=Me(n),u=Kr(a),c=e==="entries"||e===Symbol.iterator&&u,h=e==="keys"&&u,m=n[e](...i),p=s?ul:t?dl:Ht;return!t&&Tt(a,"iterate",h?il:Zr),{next(){const{value:v,done:w}=m.next();return w?{value:v,done:w}:{value:c?[p(v[0]),p(v[1])]:p(v),done:w}},[Symbol.iterator](){return this}}}}function ci(e){return function(...t){if({}.NODE_ENV!=="production"){const s=t[0]?`on key "${t[0]}" `:"";Hs(`${Yr(e)} operation ${s}failed: target is readonly.`,Me(this))}return e==="delete"?!1:e==="clear"?void 0:this}}function gv(e,t){const s={get(n){const a=this.__v_raw,u=Me(a),c=Me(n);e||(Sr(n,c)&&Tt(u,"get",n),Tt(u,"get",c));const{has:h}=ui(u),m=t?ul:e?dl:Ht;if(h.call(u,n))return m(a.get(n));if(h.call(u,c))return m(a.get(c));a!==u&&a.get(n)},get size(){const n=this.__v_raw;return!e&&Tt(Me(n),"iterate",Zr),Reflect.get(n,"size",n)},has(n){const a=this.__v_raw,u=Me(a),c=Me(n);return e||(Sr(n,c)&&Tt(u,"has",n),Tt(u,"has",c)),n===c?a.has(n):a.has(n)||a.has(c)},forEach(n,a){const u=this,c=u.__v_raw,h=Me(c),m=t?ul:e?dl:Ht;return!e&&Tt(h,"iterate",Zr),c.forEach((p,v)=>n.call(a,m(p),m(v),u))}};return pt(s,e?{add:ci("add"),set:ci("set"),delete:ci("delete"),clear:ci("clear")}:{add(n){!t&&!Yt(n)&&!nr(n)&&(n=Me(n));const a=Me(this);return ui(a).has.call(a,n)||(a.add(n),qs(a,"add",n,n)),this},set(n,a){!t&&!Yt(a)&&!nr(a)&&(a=Me(a));const u=Me(this),{has:c,get:h}=ui(u);let m=c.call(u,n);m?{}.NODE_ENV!=="production"&&ed(u,c,n):(n=Me(n),m=c.call(u,n));const p=h.call(u,n);return u.set(n,a),m?Sr(a,p)&&qs(u,"set",n,a,p):qs(u,"add",n,a),this},delete(n){const a=Me(this),{has:u,get:c}=ui(a);let h=u.call(a,n);h?{}.NODE_ENV!=="production"&&ed(a,u,n):(n=Me(n),h=u.call(a,n));const m=c?c.call(a,n):void 0,p=a.delete(n);return h&&qs(a,"delete",n,void 0,m),p},clear(){const n=Me(this),a=n.size!==0,u={}.NODE_ENV!=="production"?Kr(n)?new Map(n):new Set(n):void 0,c=n.clear();return a&&qs(n,"clear",void 0,void 0,u),c}}),["keys","values","entries",Symbol.iterator].forEach(n=>{s[n]=mv(n,e,t)}),s}function di(e,t){const s=gv(e,t);return(i,n,a)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?i:Reflect.get(Ye(s,n)&&n in i?s:i,n,a)}const vv={get:di(!1,!1)},_v={get:di(!1,!0)},yv={get:di(!0,!1)},bv={get:di(!0,!0)};function ed(e,t,s){const i=Me(s);if(i!==s&&t.call(e,i)){const n=Ya(e);Hs(`Reactive ${n} contains both the raw and reactive versions of the same object${n==="Map"?" as keys":""}, which can lead to inconsistencies. Avoid differentiating between the raw and reactive versions of an object and only use the reactive version if possible.`)}}const td=new WeakMap,sd=new WeakMap,rd=new WeakMap,nd=new WeakMap;function wv(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Ev(e){return e.__v_skip||!Object.isExtensible(e)?0:wv(Ya(e))}function fi(e){return nr(e)?e:hi(e,!1,dv,vv,td)}function od(e){return hi(e,!1,hv,_v,sd)}function id(e){return hi(e,!0,fv,yv,rd)}function zs(e){return hi(e,!0,pv,bv,nd)}function hi(e,t,s,i,n){if(!Ze(e))return{}.NODE_ENV!=="production"&&Hs(`value cannot be made ${t?"readonly":"reactive"}: ${String(e)}`),e;if(e.__v_raw&&!(t&&e.__v_isReactive))return e;const a=n.get(e);if(a)return a;const u=Ev(e);if(u===0)return e;const c=new Proxy(e,u===2?i:s);return n.set(e,c),c}function Jr(e){return nr(e)?Jr(e.__v_raw):!!(e&&e.__v_isReactive)}function nr(e){return!!(e&&e.__v_isReadonly)}function Yt(e){return!!(e&&e.__v_isShallow)}function pi(e){return e?!!e.__v_raw:!1}function Me(e){const t=e&&e.__v_raw;return t?Me(t):e}function cl(e){return!Ye(e,"__v_skip")&&Object.isExtensible(e)&&ii(e,"__v_skip",!0),e}const Ht=e=>Ze(e)?fi(e):e,dl=e=>Ze(e)?id(e):e;function Dt(e){return e?e.__v_isRef===!0:!1}function ad(e){return ld(e,!1)}function Cv(e){return ld(e,!0)}function ld(e,t){return Dt(e)?e:new Dv(e,t)}class Dv{constructor(t,s){this.dep=new nl,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:Me(t),this._value=s?t:Ht(t),this.__v_isShallow=s}get value(){return{}.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track(),this._value}set value(t){const s=this._rawValue,i=this.__v_isShallow||Yt(t)||nr(t);t=i?t:Me(t),Sr(t,s)&&(this._rawValue=t,this._value=i?t:Ht(t),{}.NODE_ENV!=="production"?this.dep.trigger({target:this,type:"set",key:"value",newValue:t,oldValue:s}):this.dep.trigger())}}function Tr(e){return Dt(e)?e.value:e}const xv={get:(e,t,s)=>t==="__v_raw"?e:Tr(Reflect.get(e,t,s)),set:(e,t,s,i)=>{const n=e[t];return Dt(n)&&!Dt(s)?(n.value=s,!0):Reflect.set(e,t,s,i)}};function ud(e){return Jr(e)?e:new Proxy(e,xv)}class Ov{constructor(t,s,i){this.fn=t,this.setter=s,this._value=void 0,this.dep=new nl(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=lo-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=i}notify(){if(this.flags|=16,!(this.flags&8)&&rt!==this)return jc(this,!0),!0}get value(){const t={}.NODE_ENV!=="production"?this.dep.track({target:this,type:"get",key:"value"}):this.dep.track();return zc(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter?this.setter(t):{}.NODE_ENV!=="production"&&Hs("Write operation failed: computed value is readonly")}}function Sv(e,t,s=!1){let i,n;xe(e)?i=e:(i=e.get,n=e.set);const a=new Ov(i,n,s);return{}.NODE_ENV!=="production"&&t&&!s&&(a.onTrack=t.onTrack,a.onTrigger=t.onTrigger),a}const mi={},gi=new WeakMap;let Xr;function Tv(e,t=!1,s=Xr){if(s){let i=gi.get(s);i||gi.set(s,i=[]),i.push(e)}else({}).NODE_ENV!=="production"&&!t&&Hs("onWatcherCleanup() was called when there was no active watcher to associate with.")}function Nv(e,t,s=st){const{immediate:i,deep:n,once:a,scheduler:u,augmentJob:c,call:h}=s,m=Z=>{(s.onWarn||Hs)("Invalid watch source: ",Z,"A watch source can only be a getter/effect function, a ref, a reactive object, or an array of these types.")},p=Z=>n?Z:Yt(Z)||n===!1||n===0?or(Z,1):or(Z);let v,w,D,V,F=!1,se=!1;if(Dt(e)?(w=()=>e.value,F=Yt(e)):Jr(e)?(w=()=>p(e),F=!0):me(e)?(se=!0,F=e.some(Z=>Jr(Z)||Yt(Z)),w=()=>e.map(Z=>{if(Dt(Z))return Z.value;if(Jr(Z))return p(Z);if(xe(Z))return h?h(Z,2):Z();({}).NODE_ENV!=="production"&&m(Z)})):xe(e)?t?w=h?()=>h(e,2):e:w=()=>{if(D){tr();try{D()}finally{sr()}}const Z=Xr;Xr=v;try{return h?h(e,3,[V]):e(V)}finally{Xr=Z}}:(w=St,{}.NODE_ENV!=="production"&&m(e)),t&&n){const Z=w,fe=n===!0?1/0:n;w=()=>or(Z(),fe)}const A=nv(),re=()=>{v.stop(),A&&A.active&&Ga(A.effects,v)};if(a&&t){const Z=t;t=(...fe)=>{Z(...fe),re()}}let Y=se?new Array(e.length).fill(mi):mi;const ye=Z=>{if(!(!(v.flags&1)||!v.dirty&&!Z))if(t){const fe=v.run();if(n||F||(se?fe.some((_e,Ae)=>Sr(_e,Y[Ae])):Sr(fe,Y))){D&&D();const _e=Xr;Xr=v;try{const Ae=[fe,Y===mi?void 0:se&&Y[0]===mi?[]:Y,V];h?h(t,3,Ae):t(...Ae),Y=fe}finally{Xr=_e}}}else v.run()};return c&&c(ye),v=new Bc(w),v.scheduler=u?()=>u(ye,!1):ye,V=Z=>Tv(Z,!1,v),D=v.onStop=()=>{const Z=gi.get(v);if(Z){if(h)h(Z,4);else for(const fe of Z)fe();gi.delete(v)}},{}.NODE_ENV!=="production"&&(v.onTrack=s.onTrack,v.onTrigger=s.onTrigger),t?i?ye(!0):Y=v.run():u?u(ye.bind(null,!0),!0):v.run(),re.pause=v.pause.bind(v),re.resume=v.resume.bind(v),re.stop=re,re}function or(e,t=1/0,s){if(t<=0||!Ze(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,Dt(e))or(e.value,t,s);else if(me(e))for(let i=0;i<e.length;i++)or(e[i],t,s);else if(xn(e)||Kr(e))e.forEach(i=>{or(i,t,s)});else if(kc(e)){for(const i in e)or(e[i],t,s);for(const i of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,i)&&or(e[i],t,s)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const en=[];function vi(e){en.push(e)}function _i(){en.pop()}let fl=!1;function Q(e,...t){if(fl)return;fl=!0,tr();const s=en.length?en[en.length-1].component:null,i=s&&s.appContext.config.warnHandler,n=Iv();if(i)Tn(i,s,11,[e+t.map(a=>{var u,c;return(c=(u=a.toString)==null?void 0:u.call(a))!=null?c:JSON.stringify(a)}).join(""),s&&s.proxy,n.map(({vnode:a})=>`at <${Li(s,a.type)}>`).join(`
`),n]);else{const a=[`[Vue warn]: ${e}`,...t];n.length&&a.push(`
`,...Av(n)),console.warn(...a)}sr(),fl=!1}function Iv(){let e=en[en.length-1];if(!e)return[];const t=[];for(;e;){const s=t[0];s&&s.vnode===e?s.recurseCount++:t.push({vnode:e,recurseCount:0});const i=e.component&&e.component.parent;e=i&&i.vnode}return t}function Av(e){const t=[];return e.forEach((s,i)=>{t.push(...i===0?[]:[`
`],...Mv(s))}),t}function Mv({vnode:e,recurseCount:t}){const s=t>0?`... (${t} recursive calls)`:"",i=e.component?e.component.parent==null:!1,n=` at <${Li(e.component,e.type,i)}`,a=">"+s;return e.props?[n,...Pv(e.props),a]:[n+a]}function Pv(e){const t=[],s=Object.keys(e);return s.slice(0,3).forEach(i=>{t.push(...cd(i,e[i]))}),s.length>3&&t.push(" ..."),t}function cd(e,t,s){return dt(t)?(t=JSON.stringify(t),s?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?s?t:[`${e}=${t}`]:Dt(t)?(t=cd(e,Me(t.value),!0),s?t:[`${e}=Ref<`,t,">"]):xe(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=Me(t),s?t:[`${e}=`,t])}function kv(e,t){({}).NODE_ENV!=="production"&&e!==void 0&&(typeof e!="number"?Q(`${t} is not a valid number - got ${JSON.stringify(e)}.`):isNaN(e)&&Q(`${t} is NaN - the duration expression might be incorrect.`))}const hl={sp:"serverPrefetch hook",bc:"beforeCreate hook",c:"created hook",bm:"beforeMount hook",m:"mounted hook",bu:"beforeUpdate hook",u:"updated",bum:"beforeUnmount hook",um:"unmounted hook",a:"activated hook",da:"deactivated hook",ec:"errorCaptured hook",rtc:"renderTracked hook",rtg:"renderTriggered hook",0:"setup function",1:"render function",2:"watcher getter",3:"watcher callback",4:"watcher cleanup function",5:"native event handler",6:"component event handler",7:"vnode hook",8:"directive hook",9:"transition hook",10:"app errorHandler",11:"app warnHandler",12:"ref function",13:"async component loader",14:"scheduler flush",15:"component update",16:"app unmount cleanup function"};function Tn(e,t,s,i){try{return i?e(...i):e()}catch(n){fo(n,t,s)}}function Ps(e,t,s,i){if(xe(e)){const n=Tn(e,t,s,i);return n&&Ka(n)&&n.catch(a=>{fo(a,t,s)}),n}if(me(e)){const n=[];for(let a=0;a<e.length;a++)n.push(Ps(e[a],t,s,i));return n}else({}).NODE_ENV!=="production"&&Q(`Invalid value type passed to callWithAsyncErrorHandling(): ${typeof e}`)}function fo(e,t,s,i=!0){const n=t?t.vnode:null,{errorHandler:a,throwUnhandledErrorInProduction:u}=t&&t.appContext.config||st;if(t){let c=t.parent;const h=t.proxy,m={}.NODE_ENV!=="production"?hl[s]:`https://vuejs.org/error-reference/#runtime-${s}`;for(;c;){const p=c.ec;if(p){for(let v=0;v<p.length;v++)if(p[v](e,h,m)===!1)return}c=c.parent}if(a){tr(),Tn(a,null,10,[e,h,m]),sr();return}}Vv(e,s,n,i,u)}function Vv(e,t,s,i=!0,n=!1){if({}.NODE_ENV!=="production"){const a=hl[t];if(s&&vi(s),Q(`Unhandled error${a?` during execution of ${a}`:""}`),s&&_i(),i)throw e;console.error(e)}else{if(n)throw e;console.error(e)}}const Qt=[];let Ws=-1;const Nn=[];let Nr=null,In=0;const dd=Promise.resolve();let yi=null;const Rv=100;function pl(e){const t=yi||dd;return e?t.then(this?e.bind(this):e):t}function Uv(e){let t=Ws+1,s=Qt.length;for(;t<s;){const i=t+s>>>1,n=Qt[i],a=ho(n);a<e||a===e&&n.flags&2?t=i+1:s=i}return t}function bi(e){if(!(e.flags&1)){const t=ho(e),s=Qt[Qt.length-1];!s||!(e.flags&2)&&t>=ho(s)?Qt.push(e):Qt.splice(Uv(t),0,e),e.flags|=1,fd()}}function fd(){yi||(yi=dd.then(gd))}function hd(e){me(e)?Nn.push(...e):Nr&&e.id===-1?Nr.splice(In+1,0,e):e.flags&1||(Nn.push(e),e.flags|=1),fd()}function pd(e,t,s=Ws+1){for({}.NODE_ENV!=="production"&&(t=t||new Map);s<Qt.length;s++){const i=Qt[s];if(i&&i.flags&2){if(e&&i.id!==e.uid||{}.NODE_ENV!=="production"&&ml(t,i))continue;Qt.splice(s,1),s--,i.flags&4&&(i.flags&=-2),i(),i.flags&4||(i.flags&=-2)}}}function md(e){if(Nn.length){const t=[...new Set(Nn)].sort((s,i)=>ho(s)-ho(i));if(Nn.length=0,Nr){Nr.push(...t);return}for(Nr=t,{}.NODE_ENV!=="production"&&(e=e||new Map),In=0;In<Nr.length;In++){const s=Nr[In];({}).NODE_ENV!=="production"&&ml(e,s)||(s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2)}Nr=null,In=0}}const ho=e=>e.id==null?e.flags&2?-1:1/0:e.id;function gd(e){({}).NODE_ENV!=="production"&&(e=e||new Map);const t={}.NODE_ENV!=="production"?s=>ml(e,s):St;try{for(Ws=0;Ws<Qt.length;Ws++){const s=Qt[Ws];if(s&&!(s.flags&8)){if({}.NODE_ENV!=="production"&&t(s))continue;s.flags&4&&(s.flags&=-2),Tn(s,s.i,s.i?15:14),s.flags&4||(s.flags&=-2)}}}finally{for(;Ws<Qt.length;Ws++){const s=Qt[Ws];s&&(s.flags&=-2)}Ws=-1,Qt.length=0,md(e),yi=null,(Qt.length||Nn.length)&&gd(e)}}function ml(e,t){const s=e.get(t)||0;if(s>Rv){const i=t.i,n=i&&$l(i.type);return fo(`Maximum recursive updates exceeded${n?` in component <${n}>`:""}. This means you have a reactive effect that is mutating its own dependencies and thus recursively triggering itself. Possible sources include component template, render function, updated hook or watcher source function.`,null,10),!0}return e.set(t,s+1),!1}let ks=!1;const wi=new Map;({}).NODE_ENV!=="production"&&(no().__VUE_HMR_RUNTIME__={createRecord:gl(vd),rerender:gl(Bv),reload:gl($v)});const tn=new Map;function Lv(e){const t=e.type.__hmrId;let s=tn.get(t);s||(vd(t,e.type),s=tn.get(t)),s.instances.add(e)}function Fv(e){tn.get(e.type.__hmrId).instances.delete(e)}function vd(e,t){return tn.has(e)?!1:(tn.set(e,{initialDef:Ei(t),instances:new Set}),!0)}function Ei(e){return If(e)?e.__vccOpts:e}function Bv(e,t){const s=tn.get(e);s&&(s.initialDef.render=t,[...s.instances].forEach(i=>{t&&(i.render=t,Ei(i.type).render=t),i.renderCache=[],ks=!0,i.update(),ks=!1}))}function $v(e,t){const s=tn.get(e);if(!s)return;t=Ei(t),_d(s.initialDef,t);const i=[...s.instances];for(let n=0;n<i.length;n++){const a=i[n],u=Ei(a.type);let c=wi.get(u);c||(u!==s.initialDef&&_d(u,t),wi.set(u,c=new Set)),c.add(a),a.appContext.propsCache.delete(a.type),a.appContext.emitsCache.delete(a.type),a.appContext.optionsCache.delete(a.type),a.ceReload?(c.add(a),a.ceReload(t.styles),c.delete(a)):a.parent?bi(()=>{ks=!0,a.parent.update(),ks=!1,c.delete(a)}):a.appContext.reload?a.appContext.reload():typeof window<"u"?window.location.reload():console.warn("[HMR] Root or manually mounted instance modified. Full reload required."),a.root.ce&&a!==a.root&&a.root.ce._removeChildStyle(u)}hd(()=>{wi.clear()})}function _d(e,t){pt(e,t);for(const s in e)s!=="__file"&&!(s in t)&&delete e[s]}function gl(e){return(t,s)=>{try{return e(t,s)}catch(i){console.error(i),console.warn("[HMR] Something went wrong during Vue component hot-reload. Full reload required.")}}}let Gs,po=[],vl=!1;function mo(e,...t){Gs?Gs.emit(e,...t):vl||po.push({event:e,args:t})}function yd(e,t){var s,i;Gs=e,Gs?(Gs.enabled=!0,po.forEach(({event:n,args:a})=>Gs.emit(n,...a)),po=[]):typeof window<"u"&&window.HTMLElement&&!((i=(s=window.navigator)==null?void 0:s.userAgent)!=null&&i.includes("jsdom"))?((t.__VUE_DEVTOOLS_HOOK_REPLAY__=t.__VUE_DEVTOOLS_HOOK_REPLAY__||[]).push(a=>{yd(a,t)}),setTimeout(()=>{Gs||(t.__VUE_DEVTOOLS_HOOK_REPLAY__=null,vl=!0,po=[])},3e3)):(vl=!0,po=[])}function jv(e,t){mo("app:init",e,t,{Fragment:Ie,Text:wo,Comment:wt,Static:Eo})}function Hv(e){mo("app:unmount",e)}const qv=_l("component:added"),bd=_l("component:updated"),zv=_l("component:removed"),Wv=e=>{Gs&&typeof Gs.cleanupBuffer=="function"&&!Gs.cleanupBuffer(e)&&zv(e)};/*! #__NO_SIDE_EFFECTS__ */function _l(e){return t=>{mo(e,t.appContext.app,t.uid,t.parent?t.parent.uid:void 0,t)}}const Gv=wd("perf:start"),Kv=wd("perf:end");function wd(e){return(t,s,i)=>{mo(e,t.appContext.app,t.uid,t,s,i)}}function Yv(e,t,s){mo("component:emit",e.appContext.app,e,t,s)}let xt=null,Ed=null;function Ci(e){const t=xt;return xt=e,Ed=e&&e.type.__scopeId||null,t}function Ne(e,t=xt,s){if(!t||e._n)return e;const i=(...n)=>{i._d&&bf(-1);const a=Ci(t);let u;try{u=e(...n)}finally{Ci(a),i._d&&bf(1)}return{}.NODE_ENV!=="production"&&bd(t),u};return i._n=!0,i._c=!0,i._d=!0,i}function Cd(e){$g(e)&&Q("Do not use built-in directive ids as custom directive id: "+e)}function ut(e,t){if(xt===null)return{}.NODE_ENV!=="production"&&Q("withDirectives can only be used inside render functions."),e;const s=Ui(xt),i=e.dirs||(e.dirs=[]);for(let n=0;n<t.length;n++){let[a,u,c,h=st]=t[n];a&&(xe(a)&&(a={mounted:a,updated:a}),a.deep&&or(u),i.push({dir:a,instance:s,value:u,oldValue:void 0,arg:c,modifiers:h}))}return e}function sn(e,t,s,i){const n=e.dirs,a=t&&t.dirs;for(let u=0;u<n.length;u++){const c=n[u];a&&(c.oldValue=a[u].value);let h=c.dir[i];h&&(tr(),Ps(h,s,8,[e.el,c,e,t]),sr())}}const Dd=Symbol("_vte"),xd=e=>e.__isTeleport,rn=e=>e&&(e.disabled||e.disabled===""),Od=e=>e&&(e.defer||e.defer===""),Sd=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Td=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,yl=(e,t)=>{const s=e&&e.to;if(dt(s))if(t){const i=t(s);return{}.NODE_ENV!=="production"&&!i&&!rn(e)&&Q(`Failed to locate Teleport target with selector "${s}". Note the target element must exist before the component is mounted - i.e. the target cannot be rendered by the component itself, and ideally should be outside of the entire Vue component tree.`),i}else return{}.NODE_ENV!=="production"&&Q("Current renderer does not support string target for Teleports. (missing querySelector renderer option)"),null;else return{}.NODE_ENV!=="production"&&!s&&!rn(e)&&Q(`Invalid Teleport target: ${s}`),s},Nd={name:"Teleport",__isTeleport:!0,process(e,t,s,i,n,a,u,c,h,m){const{mc:p,pc:v,pbc:w,o:{insert:D,querySelector:V,createText:F,createComment:se}}=m,A=rn(t.props);let{shapeFlag:re,children:Y,dynamicChildren:ye}=t;if({}.NODE_ENV!=="production"&&ks&&(h=!1,ye=null),e==null){const Z=t.el={}.NODE_ENV!=="production"?se("teleport start"):F(""),fe=t.anchor={}.NODE_ENV!=="production"?se("teleport end"):F("");D(Z,s,i),D(fe,s,i);const _e=(ae,I)=>{re&16&&(n&&n.isCE&&(n.ce._teleportTarget=ae),p(Y,ae,I,n,a,u,c,h))},Ae=()=>{const ae=t.target=yl(t.props,V),I=Id(ae,t,F,D);ae?(u!=="svg"&&Sd(ae)?u="svg":u!=="mathml"&&Td(ae)&&(u="mathml"),A||(_e(ae,I),xi(t,!1))):{}.NODE_ENV!=="production"&&!A&&Q("Invalid Teleport target on mount:",ae,`(${typeof ae})`)};A&&(_e(s,fe),xi(t,!0)),Od(t.props)?Jt(()=>{Ae(),t.el.__isMounted=!0},a):Ae()}else{if(Od(t.props)&&!e.el.__isMounted){Jt(()=>{Nd.process(e,t,s,i,n,a,u,c,h,m),delete e.el.__isMounted},a);return}t.el=e.el,t.targetStart=e.targetStart;const Z=t.anchor=e.anchor,fe=t.target=e.target,_e=t.targetAnchor=e.targetAnchor,Ae=rn(e.props),ae=Ae?s:fe,I=Ae?Z:_e;if(u==="svg"||Sd(fe)?u="svg":(u==="mathml"||Td(fe))&&(u="mathml"),ye?(w(e.dynamicChildren,ye,ae,n,a,u,c),bo(e,t,!0)):h||v(e,t,ae,I,n,a,u,c,!1),A)Ae?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):Di(t,s,Z,m,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const be=t.target=yl(t.props,V);be?Di(t,be,null,m,0):{}.NODE_ENV!=="production"&&Q("Invalid Teleport target on update:",fe,`(${typeof fe})`)}else Ae&&Di(t,fe,_e,m,1);xi(t,A)}},remove(e,t,s,{um:i,o:{remove:n}},a){const{shapeFlag:u,children:c,anchor:h,targetStart:m,targetAnchor:p,target:v,props:w}=e;if(v&&(n(m),n(p)),a&&n(h),u&16){const D=a||!rn(w);for(let V=0;V<c.length;V++){const F=c[V];i(F,t,s,D,!!F.dynamicChildren)}}},move:Di,hydrate:Qv};function Di(e,t,s,{o:{insert:i},m:n},a=2){a===0&&i(e.targetAnchor,t,s);const{el:u,anchor:c,shapeFlag:h,children:m,props:p}=e,v=a===2;if(v&&i(u,t,s),(!v||rn(p))&&h&16)for(let w=0;w<m.length;w++)n(m[w],t,s,2);v&&i(c,t,s)}function Qv(e,t,s,i,n,a,{o:{nextSibling:u,parentNode:c,querySelector:h,insert:m,createText:p}},v){const w=t.target=yl(t.props,h);if(w){const D=rn(t.props),V=w._lpa||w.firstChild;if(t.shapeFlag&16)if(D)t.anchor=v(u(e),t,c(e),s,i,n,a),t.targetStart=V,t.targetAnchor=V&&u(V);else{t.anchor=u(e);let F=V;for(;F;){if(F&&F.nodeType===8){if(F.data==="teleport start anchor")t.targetStart=F;else if(F.data==="teleport anchor"){t.targetAnchor=F,w._lpa=t.targetAnchor&&u(t.targetAnchor);break}}F=u(F)}t.targetAnchor||Id(w,t,p,m),v(V&&u(V),t,w,s,i,n,a)}xi(t,D)}return t.anchor&&u(t.anchor)}const Zv=Nd;function xi(e,t){const s=e.ctx;if(s&&s.ut){let i,n;for(t?(i=e.el,n=e.anchor):(i=e.targetStart,n=e.targetAnchor);i&&i!==n;)i.nodeType===1&&i.setAttribute("data-v-owner",s.uid),i=i.nextSibling;s.ut()}}function Id(e,t,s,i){const n=t.targetStart=s(""),a=t.targetAnchor=s("");return n[Dd]=a,e&&(i(n,e),i(a,e)),a}const Ir=Symbol("_leaveCb"),Oi=Symbol("_enterCb");function Jv(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return Bd(()=>{e.isMounted=!0}),$d(()=>{e.isUnmounting=!0}),e}const ws=[Function,Array],Ad={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:ws,onEnter:ws,onAfterEnter:ws,onEnterCancelled:ws,onBeforeLeave:ws,onLeave:ws,onAfterLeave:ws,onLeaveCancelled:ws,onBeforeAppear:ws,onAppear:ws,onAfterAppear:ws,onAppearCancelled:ws},Md=e=>{const t=e.subTree;return t.component?Md(t.component):t},Xv={name:"BaseTransition",props:Ad,setup(e,{slots:t}){const s=Vi(),i=Jv();return()=>{const n=t.default&&Rd(t.default(),!0);if(!n||!n.length)return;const a=Pd(n),u=Me(e),{mode:c}=u;if({}.NODE_ENV!=="production"&&c&&c!=="in-out"&&c!=="out-in"&&c!=="default"&&Q(`invalid <transition> mode: ${c}`),i.isLeaving)return wl(a);const h=Vd(a);if(!h)return wl(a);let m=bl(h,u,i,s,v=>m=v);h.type!==wt&&go(h,m);let p=s.subTree&&Vd(s.subTree);if(p&&p.type!==wt&&!ln(h,p)&&Md(s).type!==wt){let v=bl(p,u,i,s);if(go(p,v),c==="out-in"&&h.type!==wt)return i.isLeaving=!0,v.afterLeave=()=>{i.isLeaving=!1,s.job.flags&8||s.update(),delete v.afterLeave,p=void 0},wl(a);c==="in-out"&&h.type!==wt?v.delayLeave=(w,D,V)=>{const F=kd(i,p);F[String(p.key)]=p,w[Ir]=()=>{D(),w[Ir]=void 0,delete m.delayedLeave,p=void 0},m.delayedLeave=()=>{V(),delete m.delayedLeave,p=void 0}}:p=void 0}else p&&(p=void 0);return a}}};function Pd(e){let t=e[0];if(e.length>1){let s=!1;for(const i of e)if(i.type!==wt){if({}.NODE_ENV!=="production"&&s){Q("<transition> can only be used on a single element or component. Use <transition-group> for lists.");break}if(t=i,s=!0,{}.NODE_ENV==="production")break}}return t}const e_=Xv;function kd(e,t){const{leavingVNodes:s}=e;let i=s.get(t.type);return i||(i=Object.create(null),s.set(t.type,i)),i}function bl(e,t,s,i,n){const{appear:a,mode:u,persisted:c=!1,onBeforeEnter:h,onEnter:m,onAfterEnter:p,onEnterCancelled:v,onBeforeLeave:w,onLeave:D,onAfterLeave:V,onLeaveCancelled:F,onBeforeAppear:se,onAppear:A,onAfterAppear:re,onAppearCancelled:Y}=t,ye=String(e.key),Z=kd(s,e),fe=(ae,I)=>{ae&&Ps(ae,i,9,I)},_e=(ae,I)=>{const be=I[1];fe(ae,I),me(ae)?ae.every(ue=>ue.length<=1)&&be():ae.length<=1&&be()},Ae={mode:u,persisted:c,beforeEnter(ae){let I=h;if(!s.isMounted)if(a)I=se||h;else return;ae[Ir]&&ae[Ir](!0);const be=Z[ye];be&&ln(e,be)&&be.el[Ir]&&be.el[Ir](),fe(I,[ae])},enter(ae){let I=m,be=p,ue=v;if(!s.isMounted)if(a)I=A||m,be=re||p,ue=Y||v;else return;let Ge=!1;const vt=ae[Oi]=mt=>{Ge||(Ge=!0,mt?fe(ue,[ae]):fe(be,[ae]),Ae.delayedLeave&&Ae.delayedLeave(),ae[Oi]=void 0)};I?_e(I,[ae,vt]):vt()},leave(ae,I){const be=String(e.key);if(ae[Oi]&&ae[Oi](!0),s.isUnmounting)return I();fe(w,[ae]);let ue=!1;const Ge=ae[Ir]=vt=>{ue||(ue=!0,I(),vt?fe(F,[ae]):fe(V,[ae]),ae[Ir]=void 0,Z[be]===e&&delete Z[be])};Z[be]=e,D?_e(D,[ae,Ge]):Ge()},clone(ae){const I=bl(ae,t,s,i,n);return n&&n(I),I}};return Ae}function wl(e){if(vo(e))return e=Ks(e),e.children=null,e}function Vd(e){if(!vo(e))return xd(e.type)&&e.children?Pd(e.children):e;if({}.NODE_ENV!=="production"&&e.component)return e.component.subTree;const{shapeFlag:t,children:s}=e;if(s){if(t&16)return s[0];if(t&32&&xe(s.default))return s.default()}}function go(e,t){e.shapeFlag&6&&e.component?(e.transition=t,go(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Rd(e,t=!1,s){let i=[],n=0;for(let a=0;a<e.length;a++){let u=e[a];const c=s==null?u.key:String(s)+String(u.key!=null?u.key:a);u.type===Ie?(u.patchFlag&128&&n++,i=i.concat(Rd(u.children,t,c))):(t||u.type!==wt)&&i.push(c!=null?Ks(u,{key:c}):u)}if(n>1)for(let a=0;a<i.length;a++)i[a].patchFlag=-2;return i}/*! #__NO_SIDE_EFFECTS__ */function Ud(e,t){return xe(e)?(()=>pt({name:e.name},t,{setup:e}))():e}function Ld(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}const t_=new WeakSet;function Si(e,t,s,i,n=!1){if(me(e)){e.forEach((V,F)=>Si(V,t&&(me(t)?t[F]:t),s,i,n));return}if(An(i)&&!n){i.shapeFlag&512&&i.type.__asyncResolved&&i.component.subTree.component&&Si(e,t,s,i.component.subTree);return}const a=i.shapeFlag&4?Ui(i.component):i.el,u=n?null:a,{i:c,r:h}=e;if({}.NODE_ENV!=="production"&&!c){Q("Missing ref owner context. ref cannot be used on hoisted vnodes. A vnode with ref must be created inside the render function.");return}const m=t&&t.r,p=c.refs===st?c.refs={}:c.refs,v=c.setupState,w=Me(v),D=v===st?()=>!1:V=>({}).NODE_ENV!=="production"&&(Ye(w,V)&&!Dt(w[V])&&Q(`Template ref "${V}" used on a non-ref value. It will not work in the production build.`),t_.has(w[V]))?!1:Ye(w,V);if(m!=null&&m!==h&&(dt(m)?(p[m]=null,D(m)&&(v[m]=null)):Dt(m)&&(m.value=null)),xe(h))Tn(h,c,12,[u,p]);else{const V=dt(h),F=Dt(h);if(V||F){const se=()=>{if(e.f){const A=V?D(h)?v[h]:p[h]:h.value;n?me(A)&&Ga(A,a):me(A)?A.includes(a)||A.push(a):V?(p[h]=[a],D(h)&&(v[h]=p[h])):(h.value=[a],e.k&&(p[e.k]=h.value))}else V?(p[h]=u,D(h)&&(v[h]=u)):F?(h.value=u,e.k&&(p[e.k]=u)):{}.NODE_ENV!=="production"&&Q("Invalid template ref type:",h,`(${typeof h})`)};u?(se.id=-1,Jt(se,s)):se()}else({}).NODE_ENV!=="production"&&Q("Invalid template ref type:",h,`(${typeof h})`)}}no().requestIdleCallback,no().cancelIdleCallback;const An=e=>!!e.type.__asyncLoader,vo=e=>e.type.__isKeepAlive;function s_(e,t){Fd(e,"a",t)}function r_(e,t){Fd(e,"da",t)}function Fd(e,t,s=Nt){const i=e.__wdc||(e.__wdc=()=>{let n=s;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(Ti(t,i,s),s){let n=s.parent;for(;n&&n.parent;)vo(n.parent.vnode)&&n_(i,t,s,n),n=n.parent}}function n_(e,t,s,i){const n=Ti(t,e,i,!0);jd(()=>{Ga(i[t],n)},s)}function Ti(e,t,s=Nt,i=!1){if(s){const n=s[e]||(s[e]=[]),a=t.__weh||(t.__weh=(...u)=>{tr();const c=xo(s),h=Ps(t,s,e,u);return c(),sr(),h});return i?n.unshift(a):n.push(a),a}else if({}.NODE_ENV!=="production"){const n=Qr(hl[e].replace(/ hook$/,""));Q(`${n} is called when there is no active component instance to be associated with. Lifecycle injection APIs can only be used during execution of setup(). If you are using async setup(), make sure to register lifecycle hooks before the first await statement.`)}}const ir=e=>(t,s=Nt)=>{(!Oo||e==="sp")&&Ti(e,(...i)=>t(...i),s)},o_=ir("bm"),Bd=ir("m"),i_=ir("bu"),a_=ir("u"),$d=ir("bum"),jd=ir("um"),l_=ir("sp"),u_=ir("rtg"),c_=ir("rtc");function d_(e,t=Nt){Ti("ec",e,t)}const El="components",f_="directives";function te(e,t){return Hd(El,e,!0,t)||e}const h_=Symbol.for("v-ndc");function p_(e){return Hd(f_,e)}function Hd(e,t,s=!0,i=!1){const n=xt||Nt;if(n){const a=n.type;if(e===El){const c=$l(a,!1);if(c&&(c===t||c===Kt(t)||c===Yr(Kt(t))))return a}const u=qd(n[e]||a[e],t)||qd(n.appContext[e],t);if(!u&&i)return a;if({}.NODE_ENV!=="production"&&s&&!u){const c=e===El?`
If this is a native custom element, make sure to exclude it from component resolution via compilerOptions.isCustomElement.`:"";Q(`Failed to resolve ${e.slice(0,-1)}: ${t}${c}`)}return u}else({}).NODE_ENV!=="production"&&Q(`resolve${Yr(e.slice(0,-1))} can only be used in render() or setup().`)}function qd(e,t){return e&&(e[t]||e[Kt(t)]||e[Yr(Kt(t))])}function at(e,t,s,i){let n;const a=s&&s[i],u=me(e);if(u||dt(e)){const c=u&&Jr(e);let h=!1;c&&(h=!Yt(e),e=li(e)),n=new Array(e.length);for(let m=0,p=e.length;m<p;m++)n[m]=t(h?Ht(e[m]):e[m],m,void 0,a&&a[m])}else if(typeof e=="number"){({}).NODE_ENV!=="production"&&!Number.isInteger(e)&&Q(`The v-for range expect an integer value but got ${e}.`),n=new Array(e);for(let c=0;c<e;c++)n[c]=t(c+1,c,void 0,a&&a[c])}else if(Ze(e))if(e[Symbol.iterator])n=Array.from(e,(c,h)=>t(c,h,void 0,a&&a[h]));else{const c=Object.keys(e);n=new Array(c.length);for(let h=0,m=c.length;h<m;h++){const p=c[h];n[h]=t(e[p],p,h,a&&a[h])}}else n=[];return s&&(s[i]=n),n}function Vt(e,t,s={},i,n){if(xt.ce||xt.parent&&An(xt.parent)&&xt.parent.ce)return t!=="default"&&(s.name=t),O(),Rt(Ie,null,[k("slot",s,i&&i())],64);let a=e[t];({}).NODE_ENV!=="production"&&a&&a.length>1&&(Q("SSR-optimized slot function detected in a non-SSR-optimized render function. You need to mark this component with $dynamic-slots in the parent template."),a=()=>[]),a&&a._c&&(a._d=!1),O();const u=a&&zd(a(s)),c=s.key||u&&u.key,h=Rt(Ie,{key:(c&&!As(c)?c:`_${t}`)+(!u&&i?"_fb":"")},u||(i?i():[]),u&&e._===1?64:-2);return!n&&h.scopeId&&(h.slotScopeIds=[h.scopeId+"-s"]),a&&a._c&&(a._d=!0),h}function zd(e){return e.some(t=>an(t)?!(t.type===wt||t.type===Ie&&!zd(t.children)):!0)?e:null}const Cl=e=>e?Of(e)?Ui(e):Cl(e.parent):null,nn=pt(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>({}).NODE_ENV!=="production"?zs(e.props):e.props,$attrs:e=>({}).NODE_ENV!=="production"?zs(e.attrs):e.attrs,$slots:e=>({}).NODE_ENV!=="production"?zs(e.slots):e.slots,$refs:e=>({}).NODE_ENV!=="production"?zs(e.refs):e.refs,$parent:e=>Cl(e.parent),$root:e=>Cl(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Sl(e),$forceUpdate:e=>e.f||(e.f=()=>{bi(e.update)}),$nextTick:e=>e.n||(e.n=pl.bind(e.proxy)),$watch:e=>G_.bind(e)}),Dl=e=>e==="_"||e==="$",xl=(e,t)=>e!==st&&!e.__isScriptSetup&&Ye(e,t),Wd={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:i,data:n,props:a,accessCache:u,type:c,appContext:h}=e;if({}.NODE_ENV!=="production"&&t==="__isVue")return!0;let m;if(t[0]!=="$"){const D=u[t];if(D!==void 0)switch(D){case 1:return i[t];case 2:return n[t];case 4:return s[t];case 3:return a[t]}else{if(xl(i,t))return u[t]=1,i[t];if(n!==st&&Ye(n,t))return u[t]=2,n[t];if((m=e.propsOptions[0])&&Ye(m,t))return u[t]=3,a[t];if(s!==st&&Ye(s,t))return u[t]=4,s[t];Ol&&(u[t]=0)}}const p=nn[t];let v,w;if(p)return t==="$attrs"?(Tt(e.attrs,"get",""),{}.NODE_ENV!=="production"&&Pi()):{}.NODE_ENV!=="production"&&t==="$slots"&&Tt(e,"get",t),p(e);if((v=c.__cssModules)&&(v=v[t]))return v;if(s!==st&&Ye(s,t))return u[t]=4,s[t];if(w=h.config.globalProperties,Ye(w,t))return w[t];({}).NODE_ENV!=="production"&&xt&&(!dt(t)||t.indexOf("__v")!==0)&&(n!==st&&Dl(t[0])&&Ye(n,t)?Q(`Property ${JSON.stringify(t)} must be accessed via $data because it starts with a reserved character ("$" or "_") and is not proxied on the render context.`):e===xt&&Q(`Property ${JSON.stringify(t)} was accessed during render but is not defined on instance.`))},set({_:e},t,s){const{data:i,setupState:n,ctx:a}=e;return xl(n,t)?(n[t]=s,!0):{}.NODE_ENV!=="production"&&n.__isScriptSetup&&Ye(n,t)?(Q(`Cannot mutate <script setup> binding "${t}" from Options API.`),!1):i!==st&&Ye(i,t)?(i[t]=s,!0):Ye(e.props,t)?({}.NODE_ENV!=="production"&&Q(`Attempting to mutate prop "${t}". Props are readonly.`),!1):t[0]==="$"&&t.slice(1)in e?({}.NODE_ENV!=="production"&&Q(`Attempting to mutate public property "${t}". Properties starting with $ are reserved and readonly.`),!1):({}.NODE_ENV!=="production"&&t in e.appContext.config.globalProperties?Object.defineProperty(a,t,{enumerable:!0,configurable:!0,value:s}):a[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:i,appContext:n,propsOptions:a}},u){let c;return!!s[u]||e!==st&&Ye(e,u)||xl(t,u)||(c=a[0])&&Ye(c,u)||Ye(i,u)||Ye(nn,u)||Ye(n.config.globalProperties,u)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:Ye(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};({}).NODE_ENV!=="production"&&(Wd.ownKeys=e=>(Q("Avoid app logic that relies on enumerating keys on a component instance. The keys will be empty in production mode to avoid performance overhead."),Reflect.ownKeys(e)));function m_(e){const t={};return Object.defineProperty(t,"_",{configurable:!0,enumerable:!1,get:()=>e}),Object.keys(nn).forEach(s=>{Object.defineProperty(t,s,{configurable:!0,enumerable:!1,get:()=>nn[s](e),set:St})}),t}function g_(e){const{ctx:t,propsOptions:[s]}=e;s&&Object.keys(s).forEach(i=>{Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>e.props[i],set:St})})}function v_(e){const{ctx:t,setupState:s}=e;Object.keys(Me(s)).forEach(i=>{if(!s.__isScriptSetup){if(Dl(i[0])){Q(`setup() return property ${JSON.stringify(i)} should not start with "$" or "_" which are reserved prefixes for Vue internals.`);return}Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>s[i],set:St})}})}function Gd(e){return me(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}function __(){const e=Object.create(null);return(t,s)=>{e[s]?Q(`${t} property "${s}" is already defined in ${e[s]}.`):e[s]=t}}let Ol=!0;function y_(e){const t=Sl(e),s=e.proxy,i=e.ctx;Ol=!1,t.beforeCreate&&Kd(t.beforeCreate,e,"bc");const{data:n,computed:a,methods:u,watch:c,provide:h,inject:m,created:p,beforeMount:v,mounted:w,beforeUpdate:D,updated:V,activated:F,deactivated:se,beforeDestroy:A,beforeUnmount:re,destroyed:Y,unmounted:ye,render:Z,renderTracked:fe,renderTriggered:_e,errorCaptured:Ae,serverPrefetch:ae,expose:I,inheritAttrs:be,components:ue,directives:Ge,filters:vt}=t,mt={}.NODE_ENV!=="production"?__():null;if({}.NODE_ENV!=="production"){const[Oe]=e.propsOptions;if(Oe)for(const we in Oe)mt("Props",we)}if(m&&b_(m,i,mt),u)for(const Oe in u){const we=u[Oe];xe(we)?({}.NODE_ENV!=="production"?Object.defineProperty(i,Oe,{value:we.bind(s),configurable:!0,enumerable:!0,writable:!0}):i[Oe]=we.bind(s),{}.NODE_ENV!=="production"&&mt("Methods",Oe)):{}.NODE_ENV!=="production"&&Q(`Method "${Oe}" has type "${typeof we}" in the component definition. Did you reference the function correctly?`)}if(n){({}).NODE_ENV!=="production"&&!xe(n)&&Q("The data option must be a function. Plain object usage is no longer supported.");const Oe=n.call(s,s);if({}.NODE_ENV!=="production"&&Ka(Oe)&&Q("data() returned a Promise - note data() cannot be async; If you intend to perform data fetching before component renders, use async setup() + <Suspense>."),!Ze(Oe))({}).NODE_ENV!=="production"&&Q("data() should return an object.");else if(e.data=fi(Oe),{}.NODE_ENV!=="production")for(const we in Oe)mt("Data",we),Dl(we[0])||Object.defineProperty(i,we,{configurable:!0,enumerable:!0,get:()=>Oe[we],set:St})}if(Ol=!0,a)for(const Oe in a){const we=a[Oe],Lt=xe(we)?we.bind(s,s):xe(we.get)?we.get.bind(s,s):St;({}).NODE_ENV!=="production"&&Lt===St&&Q(`Computed property "${Oe}" has no getter.`);const es=!xe(we)&&xe(we.set)?we.set.bind(s):{}.NODE_ENV!=="production"?()=>{Q(`Write operation failed: computed property "${Oe}" is readonly.`)}:St,yt=Us({get:Lt,set:es});Object.defineProperty(i,Oe,{enumerable:!0,configurable:!0,get:()=>yt.value,set:ce=>yt.value=ce}),{}.NODE_ENV!=="production"&&mt("Computed",Oe)}if(c)for(const Oe in c)Yd(c[Oe],i,s,Oe);if(h){const Oe=xe(h)?h.call(s):h;Reflect.ownKeys(Oe).forEach(we=>{Ii(we,Oe[we])})}p&&Kd(p,e,"c");function ft(Oe,we){me(we)?we.forEach(Lt=>Oe(Lt.bind(s))):we&&Oe(we.bind(s))}if(ft(o_,v),ft(Bd,w),ft(i_,D),ft(a_,V),ft(s_,F),ft(r_,se),ft(d_,Ae),ft(c_,fe),ft(u_,_e),ft($d,re),ft(jd,ye),ft(l_,ae),me(I))if(I.length){const Oe=e.exposed||(e.exposed={});I.forEach(we=>{Object.defineProperty(Oe,we,{get:()=>s[we],set:Lt=>s[we]=Lt})})}else e.exposed||(e.exposed={});Z&&e.render===St&&(e.render=Z),be!=null&&(e.inheritAttrs=be),ue&&(e.components=ue),Ge&&(e.directives=Ge),ae&&Ld(e)}function b_(e,t,s=St){me(e)&&(e=Tl(e));for(const i in e){const n=e[i];let a;Ze(n)?"default"in n?a=Vs(n.from||i,n.default,!0):a=Vs(n.from||i):a=Vs(n),Dt(a)?Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>a.value,set:u=>a.value=u}):t[i]=a,{}.NODE_ENV!=="production"&&s("Inject",i)}}function Kd(e,t,s){Ps(me(e)?e.map(i=>i.bind(t.proxy)):e.bind(t.proxy),t,s)}function Yd(e,t,s,i){let n=i.includes(".")?pf(s,i):()=>s[i];if(dt(e)){const a=t[e];xe(a)?Pn(n,a):{}.NODE_ENV!=="production"&&Q(`Invalid watch handler specified by key "${e}"`,a)}else if(xe(e))Pn(n,e.bind(s));else if(Ze(e))if(me(e))e.forEach(a=>Yd(a,t,s,i));else{const a=xe(e.handler)?e.handler.bind(s):t[e.handler];xe(a)?Pn(n,a,e):{}.NODE_ENV!=="production"&&Q(`Invalid watch handler specified by key "${e.handler}"`,a)}else({}).NODE_ENV!=="production"&&Q(`Invalid watch option: "${i}"`,e)}function Sl(e){const t=e.type,{mixins:s,extends:i}=t,{mixins:n,optionsCache:a,config:{optionMergeStrategies:u}}=e.appContext,c=a.get(t);let h;return c?h=c:!n.length&&!s&&!i?h=t:(h={},n.length&&n.forEach(m=>Ni(h,m,u,!0)),Ni(h,t,u)),Ze(t)&&a.set(t,h),h}function Ni(e,t,s,i=!1){const{mixins:n,extends:a}=t;a&&Ni(e,a,s,!0),n&&n.forEach(u=>Ni(e,u,s,!0));for(const u in t)if(i&&u==="expose")({}).NODE_ENV!=="production"&&Q('"expose" option is ignored when declared in mixins or extends. It should only be declared in the base component itself.');else{const c=w_[u]||s&&s[u];e[u]=c?c(e[u],t[u]):t[u]}return e}const w_={data:Qd,props:Zd,emits:Zd,methods:_o,computed:_o,beforeCreate:Zt,created:Zt,beforeMount:Zt,mounted:Zt,beforeUpdate:Zt,updated:Zt,beforeDestroy:Zt,beforeUnmount:Zt,destroyed:Zt,unmounted:Zt,activated:Zt,deactivated:Zt,errorCaptured:Zt,serverPrefetch:Zt,components:_o,directives:_o,watch:C_,provide:Qd,inject:E_};function Qd(e,t){return t?e?function(){return pt(xe(e)?e.call(this,this):e,xe(t)?t.call(this,this):t)}:t:e}function E_(e,t){return _o(Tl(e),Tl(t))}function Tl(e){if(me(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Zt(e,t){return e?[...new Set([].concat(e,t))]:t}function _o(e,t){return e?pt(Object.create(null),e,t):t}function Zd(e,t){return e?me(e)&&me(t)?[...new Set([...e,...t])]:pt(Object.create(null),Gd(e),Gd(t??{})):t}function C_(e,t){if(!e)return t;if(!t)return e;const s=pt(Object.create(null),e);for(const i in t)s[i]=Zt(e[i],t[i]);return s}function Jd(){return{app:null,config:{isNativeTag:Fg,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let D_=0;function x_(e,t){return function(i,n=null){xe(i)||(i=pt({},i)),n!=null&&!Ze(n)&&({}.NODE_ENV!=="production"&&Q("root props passed to app.mount() must be an object."),n=null);const a=Jd(),u=new WeakSet,c=[];let h=!1;const m=a.app={_uid:D_++,_component:i,_props:n,_container:null,_context:a,_instance:null,version:Af,get config(){return a.config},set config(p){({}).NODE_ENV!=="production"&&Q("app.config cannot be replaced. Modify individual options instead.")},use(p,...v){return u.has(p)?{}.NODE_ENV!=="production"&&Q("Plugin has already been applied to target app."):p&&xe(p.install)?(u.add(p),p.install(m,...v)):xe(p)?(u.add(p),p(m,...v)):{}.NODE_ENV!=="production"&&Q('A plugin must either be a function or an object with an "install" function.'),m},mixin(p){return a.mixins.includes(p)?{}.NODE_ENV!=="production"&&Q("Mixin has already been applied to target app"+(p.name?`: ${p.name}`:"")):a.mixins.push(p),m},component(p,v){return{}.NODE_ENV!=="production"&&Fl(p,a.config),v?({}.NODE_ENV!=="production"&&a.components[p]&&Q(`Component "${p}" has already been registered in target app.`),a.components[p]=v,m):a.components[p]},directive(p,v){return{}.NODE_ENV!=="production"&&Cd(p),v?({}.NODE_ENV!=="production"&&a.directives[p]&&Q(`Directive "${p}" has already been registered in target app.`),a.directives[p]=v,m):a.directives[p]},mount(p,v,w){if(h)({}).NODE_ENV!=="production"&&Q("App has already been mounted.\nIf you want to remount the same app, move your app creation logic into a factory function and create fresh app instances for each mount - e.g. `const createMyApp = () => createApp(App)`");else{({}).NODE_ENV!=="production"&&p.__vue_app__&&Q("There is already an app instance mounted on the host container.\n If you want to mount another app on the same host container, you need to unmount the previous app by calling `app.unmount()` first.");const D=m._ceVNode||k(i,n);return D.appContext=a,w===!0?w="svg":w===!1&&(w=void 0),{}.NODE_ENV!=="production"&&(a.reload=()=>{e(Ks(D),p,w)}),v&&t?t(D,p):e(D,p,w),h=!0,m._container=p,p.__vue_app__=m,{}.NODE_ENV!=="production"&&(m._instance=D.component,jv(m,Af)),Ui(D.component)}},onUnmount(p){({}).NODE_ENV!=="production"&&typeof p!="function"&&Q(`Expected function as first argument to app.onUnmount(), but got ${typeof p}`),c.push(p)},unmount(){h?(Ps(c,m._instance,16),e(null,m._container),{}.NODE_ENV!=="production"&&(m._instance=null,Hv(m)),delete m._container.__vue_app__):{}.NODE_ENV!=="production"&&Q("Cannot unmount an app that is not mounted.")},provide(p,v){return{}.NODE_ENV!=="production"&&p in a.provides&&Q(`App already provides property with key "${String(p)}". It will be overwritten with the new value.`),a.provides[p]=v,m},runWithContext(p){const v=Mn;Mn=m;try{return p()}finally{Mn=v}}};return m}}let Mn=null;function Ii(e,t){if(!Nt)({}).NODE_ENV!=="production"&&Q("provide() can only be used inside setup().");else{let s=Nt.provides;const i=Nt.parent&&Nt.parent.provides;i===s&&(s=Nt.provides=Object.create(i)),s[e]=t}}function Vs(e,t,s=!1){const i=Nt||xt;if(i||Mn){const n=Mn?Mn._context.provides:i?i.parent==null?i.vnode.appContext&&i.vnode.appContext.provides:i.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return s&&xe(t)?t.call(i&&i.proxy):t;({}).NODE_ENV!=="production"&&Q(`injection "${String(e)}" not found.`)}else({}).NODE_ENV!=="production"&&Q("inject() can only be used inside setup() or functional components.")}const Xd={},ef=()=>Object.create(Xd),tf=e=>Object.getPrototypeOf(e)===Xd;function O_(e,t,s,i=!1){const n={},a=ef();e.propsDefaults=Object.create(null),sf(e,t,n,a);for(const u in e.propsOptions[0])u in n||(n[u]=void 0);({}).NODE_ENV!=="production"&&of(t||{},n,e),s?e.props=i?n:od(n):e.type.props?e.props=n:e.props=a,e.attrs=a}function S_(e){for(;e;){if(e.type.__hmrId)return!0;e=e.parent}}function T_(e,t,s,i){const{props:n,attrs:a,vnode:{patchFlag:u}}=e,c=Me(n),[h]=e.propsOptions;let m=!1;if(!({}.NODE_ENV!=="production"&&S_(e))&&(i||u>0)&&!(u&16)){if(u&8){const p=e.vnode.dynamicProps;for(let v=0;v<p.length;v++){let w=p[v];if(Mi(e.emitsOptions,w))continue;const D=t[w];if(h)if(Ye(a,w))D!==a[w]&&(a[w]=D,m=!0);else{const V=Kt(w);n[V]=Nl(h,c,V,D,e,!1)}else D!==a[w]&&(a[w]=D,m=!0)}}}else{sf(e,t,n,a)&&(m=!0);let p;for(const v in c)(!t||!Ye(t,v)&&((p=Or(v))===v||!Ye(t,p)))&&(h?s&&(s[v]!==void 0||s[p]!==void 0)&&(n[v]=Nl(h,c,v,void 0,e,!0)):delete n[v]);if(a!==c)for(const v in a)(!t||!Ye(t,v))&&(delete a[v],m=!0)}m&&qs(e.attrs,"set",""),{}.NODE_ENV!=="production"&&of(t||{},n,e)}function sf(e,t,s,i){const[n,a]=e.propsOptions;let u=!1,c;if(t)for(let h in t){if(ro(h))continue;const m=t[h];let p;n&&Ye(n,p=Kt(h))?!a||!a.includes(p)?s[p]=m:(c||(c={}))[p]=m:Mi(e.emitsOptions,h)||(!(h in i)||m!==i[h])&&(i[h]=m,u=!0)}if(a){const h=Me(s),m=c||st;for(let p=0;p<a.length;p++){const v=a[p];s[v]=Nl(n,h,v,m[v],e,!Ye(m,v))}}return u}function Nl(e,t,s,i,n,a){const u=e[s];if(u!=null){const c=Ye(u,"default");if(c&&i===void 0){const h=u.default;if(u.type!==Function&&!u.skipFactory&&xe(h)){const{propsDefaults:m}=n;if(s in m)i=m[s];else{const p=xo(n);i=m[s]=h.call(null,t),p()}}else i=h;n.ce&&n.ce._setProp(s,i)}u[0]&&(a&&!c?i=!1:u[1]&&(i===""||i===Or(s))&&(i=!0))}return i}const N_=new WeakMap;function rf(e,t,s=!1){const i=s?N_:t.propsCache,n=i.get(e);if(n)return n;const a=e.props,u={},c=[];let h=!1;if(!xe(e)){const p=v=>{h=!0;const[w,D]=rf(v,t,!0);pt(u,w),D&&c.push(...D)};!s&&t.mixins.length&&t.mixins.forEach(p),e.extends&&p(e.extends),e.mixins&&e.mixins.forEach(p)}if(!a&&!h)return Ze(e)&&i.set(e,Dn),Dn;if(me(a))for(let p=0;p<a.length;p++){({}).NODE_ENV!=="production"&&!dt(a[p])&&Q("props must be strings when using array syntax.",a[p]);const v=Kt(a[p]);nf(v)&&(u[v]=st)}else if(a){({}).NODE_ENV!=="production"&&!Ze(a)&&Q("invalid props options",a);for(const p in a){const v=Kt(p);if(nf(v)){const w=a[p],D=u[v]=me(w)||xe(w)?{type:w}:pt({},w),V=D.type;let F=!1,se=!0;if(me(V))for(let A=0;A<V.length;++A){const re=V[A],Y=xe(re)&&re.name;if(Y==="Boolean"){F=!0;break}else Y==="String"&&(se=!1)}else F=xe(V)&&V.name==="Boolean";D[0]=F,D[1]=se,(F||Ye(D,"default"))&&c.push(v)}}}const m=[u,c];return Ze(e)&&i.set(e,m),m}function nf(e){return e[0]!=="$"&&!ro(e)?!0:({}.NODE_ENV!=="production"&&Q(`Invalid prop name: "${e}" is a reserved property.`),!1)}function I_(e){return e===null?"null":typeof e=="function"?e.name||"":typeof e=="object"&&e.constructor&&e.constructor.name||""}function of(e,t,s){const i=Me(t),n=s.propsOptions[0],a=Object.keys(e).map(u=>Kt(u));for(const u in n){let c=n[u];c!=null&&A_(u,i[u],c,{}.NODE_ENV!=="production"?zs(i):i,!a.includes(u))}}function A_(e,t,s,i,n){const{type:a,required:u,validator:c,skipCheck:h}=s;if(u&&n){Q('Missing required prop: "'+e+'"');return}if(!(t==null&&!u)){if(a!=null&&a!==!0&&!h){let m=!1;const p=me(a)?a:[a],v=[];for(let w=0;w<p.length&&!m;w++){const{valid:D,expectedType:V}=P_(t,p[w]);v.push(V||""),m=D}if(!m){Q(k_(e,t,v));return}}c&&!c(t,i)&&Q('Invalid prop: custom validator check failed for prop "'+e+'".')}}const M_=er("String,Number,Boolean,Function,Symbol,BigInt");function P_(e,t){let s;const i=I_(t);if(i==="null")s=e===null;else if(M_(i)){const n=typeof e;s=n===i.toLowerCase(),!s&&n==="object"&&(s=e instanceof t)}else i==="Object"?s=Ze(e):i==="Array"?s=me(e):s=e instanceof t;return{valid:s,expectedType:i}}function k_(e,t,s){if(s.length===0)return`Prop type [] for prop "${e}" won't match anything. Did you mean to use type Array instead?`;let i=`Invalid prop: type check failed for prop "${e}". Expected ${s.map(Yr).join(" | ")}`;const n=s[0],a=Ya(t),u=af(t,n),c=af(t,a);return s.length===1&&lf(n)&&!V_(n,a)&&(i+=` with value ${u}`),i+=`, got ${a} `,lf(a)&&(i+=`with value ${c}.`),i}function af(e,t){return t==="String"?`"${e}"`:t==="Number"?`${Number(e)}`:`${e}`}function lf(e){return["string","number","boolean"].some(s=>e.toLowerCase()===s)}function V_(...e){return e.some(t=>t.toLowerCase()==="boolean")}const uf=e=>e[0]==="_"||e==="$stable",Il=e=>me(e)?e.map(Rs):[Rs(e)],R_=(e,t,s)=>{if(t._n)return t;const i=Ne((...n)=>({}.NODE_ENV!=="production"&&Nt&&(!s||s.root===Nt.root)&&Q(`Slot "${e}" invoked outside of the render function: this will not track dependencies used in the slot. Invoke the slot function inside the render function instead.`),Il(t(...n))),s);return i._c=!1,i},cf=(e,t,s)=>{const i=e._ctx;for(const n in e){if(uf(n))continue;const a=e[n];if(xe(a))t[n]=R_(n,a,i);else if(a!=null){({}).NODE_ENV!=="production"&&Q(`Non-function value encountered for slot "${n}". Prefer function slots for better performance.`);const u=Il(a);t[n]=()=>u}}},df=(e,t)=>{({}).NODE_ENV!=="production"&&!vo(e.vnode)&&Q("Non-function value encountered for default slot. Prefer function slots for better performance.");const s=Il(t);e.slots.default=()=>s},Al=(e,t,s)=>{for(const i in t)(s||i!=="_")&&(e[i]=t[i])},U_=(e,t,s)=>{const i=e.slots=ef();if(e.vnode.shapeFlag&32){const n=t._;n?(Al(i,t,s),s&&ii(i,"_",n,!0)):cf(t,i)}else t&&df(e,t)},L_=(e,t,s)=>{const{vnode:i,slots:n}=e;let a=!0,u=st;if(i.shapeFlag&32){const c=t._;c?{}.NODE_ENV!=="production"&&ks?(Al(n,t,s),qs(e,"set","$slots")):s&&c===1?a=!1:Al(n,t,s):(a=!t.$stable,cf(t,n)),u=t}else t&&(df(e,t),u={default:1});if(a)for(const c in n)!uf(c)&&u[c]==null&&delete n[c]};let yo,Ar;function ar(e,t){e.appContext.config.performance&&Ai()&&Ar.mark(`vue-${t}-${e.uid}`),{}.NODE_ENV!=="production"&&Gv(e,t,Ai()?Ar.now():Date.now())}function lr(e,t){if(e.appContext.config.performance&&Ai()){const s=`vue-${t}-${e.uid}`,i=s+":end";Ar.mark(i),Ar.measure(`<${Li(e,e.type)}> ${t}`,s,i),Ar.clearMarks(s),Ar.clearMarks(i)}({}).NODE_ENV!=="production"&&Kv(e,t,Ai()?Ar.now():Date.now())}function Ai(){return yo!==void 0||(typeof window<"u"&&window.performance?(yo=!0,Ar=window.performance):yo=!1),yo}function F_(){const e=[];if({}.NODE_ENV!=="production"&&e.length){const t=e.length>1;console.warn(`Feature flag${t?"s":""} ${e.join(", ")} ${t?"are":"is"} not explicitly defined. You are running the esm-bundler build of Vue, which expects these compile-time feature flags to be globally injected via the bundler config in order to get better tree-shaking in the production bundle.

For more details, see https://link.vuejs.org/feature-flags.`)}}const Jt=ey;function B_(e){return $_(e)}function $_(e,t){F_();const s=no();s.__VUE__=!0,{}.NODE_ENV!=="production"&&yd(s.__VUE_DEVTOOLS_GLOBAL_HOOK__,s);const{insert:i,remove:n,patchProp:a,createElement:u,createText:c,createComment:h,setText:m,setElementText:p,parentNode:v,nextSibling:w,setScopeId:D=St,insertStaticContent:V}=e,F=(b,C,P,L=null,H=null,W=null,ee=void 0,K=null,J={}.NODE_ENV!=="production"&&ks?!1:!!C.dynamicChildren)=>{if(b===C)return;b&&!ln(b,C)&&(L=ie(b),ze(b,H,W,!0),b=null),C.patchFlag===-2&&(J=!1,C.dynamicChildren=null);const{type:G,ref:Ee,shapeFlag:ne}=C;switch(G){case wo:se(b,C,P,L);break;case wt:A(b,C,P,L);break;case Eo:b==null?re(C,P,L,ee):{}.NODE_ENV!=="production"&&Y(b,C,P,ee);break;case Ie:Ge(b,C,P,L,H,W,ee,K,J);break;default:ne&1?fe(b,C,P,L,H,W,ee,K,J):ne&6?vt(b,C,P,L,H,W,ee,K,J):ne&64||ne&128?G.process(b,C,P,L,H,W,ee,K,J,ke):{}.NODE_ENV!=="production"&&Q("Invalid VNode type:",G,`(${typeof G})`)}Ee!=null&&H&&Si(Ee,b&&b.ref,W,C||b,!C)},se=(b,C,P,L)=>{if(b==null)i(C.el=c(C.children),P,L);else{const H=C.el=b.el;C.children!==b.children&&m(H,C.children)}},A=(b,C,P,L)=>{b==null?i(C.el=h(C.children||""),P,L):C.el=b.el},re=(b,C,P,L)=>{[b.el,b.anchor]=V(b.children,C,P,L,b.el,b.anchor)},Y=(b,C,P,L)=>{if(C.children!==b.children){const H=w(b.anchor);Z(b),[C.el,C.anchor]=V(C.children,P,H,L)}else C.el=b.el,C.anchor=b.anchor},ye=({el:b,anchor:C},P,L)=>{let H;for(;b&&b!==C;)H=w(b),i(b,P,L),b=H;i(C,P,L)},Z=({el:b,anchor:C})=>{let P;for(;b&&b!==C;)P=w(b),n(b),b=P;n(C)},fe=(b,C,P,L,H,W,ee,K,J)=>{C.type==="svg"?ee="svg":C.type==="math"&&(ee="mathml"),b==null?_e(C,P,L,H,W,ee,K,J):I(b,C,H,W,ee,K,J)},_e=(b,C,P,L,H,W,ee,K)=>{let J,G;const{props:Ee,shapeFlag:ne,transition:ve,dirs:Ce}=b;if(J=b.el=u(b.type,W,Ee&&Ee.is,Ee),ne&8?p(J,b.children):ne&16&&ae(b.children,J,null,L,H,Ml(b,W),ee,K),Ce&&sn(b,null,L,"created"),Ae(J,b,b.scopeId,ee,L),Ee){for(const et in Ee)et!=="value"&&!ro(et)&&a(J,et,null,Ee[et],W,L);"value"in Ee&&a(J,"value",null,Ee.value,W),(G=Ee.onVnodeBeforeMount)&&Ys(G,L,b)}({}).NODE_ENV!=="production"&&(ii(J,"__vnode",b,!0),ii(J,"__vueParentComponent",L,!0)),Ce&&sn(b,null,L,"beforeMount");const Le=j_(H,ve);Le&&ve.beforeEnter(J),i(J,C,P),((G=Ee&&Ee.onVnodeMounted)||Le||Ce)&&Jt(()=>{G&&Ys(G,L,b),Le&&ve.enter(J),Ce&&sn(b,null,L,"mounted")},H)},Ae=(b,C,P,L,H)=>{if(P&&D(b,P),L)for(let W=0;W<L.length;W++)D(b,L[W]);if(H){let W=H.subTree;if({}.NODE_ENV!=="production"&&W.patchFlag>0&&W.patchFlag&2048&&(W=Rl(W.children)||W),C===W||yf(W.type)&&(W.ssContent===C||W.ssFallback===C)){const ee=H.vnode;Ae(b,ee,ee.scopeId,ee.slotScopeIds,H.parent)}}},ae=(b,C,P,L,H,W,ee,K,J=0)=>{for(let G=J;G<b.length;G++){const Ee=b[G]=K?Mr(b[G]):Rs(b[G]);F(null,Ee,C,P,L,H,W,ee,K)}},I=(b,C,P,L,H,W,ee)=>{const K=C.el=b.el;({}).NODE_ENV!=="production"&&(K.__vnode=C);let{patchFlag:J,dynamicChildren:G,dirs:Ee}=C;J|=b.patchFlag&16;const ne=b.props||st,ve=C.props||st;let Ce;if(P&&on(P,!1),(Ce=ve.onVnodeBeforeUpdate)&&Ys(Ce,P,C,b),Ee&&sn(C,b,P,"beforeUpdate"),P&&on(P,!0),{}.NODE_ENV!=="production"&&ks&&(J=0,ee=!1,G=null),(ne.innerHTML&&ve.innerHTML==null||ne.textContent&&ve.textContent==null)&&p(K,""),G?(be(b.dynamicChildren,G,K,P,L,Ml(C,H),W),{}.NODE_ENV!=="production"&&bo(b,C)):ee||Lt(b,C,K,null,P,L,Ml(C,H),W,!1),J>0){if(J&16)ue(K,ne,ve,P,H);else if(J&2&&ne.class!==ve.class&&a(K,"class",null,ve.class,H),J&4&&a(K,"style",ne.style,ve.style,H),J&8){const Le=C.dynamicProps;for(let et=0;et<Le.length;et++){const Qe=Le[et],Ft=ne[Qe],Ot=ve[Qe];(Ot!==Ft||Qe==="value")&&a(K,Qe,Ft,Ot,H,P)}}J&1&&b.children!==C.children&&p(K,C.children)}else!ee&&G==null&&ue(K,ne,ve,P,H);((Ce=ve.onVnodeUpdated)||Ee)&&Jt(()=>{Ce&&Ys(Ce,P,C,b),Ee&&sn(C,b,P,"updated")},L)},be=(b,C,P,L,H,W,ee)=>{for(let K=0;K<C.length;K++){const J=b[K],G=C[K],Ee=J.el&&(J.type===Ie||!ln(J,G)||J.shapeFlag&70)?v(J.el):P;F(J,G,Ee,null,L,H,W,ee,!0)}},ue=(b,C,P,L,H)=>{if(C!==P){if(C!==st)for(const W in C)!ro(W)&&!(W in P)&&a(b,W,C[W],null,H,L);for(const W in P){if(ro(W))continue;const ee=P[W],K=C[W];ee!==K&&W!=="value"&&a(b,W,K,ee,H,L)}"value"in P&&a(b,"value",C.value,P.value,H)}},Ge=(b,C,P,L,H,W,ee,K,J)=>{const G=C.el=b?b.el:c(""),Ee=C.anchor=b?b.anchor:c("");let{patchFlag:ne,dynamicChildren:ve,slotScopeIds:Ce}=C;({}).NODE_ENV!=="production"&&(ks||ne&2048)&&(ne=0,J=!1,ve=null),Ce&&(K=K?K.concat(Ce):Ce),b==null?(i(G,P,L),i(Ee,P,L),ae(C.children||[],P,Ee,H,W,ee,K,J)):ne>0&&ne&64&&ve&&b.dynamicChildren?(be(b.dynamicChildren,ve,P,H,W,ee,K),{}.NODE_ENV!=="production"?bo(b,C):(C.key!=null||H&&C===H.subTree)&&bo(b,C,!0)):Lt(b,C,P,Ee,H,W,ee,K,J)},vt=(b,C,P,L,H,W,ee,K,J)=>{C.slotScopeIds=K,b==null?C.shapeFlag&512?H.ctx.activate(C,P,L,ee,J):mt(C,P,L,H,W,ee,J):ft(b,C,J)},mt=(b,C,P,L,H,W,ee)=>{const K=b.component=ly(b,L,H);if({}.NODE_ENV!=="production"&&K.type.__hmrId&&Lv(K),{}.NODE_ENV!=="production"&&(vi(b),ar(K,"mount")),vo(b)&&(K.ctx.renderer=ke),{}.NODE_ENV!=="production"&&ar(K,"init"),cy(K,!1,ee),{}.NODE_ENV!=="production"&&lr(K,"init"),K.asyncDep){if({}.NODE_ENV!=="production"&&ks&&(b.el=null),H&&H.registerDep(K,Oe,ee),!b.el){const J=K.subTree=k(wt);A(null,J,C,P)}}else Oe(K,b,C,P,H,W,ee);({}).NODE_ENV!=="production"&&(_i(),lr(K,"mount"))},ft=(b,C,P)=>{const L=C.component=b.component;if(J_(b,C,P))if(L.asyncDep&&!L.asyncResolved){({}).NODE_ENV!=="production"&&vi(C),we(L,C,P),{}.NODE_ENV!=="production"&&_i();return}else L.next=C,L.update();else C.el=b.el,L.vnode=C},Oe=(b,C,P,L,H,W,ee)=>{const K=()=>{if(b.isMounted){let{next:ne,bu:ve,u:Ce,parent:Le,vnode:et}=b;{const Bt=ff(b);if(Bt){ne&&(ne.el=et.el,we(b,ne,ee)),Bt.asyncDep.then(()=>{b.isUnmounted||K()});return}}let Qe=ne,Ft;({}).NODE_ENV!=="production"&&vi(ne||b.vnode),on(b,!1),ne?(ne.el=et.el,we(b,ne,ee)):ne=et,ve&&On(ve),(Ft=ne.props&&ne.props.onVnodeBeforeUpdate)&&Ys(Ft,Le,ne,et),on(b,!0),{}.NODE_ENV!=="production"&&ar(b,"render");const Ot=Vl(b);({}).NODE_ENV!=="production"&&lr(b,"render");const ts=b.subTree;b.subTree=Ot,{}.NODE_ENV!=="production"&&ar(b,"patch"),F(ts,Ot,v(ts.el),ie(ts),b,H,W),{}.NODE_ENV!=="production"&&lr(b,"patch"),ne.el=Ot.el,Qe===null&&X_(b,Ot.el),Ce&&Jt(Ce,H),(Ft=ne.props&&ne.props.onVnodeUpdated)&&Jt(()=>Ys(Ft,Le,ne,et),H),{}.NODE_ENV!=="production"&&bd(b),{}.NODE_ENV!=="production"&&_i()}else{let ne;const{el:ve,props:Ce}=C,{bm:Le,m:et,parent:Qe,root:Ft,type:Ot}=b,ts=An(C);if(on(b,!1),Le&&On(Le),!ts&&(ne=Ce&&Ce.onVnodeBeforeMount)&&Ys(ne,Qe,C),on(b,!0),ve&&Ve){const Bt=()=>{({}).NODE_ENV!=="production"&&ar(b,"render"),b.subTree=Vl(b),{}.NODE_ENV!=="production"&&lr(b,"render"),{}.NODE_ENV!=="production"&&ar(b,"hydrate"),Ve(ve,b.subTree,b,H,null),{}.NODE_ENV!=="production"&&lr(b,"hydrate")};ts&&Ot.__asyncHydrate?Ot.__asyncHydrate(ve,b,Bt):Bt()}else{Ft.ce&&Ft.ce._injectChildStyle(Ot),{}.NODE_ENV!=="production"&&ar(b,"render");const Bt=b.subTree=Vl(b);({}).NODE_ENV!=="production"&&lr(b,"render"),{}.NODE_ENV!=="production"&&ar(b,"patch"),F(null,Bt,P,L,b,H,W),{}.NODE_ENV!=="production"&&lr(b,"patch"),C.el=Bt.el}if(et&&Jt(et,H),!ts&&(ne=Ce&&Ce.onVnodeMounted)){const Bt=C;Jt(()=>Ys(ne,Qe,Bt),H)}(C.shapeFlag&256||Qe&&An(Qe.vnode)&&Qe.vnode.shapeFlag&256)&&b.a&&Jt(b.a,H),b.isMounted=!0,{}.NODE_ENV!=="production"&&qv(b),C=P=L=null}};b.scope.on();const J=b.effect=new Bc(K);b.scope.off();const G=b.update=J.run.bind(J),Ee=b.job=J.runIfDirty.bind(J);Ee.i=b,Ee.id=b.uid,J.scheduler=()=>bi(Ee),on(b,!0),{}.NODE_ENV!=="production"&&(J.onTrack=b.rtc?ne=>On(b.rtc,ne):void 0,J.onTrigger=b.rtg?ne=>On(b.rtg,ne):void 0),G()},we=(b,C,P)=>{C.component=b;const L=b.vnode.props;b.vnode=C,b.next=null,T_(b,C.props,L,P),L_(b,C.children,P),tr(),pd(b),sr()},Lt=(b,C,P,L,H,W,ee,K,J=!1)=>{const G=b&&b.children,Ee=b?b.shapeFlag:0,ne=C.children,{patchFlag:ve,shapeFlag:Ce}=C;if(ve>0){if(ve&128){yt(G,ne,P,L,H,W,ee,K,J);return}else if(ve&256){es(G,ne,P,L,H,W,ee,K,J);return}}Ce&8?(Ee&16&&R(G,H,W),ne!==G&&p(P,ne)):Ee&16?Ce&16?yt(G,ne,P,L,H,W,ee,K,J):R(G,H,W,!0):(Ee&8&&p(P,""),Ce&16&&ae(ne,P,L,H,W,ee,K,J))},es=(b,C,P,L,H,W,ee,K,J)=>{b=b||Dn,C=C||Dn;const G=b.length,Ee=C.length,ne=Math.min(G,Ee);let ve;for(ve=0;ve<ne;ve++){const Ce=C[ve]=J?Mr(C[ve]):Rs(C[ve]);F(b[ve],Ce,P,null,H,W,ee,K,J)}G>Ee?R(b,H,W,!0,!1,ne):ae(C,P,L,H,W,ee,K,J,ne)},yt=(b,C,P,L,H,W,ee,K,J)=>{let G=0;const Ee=C.length;let ne=b.length-1,ve=Ee-1;for(;G<=ne&&G<=ve;){const Ce=b[G],Le=C[G]=J?Mr(C[G]):Rs(C[G]);if(ln(Ce,Le))F(Ce,Le,P,null,H,W,ee,K,J);else break;G++}for(;G<=ne&&G<=ve;){const Ce=b[ne],Le=C[ve]=J?Mr(C[ve]):Rs(C[ve]);if(ln(Ce,Le))F(Ce,Le,P,null,H,W,ee,K,J);else break;ne--,ve--}if(G>ne){if(G<=ve){const Ce=ve+1,Le=Ce<Ee?C[Ce].el:L;for(;G<=ve;)F(null,C[G]=J?Mr(C[G]):Rs(C[G]),P,Le,H,W,ee,K,J),G++}}else if(G>ve)for(;G<=ne;)ze(b[G],H,W,!0),G++;else{const Ce=G,Le=G,et=new Map;for(G=Le;G<=ve;G++){const It=C[G]=J?Mr(C[G]):Rs(C[G]);It.key!=null&&({}.NODE_ENV!=="production"&&et.has(It.key)&&Q("Duplicate keys found during update:",JSON.stringify(It.key),"Make sure keys are unique."),et.set(It.key,G))}let Qe,Ft=0;const Ot=ve-Le+1;let ts=!1,Bt=0;const gr=new Array(Ot);for(G=0;G<Ot;G++)gr[G]=0;for(G=Ce;G<=ne;G++){const It=b[G];if(Ft>=Ot){ze(It,H,W,!0);continue}let Es;if(It.key!=null)Es=et.get(It.key);else for(Qe=Le;Qe<=ve;Qe++)if(gr[Qe-Le]===0&&ln(It,C[Qe])){Es=Qe;break}Es===void 0?ze(It,H,W,!0):(gr[Es-Le]=G+1,Es>=Bt?Bt=Es:ts=!0,F(It,C[Es],P,null,H,W,ee,K,J),Ft++)}const Bn=ts?H_(gr):Dn;for(Qe=Bn.length-1,G=Ot-1;G>=0;G--){const It=Le+G,Es=C[It],na=It+1<Ee?C[It+1].el:L;gr[G]===0?F(null,Es,P,na,H,W,ee,K,J):ts&&(Qe<0||G!==Bn[Qe]?ce(Es,P,na,2):Qe--)}}},ce=(b,C,P,L,H=null)=>{const{el:W,type:ee,transition:K,children:J,shapeFlag:G}=b;if(G&6){ce(b.component.subTree,C,P,L);return}if(G&128){b.suspense.move(C,P,L);return}if(G&64){ee.move(b,C,P,ke);return}if(ee===Ie){i(W,C,P);for(let ne=0;ne<J.length;ne++)ce(J[ne],C,P,L);i(b.anchor,C,P);return}if(ee===Eo){ye(b,C,P);return}if(L!==2&&G&1&&K)if(L===0)K.beforeEnter(W),i(W,C,P),Jt(()=>K.enter(W),H);else{const{leave:ne,delayLeave:ve,afterLeave:Ce}=K,Le=()=>i(W,C,P),et=()=>{ne(W,()=>{Le(),Ce&&Ce()})};ve?ve(W,Le,et):et()}else i(W,C,P)},ze=(b,C,P,L=!1,H=!1)=>{const{type:W,props:ee,ref:K,children:J,dynamicChildren:G,shapeFlag:Ee,patchFlag:ne,dirs:ve,cacheIndex:Ce}=b;if(ne===-2&&(H=!1),K!=null&&Si(K,null,P,b,!0),Ce!=null&&(C.renderCache[Ce]=void 0),Ee&256){C.ctx.deactivate(b);return}const Le=Ee&1&&ve,et=!An(b);let Qe;if(et&&(Qe=ee&&ee.onVnodeBeforeUnmount)&&Ys(Qe,C,b),Ee&6)ps(b.component,P,L);else{if(Ee&128){b.suspense.unmount(P,L);return}Le&&sn(b,null,C,"beforeUnmount"),Ee&64?b.type.remove(b,C,P,ke,L):G&&!G.hasOnce&&(W!==Ie||ne>0&&ne&64)?R(G,C,P,!1,!0):(W===Ie&&ne&384||!H&&Ee&16)&&R(J,C,P),L&&hs(b)}(et&&(Qe=ee&&ee.onVnodeUnmounted)||Le)&&Jt(()=>{Qe&&Ys(Qe,C,b),Le&&sn(b,null,C,"unmounted")},P)},hs=b=>{const{type:C,el:P,anchor:L,transition:H}=b;if(C===Ie){({}).NODE_ENV!=="production"&&b.patchFlag>0&&b.patchFlag&2048&&H&&!H.persisted?b.children.forEach(ee=>{ee.type===wt?n(ee.el):hs(ee)}):zt(P,L);return}if(C===Eo){Z(b);return}const W=()=>{n(P),H&&!H.persisted&&H.afterLeave&&H.afterLeave()};if(b.shapeFlag&1&&H&&!H.persisted){const{leave:ee,delayLeave:K}=H,J=()=>ee(P,W);K?K(b.el,W,J):J()}else W()},zt=(b,C)=>{let P;for(;b!==C;)P=w(b),n(b),b=P;n(C)},ps=(b,C,P)=>{({}).NODE_ENV!=="production"&&b.type.__hmrId&&Fv(b);const{bum:L,scope:H,job:W,subTree:ee,um:K,m:J,a:G}=b;hf(J),hf(G),L&&On(L),H.stop(),W&&(W.flags|=8,ze(ee,b,C,P)),K&&Jt(K,C),Jt(()=>{b.isUnmounted=!0},C),C&&C.pendingBranch&&!C.isUnmounted&&b.asyncDep&&!b.asyncResolved&&b.suspenseId===C.pendingId&&(C.deps--,C.deps===0&&C.resolve()),{}.NODE_ENV!=="production"&&Wv(b)},R=(b,C,P,L=!1,H=!1,W=0)=>{for(let ee=W;ee<b.length;ee++)ze(b[ee],C,P,L,H)},ie=b=>{if(b.shapeFlag&6)return ie(b.component.subTree);if(b.shapeFlag&128)return b.suspense.next();const C=w(b.anchor||b.el),P=C&&C[Dd];return P?w(P):C};let oe=!1;const pe=(b,C,P)=>{b==null?C._vnode&&ze(C._vnode,null,null,!0):F(C._vnode||null,b,C,null,null,null,P),C._vnode=b,oe||(oe=!0,pd(),md(),oe=!1)},ke={p:F,um:ze,m:ce,r:hs,mt,mc:ae,pc:Lt,pbc:be,n:ie,o:e};let ot,Ve;return t&&([ot,Ve]=t(ke)),{render:pe,hydrate:ot,createApp:x_(pe,ot)}}function Ml({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function on({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function j_(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function bo(e,t,s=!1){const i=e.children,n=t.children;if(me(i)&&me(n))for(let a=0;a<i.length;a++){const u=i[a];let c=n[a];c.shapeFlag&1&&!c.dynamicChildren&&((c.patchFlag<=0||c.patchFlag===32)&&(c=n[a]=Mr(n[a]),c.el=u.el),!s&&c.patchFlag!==-2&&bo(u,c)),c.type===wo&&(c.el=u.el),{}.NODE_ENV!=="production"&&c.type===wt&&!c.el&&(c.el=u.el)}}function H_(e){const t=e.slice(),s=[0];let i,n,a,u,c;const h=e.length;for(i=0;i<h;i++){const m=e[i];if(m!==0){if(n=s[s.length-1],e[n]<m){t[i]=n,s.push(i);continue}for(a=0,u=s.length-1;a<u;)c=a+u>>1,e[s[c]]<m?a=c+1:u=c;m<e[s[a]]&&(a>0&&(t[i]=s[a-1]),s[a]=i)}}for(a=s.length,u=s[a-1];a-- >0;)s[a]=u,u=t[u];return s}function ff(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:ff(t)}function hf(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const q_=Symbol.for("v-scx"),z_=()=>{{const e=Vs(q_);return e||{}.NODE_ENV!=="production"&&Q("Server rendering context not provided. Make sure to only call useSSRContext() conditionally in the server build."),e}};function W_(e,t){return Pl(e,null,t)}function Pn(e,t,s){return{}.NODE_ENV!=="production"&&!xe(t)&&Q("`watch(fn, options?)` signature has been moved to a separate API. Use `watchEffect(fn, options?)` instead. `watch` now only supports `watch(source, cb, options?) signature."),Pl(e,t,s)}function Pl(e,t,s=st){const{immediate:i,deep:n,flush:a,once:u}=s;({}).NODE_ENV!=="production"&&!t&&(i!==void 0&&Q('watch() "immediate" option is only respected when using the watch(source, callback, options?) signature.'),n!==void 0&&Q('watch() "deep" option is only respected when using the watch(source, callback, options?) signature.'),u!==void 0&&Q('watch() "once" option is only respected when using the watch(source, callback, options?) signature.'));const c=pt({},s);({}).NODE_ENV!=="production"&&(c.onWarn=Q);const h=t&&i||!t&&a!=="post";let m;if(Oo){if(a==="sync"){const D=z_();m=D.__watcherHandles||(D.__watcherHandles=[])}else if(!h){const D=()=>{};return D.stop=St,D.resume=St,D.pause=St,D}}const p=Nt;c.call=(D,V,F)=>Ps(D,p,V,F);let v=!1;a==="post"?c.scheduler=D=>{Jt(D,p&&p.suspense)}:a!=="sync"&&(v=!0,c.scheduler=(D,V)=>{V?D():bi(D)}),c.augmentJob=D=>{t&&(D.flags|=4),v&&(D.flags|=2,p&&(D.id=p.uid,D.i=p))};const w=Nv(e,t,c);return Oo&&(m?m.push(w):h&&w()),w}function G_(e,t,s){const i=this.proxy,n=dt(e)?e.includes(".")?pf(i,e):()=>i[e]:e.bind(i,i);let a;xe(t)?a=t:(a=t.handler,s=t);const u=xo(this),c=Pl(n,a.bind(i),s);return u(),c}function pf(e,t){const s=t.split(".");return()=>{let i=e;for(let n=0;n<s.length&&i;n++)i=i[s[n]];return i}}const K_=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Kt(t)}Modifiers`]||e[`${Or(t)}Modifiers`];function Y_(e,t,...s){if(e.isUnmounted)return;const i=e.vnode.props||st;if({}.NODE_ENV!=="production"){const{emitsOptions:p,propsOptions:[v]}=e;if(p)if(!(t in p))(!v||!(Qr(Kt(t))in v))&&Q(`Component emitted event "${t}" but it is neither declared in the emits option nor as an "${Qr(Kt(t))}" prop.`);else{const w=p[t];xe(w)&&(w(...s)||Q(`Invalid event arguments: event validation failed for event "${t}".`))}}let n=s;const a=t.startsWith("update:"),u=a&&K_(i,t.slice(7));if(u&&(u.trim&&(n=s.map(p=>dt(p)?p.trim():p)),u.number&&(n=s.map(ai))),{}.NODE_ENV!=="production"&&Yv(e,t,n),{}.NODE_ENV!=="production"){const p=t.toLowerCase();p!==t&&i[Qr(p)]&&Q(`Event "${p}" is emitted in component ${Li(e,e.type)} but the handler is registered for "${t}". Note that HTML attributes are case-insensitive and you cannot use v-on to listen to camelCase events when using in-DOM templates. You should probably use "${Or(t)}" instead of "${t}".`)}let c,h=i[c=Qr(t)]||i[c=Qr(Kt(t))];!h&&a&&(h=i[c=Qr(Or(t))]),h&&Ps(h,e,6,n);const m=i[c+"Once"];if(m){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,Ps(m,e,6,n)}}function mf(e,t,s=!1){const i=t.emitsCache,n=i.get(e);if(n!==void 0)return n;const a=e.emits;let u={},c=!1;if(!xe(e)){const h=m=>{const p=mf(m,t,!0);p&&(c=!0,pt(u,p))};!s&&t.mixins.length&&t.mixins.forEach(h),e.extends&&h(e.extends),e.mixins&&e.mixins.forEach(h)}return!a&&!c?(Ze(e)&&i.set(e,null),null):(me(a)?a.forEach(h=>u[h]=null):pt(u,a),Ze(e)&&i.set(e,u),u)}function Mi(e,t){return!e||!to(t)?!1:(t=t.slice(2).replace(/Once$/,""),Ye(e,t[0].toLowerCase()+t.slice(1))||Ye(e,Or(t))||Ye(e,t))}let kl=!1;function Pi(){kl=!0}function Vl(e){const{type:t,vnode:s,proxy:i,withProxy:n,propsOptions:[a],slots:u,attrs:c,emit:h,render:m,renderCache:p,props:v,data:w,setupState:D,ctx:V,inheritAttrs:F}=e,se=Ci(e);let A,re;({}).NODE_ENV!=="production"&&(kl=!1);try{if(s.shapeFlag&4){const Z=n||i,fe={}.NODE_ENV!=="production"&&D.__isScriptSetup?new Proxy(Z,{get(_e,Ae,ae){return Q(`Property '${String(Ae)}' was accessed via 'this'. Avoid using 'this' in templates.`),Reflect.get(_e,Ae,ae)}}):Z;A=Rs(m.call(fe,Z,p,{}.NODE_ENV!=="production"?zs(v):v,D,w,V)),re=c}else{const Z=t;({}).NODE_ENV!=="production"&&c===v&&Pi(),A=Rs(Z.length>1?Z({}.NODE_ENV!=="production"?zs(v):v,{}.NODE_ENV!=="production"?{get attrs(){return Pi(),zs(c)},slots:u,emit:h}:{attrs:c,slots:u,emit:h}):Z({}.NODE_ENV!=="production"?zs(v):v,null)),re=t.props?c:Q_(c)}}catch(Z){Co.length=0,fo(Z,e,1),A=k(wt)}let Y=A,ye;if({}.NODE_ENV!=="production"&&A.patchFlag>0&&A.patchFlag&2048&&([Y,ye]=gf(A)),re&&F!==!1){const Z=Object.keys(re),{shapeFlag:fe}=Y;if(Z.length){if(fe&7)a&&Z.some(ni)&&(re=Z_(re,a)),Y=Ks(Y,re,!1,!0);else if({}.NODE_ENV!=="production"&&!kl&&Y.type!==wt){const _e=Object.keys(c),Ae=[],ae=[];for(let I=0,be=_e.length;I<be;I++){const ue=_e[I];to(ue)?ni(ue)||Ae.push(ue[2].toLowerCase()+ue.slice(3)):ae.push(ue)}ae.length&&Q(`Extraneous non-props attributes (${ae.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text or teleport root nodes.`),Ae.length&&Q(`Extraneous non-emits event listeners (${Ae.join(", ")}) were passed to component but could not be automatically inherited because component renders fragment or text root nodes. If the listener is intended to be a component custom event listener only, declare it using the "emits" option.`)}}}return s.dirs&&({}.NODE_ENV!=="production"&&!vf(Y)&&Q("Runtime directive used on component with non-element root node. The directives will not function as intended."),Y=Ks(Y,null,!1,!0),Y.dirs=Y.dirs?Y.dirs.concat(s.dirs):s.dirs),s.transition&&({}.NODE_ENV!=="production"&&!vf(Y)&&Q("Component inside <Transition> renders non-element root node that cannot be animated."),go(Y,s.transition)),{}.NODE_ENV!=="production"&&ye?ye(Y):A=Y,Ci(se),A}const gf=e=>{const t=e.children,s=e.dynamicChildren,i=Rl(t,!1);if(i){if({}.NODE_ENV!=="production"&&i.patchFlag>0&&i.patchFlag&2048)return gf(i)}else return[e,void 0];const n=t.indexOf(i),a=s?s.indexOf(i):-1,u=c=>{t[n]=c,s&&(a>-1?s[a]=c:c.patchFlag>0&&(e.dynamicChildren=[...s,c]))};return[Rs(i),u]};function Rl(e,t=!0){let s;for(let i=0;i<e.length;i++){const n=e[i];if(an(n)){if(n.type!==wt||n.children==="v-if"){if(s)return;if(s=n,{}.NODE_ENV!=="production"&&t&&s.patchFlag>0&&s.patchFlag&2048)return Rl(s.children)}}else return}return s}const Q_=e=>{let t;for(const s in e)(s==="class"||s==="style"||to(s))&&((t||(t={}))[s]=e[s]);return t},Z_=(e,t)=>{const s={};for(const i in e)(!ni(i)||!(i.slice(9)in t))&&(s[i]=e[i]);return s},vf=e=>e.shapeFlag&7||e.type===wt;function J_(e,t,s){const{props:i,children:n,component:a}=e,{props:u,children:c,patchFlag:h}=t,m=a.emitsOptions;if({}.NODE_ENV!=="production"&&(n||c)&&ks||t.dirs||t.transition)return!0;if(s&&h>=0){if(h&1024)return!0;if(h&16)return i?_f(i,u,m):!!u;if(h&8){const p=t.dynamicProps;for(let v=0;v<p.length;v++){const w=p[v];if(u[w]!==i[w]&&!Mi(m,w))return!0}}}else return(n||c)&&(!c||!c.$stable)?!0:i===u?!1:i?u?_f(i,u,m):!0:!!u;return!1}function _f(e,t,s){const i=Object.keys(t);if(i.length!==Object.keys(e).length)return!0;for(let n=0;n<i.length;n++){const a=i[n];if(t[a]!==e[a]&&!Mi(s,a))return!0}return!1}function X_({vnode:e,parent:t},s){for(;t;){const i=t.subTree;if(i.suspense&&i.suspense.activeBranch===e&&(i.el=e.el),i===e)(e=t.vnode).el=s,t=t.parent;else break}}const yf=e=>e.__isSuspense;function ey(e,t){t&&t.pendingBranch?me(e)?t.effects.push(...e):t.effects.push(e):hd(e)}const Ie=Symbol.for("v-fgt"),wo=Symbol.for("v-txt"),wt=Symbol.for("v-cmt"),Eo=Symbol.for("v-stc"),Co=[];let ds=null;function O(e=!1){Co.push(ds=e?null:[])}function ty(){Co.pop(),ds=Co[Co.length-1]||null}let Do=1;function bf(e,t=!1){Do+=e,e<0&&ds&&t&&(ds.hasOnce=!0)}function wf(e){return e.dynamicChildren=Do>0?ds||Dn:null,ty(),Do>0&&ds&&ds.push(e),e}function S(e,t,s,i,n,a){return wf(f(e,t,s,i,n,a,!0))}function Rt(e,t,s,i,n){return wf(k(e,t,s,i,n,!0))}function an(e){return e?e.__v_isVNode===!0:!1}function ln(e,t){if({}.NODE_ENV!=="production"&&t.shapeFlag&6&&e.component){const s=wi.get(t.type);if(s&&s.has(e.component))return e.shapeFlag&=-257,t.shapeFlag&=-513,!1}return e.type===t.type&&e.key===t.key}const sy=(...e)=>Cf(...e),Ef=({key:e})=>e??null,ki=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?dt(e)||Dt(e)||xe(e)?{i:xt,r:e,k:t,f:!!s}:e:null);function f(e,t=null,s=null,i=0,n=null,a=e===Ie?0:1,u=!1,c=!1){const h={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Ef(t),ref:t&&ki(t),scopeId:Ed,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:a,patchFlag:i,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:xt};return c?(Ul(h,s),a&128&&e.normalize(h)):s&&(h.shapeFlag|=dt(s)?8:16),{}.NODE_ENV!=="production"&&h.key!==h.key&&Q("VNode created with invalid key (NaN). VNode type:",h.type),Do>0&&!u&&ds&&(h.patchFlag>0||a&6)&&h.patchFlag!==32&&ds.push(h),h}const k={}.NODE_ENV!=="production"?sy:Cf;function Cf(e,t=null,s=null,i=0,n=null,a=!1){if((!e||e===h_)&&({}.NODE_ENV!=="production"&&!e&&Q(`Invalid vnode type when creating vnode: ${e}.`),e=wt),an(e)){const c=Ks(e,t,!0);return s&&Ul(c,s),Do>0&&!a&&ds&&(c.shapeFlag&6?ds[ds.indexOf(e)]=c:ds.push(c)),c.patchFlag=-2,c}if(If(e)&&(e=e.__vccOpts),t){t=ry(t);let{class:c,style:h}=t;c&&!dt(c)&&(t.class=he(c)),Ze(h)&&(pi(h)&&!me(h)&&(h=pt({},h)),t.style=us(h))}const u=dt(e)?1:yf(e)?128:xd(e)?64:Ze(e)?4:xe(e)?2:0;return{}.NODE_ENV!=="production"&&u&4&&pi(e)&&(e=Me(e),Q("Vue received a Component that was made a reactive object. This can lead to unnecessary performance overhead and should be avoided by marking the component with `markRaw` or using `shallowRef` instead of `ref`.",`
Component that was made reactive: `,e)),f(e,t,s,i,n,u,a,!0)}function ry(e){return e?pi(e)||tf(e)?pt({},e):e:null}function Ks(e,t,s=!1,i=!1){const{props:n,ref:a,patchFlag:u,children:c,transition:h}=e,m=t?oy(n||{},t):n,p={__v_isVNode:!0,__v_skip:!0,type:e.type,props:m,key:m&&Ef(m),ref:t&&t.ref?s&&a?me(a)?a.concat(ki(t)):[a,ki(t)]:ki(t):a,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:{}.NODE_ENV!=="production"&&u===-1&&me(c)?c.map(Df):c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ie?u===-1?16:u|16:u,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:h,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ks(e.ssContent),ssFallback:e.ssFallback&&Ks(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return h&&i&&go(p,h.clone(p)),p}function Df(e){const t=Ks(e);return me(e.children)&&(t.children=e.children.map(Df)),t}function Je(e=" ",t=0){return k(wo,null,e,t)}function ny(e,t){const s=k(Eo,null,e);return s.staticCount=t,s}function X(e="",t=!1){return t?(O(),Rt(wt,null,e)):k(wt,null,e)}function Rs(e){return e==null||typeof e=="boolean"?k(wt):me(e)?k(Ie,null,e.slice()):an(e)?Mr(e):k(wo,null,String(e))}function Mr(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ks(e)}function Ul(e,t){let s=0;const{shapeFlag:i}=e;if(t==null)t=null;else if(me(t))s=16;else if(typeof t=="object")if(i&65){const n=t.default;n&&(n._c&&(n._d=!1),Ul(e,n()),n._c&&(n._d=!0));return}else{s=32;const n=t._;!n&&!tf(t)?t._ctx=xt:n===3&&xt&&(xt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else xe(t)?(t={default:t,_ctx:xt},s=32):(t=String(t),i&64?(s=16,t=[Je(t)]):s=8);e.children=t,e.shapeFlag|=s}function oy(...e){const t={};for(let s=0;s<e.length;s++){const i=e[s];for(const n in i)if(n==="class")t.class!==i.class&&(t.class=he([t.class,i.class]));else if(n==="style")t.style=us([t.style,i.style]);else if(to(n)){const a=t[n],u=i[n];u&&a!==u&&!(me(a)&&a.includes(u))&&(t[n]=a?[].concat(a,u):u)}else n!==""&&(t[n]=i[n])}return t}function Ys(e,t,s,i=null){Ps(e,t,7,[s,i])}const iy=Jd();let ay=0;function ly(e,t,s){const i=e.type,n=(t?t.appContext:e.appContext)||iy,a={uid:ay++,vnode:e,type:i,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Fc(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:rf(i,n),emitsOptions:mf(i,n),emit:null,emitted:null,propsDefaults:st,inheritAttrs:i.inheritAttrs,ctx:st,data:st,props:st,attrs:st,slots:st,refs:st,setupState:st,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return{}.NODE_ENV!=="production"?a.ctx=m_(a):a.ctx={_:a},a.root=t?t.root:a,a.emit=Y_.bind(null,a),e.ce&&e.ce(a),a}let Nt=null;const Vi=()=>Nt||xt;let Ri,Ll;{const e=no(),t=(s,i)=>{let n;return(n=e[s])||(n=e[s]=[]),n.push(i),a=>{n.length>1?n.forEach(u=>u(a)):n[0](a)}};Ri=t("__VUE_INSTANCE_SETTERS__",s=>Nt=s),Ll=t("__VUE_SSR_SETTERS__",s=>Oo=s)}const xo=e=>{const t=Nt;return Ri(e),e.scope.on(),()=>{e.scope.off(),Ri(t)}},xf=()=>{Nt&&Nt.scope.off(),Ri(null)},uy=er("slot,component");function Fl(e,{isNativeTag:t}){(uy(e)||t(e))&&Q("Do not use built-in or reserved HTML elements as component id: "+e)}function Of(e){return e.vnode.shapeFlag&4}let Oo=!1;function cy(e,t=!1,s=!1){t&&Ll(t);const{props:i,children:n}=e.vnode,a=Of(e);O_(e,i,a,t),U_(e,n,s);const u=a?dy(e,t):void 0;return t&&Ll(!1),u}function dy(e,t){var s;const i=e.type;if({}.NODE_ENV!=="production"){if(i.name&&Fl(i.name,e.appContext.config),i.components){const a=Object.keys(i.components);for(let u=0;u<a.length;u++)Fl(a[u],e.appContext.config)}if(i.directives){const a=Object.keys(i.directives);for(let u=0;u<a.length;u++)Cd(a[u])}i.compilerOptions&&fy()&&Q('"compilerOptions" is only supported when using a build of Vue that includes the runtime compiler. Since you are using a runtime-only build, the options should be passed via your build tool config instead.')}e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Wd),{}.NODE_ENV!=="production"&&g_(e);const{setup:n}=i;if(n){tr();const a=e.setupContext=n.length>1?py(e):null,u=xo(e),c=Tn(n,e,0,[{}.NODE_ENV!=="production"?zs(e.props):e.props,a]),h=Ka(c);if(sr(),u(),(h||e.sp)&&!An(e)&&Ld(e),h){if(c.then(xf,xf),t)return c.then(m=>{Sf(e,m,t)}).catch(m=>{fo(m,e,0)});if(e.asyncDep=c,{}.NODE_ENV!=="production"&&!e.suspense){const m=(s=i.name)!=null?s:"Anonymous";Q(`Component <${m}>: setup function returned a promise, but no <Suspense> boundary was found in the parent component tree. A component with async setup() must be nested in a <Suspense> in order to be rendered.`)}}else Sf(e,c,t)}else Tf(e,t)}function Sf(e,t,s){xe(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Ze(t)?({}.NODE_ENV!=="production"&&an(t)&&Q("setup() should not return VNodes directly - return a render function instead."),{}.NODE_ENV!=="production"&&(e.devtoolsRawSetupState=t),e.setupState=ud(t),{}.NODE_ENV!=="production"&&v_(e)):{}.NODE_ENV!=="production"&&t!==void 0&&Q(`setup() should return an object. Received: ${t===null?"null":typeof t}`),Tf(e,s)}let Bl;const fy=()=>!Bl;function Tf(e,t,s){const i=e.type;if(!e.render){if(!t&&Bl&&!i.render){const n=i.template||Sl(e).template;if(n){({}).NODE_ENV!=="production"&&ar(e,"compile");const{isCustomElement:a,compilerOptions:u}=e.appContext.config,{delimiters:c,compilerOptions:h}=i,m=pt(pt({isCustomElement:a,delimiters:c},u),h);i.render=Bl(n,m),{}.NODE_ENV!=="production"&&lr(e,"compile")}}e.render=i.render||St}{const n=xo(e);tr();try{y_(e)}finally{sr(),n()}}({}).NODE_ENV!=="production"&&!i.render&&e.render===St&&!t&&(i.template?Q('Component provided template option but runtime compilation is not supported in this build of Vue. Configure your bundler to alias "vue" to "vue/dist/vue.esm-bundler.js".'):Q("Component is missing template or render function: ",i))}const Nf={}.NODE_ENV!=="production"?{get(e,t){return Pi(),Tt(e,"get",""),e[t]},set(){return Q("setupContext.attrs is readonly."),!1},deleteProperty(){return Q("setupContext.attrs is readonly."),!1}}:{get(e,t){return Tt(e,"get",""),e[t]}};function hy(e){return new Proxy(e.slots,{get(t,s){return Tt(e,"get","$slots"),t[s]}})}function py(e){const t=s=>{if({}.NODE_ENV!=="production"&&(e.exposed&&Q("expose() should be called only once per setup()."),s!=null)){let i=typeof s;i==="object"&&(me(s)?i="array":Dt(s)&&(i="ref")),i!=="object"&&Q(`expose() should be passed a plain object, received ${i}.`)}e.exposed=s||{}};if({}.NODE_ENV!=="production"){let s,i;return Object.freeze({get attrs(){return s||(s=new Proxy(e.attrs,Nf))},get slots(){return i||(i=hy(e))},get emit(){return(n,...a)=>e.emit(n,...a)},expose:t})}else return{attrs:new Proxy(e.attrs,Nf),slots:e.slots,emit:e.emit,expose:t}}function Ui(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(ud(cl(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in nn)return nn[s](e)},has(t,s){return s in t||s in nn}})):e.proxy}const my=/(?:^|[-_])(\w)/g,gy=e=>e.replace(my,t=>t.toUpperCase()).replace(/[-_]/g,"");function $l(e,t=!0){return xe(e)?e.displayName||e.name:e.name||t&&e.__name}function Li(e,t,s=!1){let i=$l(t);if(!i&&t.__file){const n=t.__file.match(/([^/\\]+)\.\w+$/);n&&(i=n[1])}if(!i&&e&&e.parent){const n=a=>{for(const u in a)if(a[u]===t)return u};i=n(e.components||e.parent.type.components)||n(e.appContext.components)}return i?gy(i):s?"App":"Anonymous"}function If(e){return xe(e)&&"__vccOpts"in e}const Us=(e,t)=>{const s=Sv(e,t,Oo);if({}.NODE_ENV!=="production"){const i=Vi();i&&i.appContext.config.warnRecursiveComputed&&(s._warnRecursive=!0)}return s};function jl(e,t,s){const i=arguments.length;return i===2?Ze(t)&&!me(t)?an(t)?k(e,null,[t]):k(e,t):k(e,null,t):(i>3?s=Array.prototype.slice.call(arguments,2):i===3&&an(s)&&(s=[s]),k(e,t,s))}function vy(){if({}.NODE_ENV==="production"||typeof window>"u")return;const e={style:"color:#3ba776"},t={style:"color:#1677ff"},s={style:"color:#f5222d"},i={style:"color:#eb2f96"},n={__vue_custom_formatter:!0,header(v){return Ze(v)?v.__isVue?["div",e,"VueInstance"]:Dt(v)?["div",{},["span",e,p(v)],"<",c("_value"in v?v._value:v),">"]:Jr(v)?["div",{},["span",e,Yt(v)?"ShallowReactive":"Reactive"],"<",c(v),`>${nr(v)?" (readonly)":""}`]:nr(v)?["div",{},["span",e,Yt(v)?"ShallowReadonly":"Readonly"],"<",c(v),">"]:null:null},hasBody(v){return v&&v.__isVue},body(v){if(v&&v.__isVue)return["div",{},...a(v.$)]}};function a(v){const w=[];v.type.props&&v.props&&w.push(u("props",Me(v.props))),v.setupState!==st&&w.push(u("setup",v.setupState)),v.data!==st&&w.push(u("data",Me(v.data)));const D=h(v,"computed");D&&w.push(u("computed",D));const V=h(v,"inject");return V&&w.push(u("injected",V)),w.push(["div",{},["span",{style:i.style+";opacity:0.66"},"$ (internal): "],["object",{object:v}]]),w}function u(v,w){return w=pt({},w),Object.keys(w).length?["div",{style:"line-height:1.25em;margin-bottom:0.6em"},["div",{style:"color:#476582"},v],["div",{style:"padding-left:1.25em"},...Object.keys(w).map(D=>["div",{},["span",i,D+": "],c(w[D],!1)])]]:["span",{}]}function c(v,w=!0){return typeof v=="number"?["span",t,v]:typeof v=="string"?["span",s,JSON.stringify(v)]:typeof v=="boolean"?["span",i,v]:Ze(v)?["object",{object:w?Me(v):v}]:["span",s,String(v)]}function h(v,w){const D=v.type;if(xe(D))return;const V={};for(const F in v.ctx)m(D,F,w)&&(V[F]=v.ctx[F]);return V}function m(v,w,D){const V=v[D];if(me(V)&&V.includes(w)||Ze(V)&&w in V||v.extends&&m(v.extends,w,D)||v.mixins&&v.mixins.some(F=>m(F,w,D)))return!0}function p(v){return Yt(v)?"ShallowRef":v.effect?"ComputedRef":"Ref"}window.devtoolsFormatters?window.devtoolsFormatters.push(n):window.devtoolsFormatters=[n]}const Af="3.5.13",Qs={}.NODE_ENV!=="production"?Q:St;/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Hl;const Mf=typeof window<"u"&&window.trustedTypes;if(Mf)try{Hl=Mf.createPolicy("vue",{createHTML:e=>e})}catch(e){({}).NODE_ENV!=="production"&&Qs(`Error creating trusted types policy: ${e}`)}const Pf=Hl?e=>Hl.createHTML(e):e=>e,_y="http://www.w3.org/2000/svg",yy="http://www.w3.org/1998/Math/MathML",ur=typeof document<"u"?document:null,kf=ur&&ur.createElement("template"),by={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,i)=>{const n=t==="svg"?ur.createElementNS(_y,e):t==="mathml"?ur.createElementNS(yy,e):s?ur.createElement(e,{is:s}):ur.createElement(e);return e==="select"&&i&&i.multiple!=null&&n.setAttribute("multiple",i.multiple),n},createText:e=>ur.createTextNode(e),createComment:e=>ur.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ur.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,i,n,a){const u=s?s.previousSibling:t.lastChild;if(n&&(n===a||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),s),!(n===a||!(n=n.nextSibling)););else{kf.innerHTML=Pf(i==="svg"?`<svg>${e}</svg>`:i==="mathml"?`<math>${e}</math>`:e);const c=kf.content;if(i==="svg"||i==="mathml"){const h=c.firstChild;for(;h.firstChild;)c.appendChild(h.firstChild);c.removeChild(h)}t.insertBefore(c,s)}return[u?u.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},Pr="transition",So="animation",To=Symbol("_vtc"),Vf={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},wy=pt({},Ad,Vf),Rf=(e=>(e.displayName="Transition",e.props=wy,e))((e,{slots:t})=>jl(e_,Ey(e),t)),un=(e,t=[])=>{me(e)?e.forEach(s=>s(...t)):e&&e(...t)},Uf=e=>e?me(e)?e.some(t=>t.length>1):e.length>1:!1;function Ey(e){const t={};for(const ue in e)ue in Vf||(t[ue]=e[ue]);if(e.css===!1)return t;const{name:s="v",type:i,duration:n,enterFromClass:a=`${s}-enter-from`,enterActiveClass:u=`${s}-enter-active`,enterToClass:c=`${s}-enter-to`,appearFromClass:h=a,appearActiveClass:m=u,appearToClass:p=c,leaveFromClass:v=`${s}-leave-from`,leaveActiveClass:w=`${s}-leave-active`,leaveToClass:D=`${s}-leave-to`}=e,V=Cy(n),F=V&&V[0],se=V&&V[1],{onBeforeEnter:A,onEnter:re,onEnterCancelled:Y,onLeave:ye,onLeaveCancelled:Z,onBeforeAppear:fe=A,onAppear:_e=re,onAppearCancelled:Ae=Y}=t,ae=(ue,Ge,vt,mt)=>{ue._enterCancelled=mt,cn(ue,Ge?p:c),cn(ue,Ge?m:u),vt&&vt()},I=(ue,Ge)=>{ue._isLeaving=!1,cn(ue,v),cn(ue,D),cn(ue,w),Ge&&Ge()},be=ue=>(Ge,vt)=>{const mt=ue?_e:re,ft=()=>ae(Ge,ue,vt);un(mt,[Ge,ft]),Lf(()=>{cn(Ge,ue?h:a),cr(Ge,ue?p:c),Uf(mt)||Ff(Ge,i,F,ft)})};return pt(t,{onBeforeEnter(ue){un(A,[ue]),cr(ue,a),cr(ue,u)},onBeforeAppear(ue){un(fe,[ue]),cr(ue,h),cr(ue,m)},onEnter:be(!1),onAppear:be(!0),onLeave(ue,Ge){ue._isLeaving=!0;const vt=()=>I(ue,Ge);cr(ue,v),ue._enterCancelled?(cr(ue,w),jf()):(jf(),cr(ue,w)),Lf(()=>{ue._isLeaving&&(cn(ue,v),cr(ue,D),Uf(ye)||Ff(ue,i,se,vt))}),un(ye,[ue,vt])},onEnterCancelled(ue){ae(ue,!1,void 0,!0),un(Y,[ue])},onAppearCancelled(ue){ae(ue,!0,void 0,!0),un(Ae,[ue])},onLeaveCancelled(ue){I(ue),un(Z,[ue])}})}function Cy(e){if(e==null)return null;if(Ze(e))return[ql(e.enter),ql(e.leave)];{const t=ql(e);return[t,t]}}function ql(e){const t=qg(e);return{}.NODE_ENV!=="production"&&kv(t,"<transition> explicit duration"),t}function cr(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.add(s)),(e[To]||(e[To]=new Set)).add(t)}function cn(e,t){t.split(/\s+/).forEach(i=>i&&e.classList.remove(i));const s=e[To];s&&(s.delete(t),s.size||(e[To]=void 0))}function Lf(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Dy=0;function Ff(e,t,s,i){const n=e._endId=++Dy,a=()=>{n===e._endId&&i()};if(s!=null)return setTimeout(a,s);const{type:u,timeout:c,propCount:h}=xy(e,t);if(!u)return i();const m=u+"end";let p=0;const v=()=>{e.removeEventListener(m,w),a()},w=D=>{D.target===e&&++p>=h&&v()};setTimeout(()=>{p<h&&v()},c+1),e.addEventListener(m,w)}function xy(e,t){const s=window.getComputedStyle(e),i=V=>(s[V]||"").split(", "),n=i(`${Pr}Delay`),a=i(`${Pr}Duration`),u=Bf(n,a),c=i(`${So}Delay`),h=i(`${So}Duration`),m=Bf(c,h);let p=null,v=0,w=0;t===Pr?u>0&&(p=Pr,v=u,w=a.length):t===So?m>0&&(p=So,v=m,w=h.length):(v=Math.max(u,m),p=v>0?u>m?Pr:So:null,w=p?p===Pr?a.length:h.length:0);const D=p===Pr&&/\b(transform|all)(,|$)/.test(i(`${Pr}Property`).toString());return{type:p,timeout:v,propCount:w,hasTransform:D}}function Bf(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((s,i)=>$f(s)+$f(e[i])))}function $f(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function jf(){return document.body.offsetHeight}function Oy(e,t,s){const i=e[To];i&&(t=(t?[t,...i]:[...i]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const Fi=Symbol("_vod"),Hf=Symbol("_vsh"),zl={beforeMount(e,{value:t},{transition:s}){e[Fi]=e.style.display==="none"?"":e.style.display,s&&t?s.beforeEnter(e):No(e,t)},mounted(e,{value:t},{transition:s}){s&&t&&s.enter(e)},updated(e,{value:t,oldValue:s},{transition:i}){!t!=!s&&(i?t?(i.beforeEnter(e),No(e,!0),i.enter(e)):i.leave(e,()=>{No(e,!1)}):No(e,t))},beforeUnmount(e,{value:t}){No(e,t)}};({}).NODE_ENV!=="production"&&(zl.name="show");function No(e,t){e.style.display=t?e[Fi]:"none",e[Hf]=!t}const Sy=Symbol({}.NODE_ENV!=="production"?"CSS_VAR_TEXT":""),Ty=/(^|;)\s*display\s*:/;function Ny(e,t,s){const i=e.style,n=dt(s);let a=!1;if(s&&!n){if(t)if(dt(t))for(const u of t.split(";")){const c=u.slice(0,u.indexOf(":")).trim();s[c]==null&&Bi(i,c,"")}else for(const u in t)s[u]==null&&Bi(i,u,"");for(const u in s)u==="display"&&(a=!0),Bi(i,u,s[u])}else if(n){if(t!==s){const u=i[Sy];u&&(s+=";"+u),i.cssText=s,a=Ty.test(s)}}else t&&e.removeAttribute("style");Fi in e&&(e[Fi]=a?i.display:"",e[Hf]&&(i.display="none"))}const Iy=/[^\\];\s*$/,qf=/\s*!important$/;function Bi(e,t,s){if(me(s))s.forEach(i=>Bi(e,t,i));else if(s==null&&(s=""),{}.NODE_ENV!=="production"&&Iy.test(s)&&Qs(`Unexpected semicolon at the end of '${t}' style value: '${s}'`),t.startsWith("--"))e.setProperty(t,s);else{const i=Ay(e,t);qf.test(s)?e.setProperty(Or(i),s.replace(qf,""),"important"):e[i]=s}}const zf=["Webkit","Moz","ms"],Wl={};function Ay(e,t){const s=Wl[t];if(s)return s;let i=Kt(t);if(i!=="filter"&&i in e)return Wl[t]=i;i=Yr(i);for(let n=0;n<zf.length;n++){const a=zf[n]+i;if(a in e)return Wl[t]=a}return t}const Wf="http://www.w3.org/1999/xlink";function Gf(e,t,s,i,n,a=tv(t)){i&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(Wf,t.slice(6,t.length)):e.setAttributeNS(Wf,t,s):s==null||a&&!Rc(s)?e.removeAttribute(t):e.setAttribute(t,a?"":As(s)?String(s):s)}function Kf(e,t,s,i,n){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?Pf(s):s);return}const a=e.tagName;if(t==="value"&&a!=="PROGRESS"&&!a.includes("-")){const c=a==="OPTION"?e.getAttribute("value")||"":e.value,h=s==null?e.type==="checkbox"?"on":"":String(s);(c!==h||!("_value"in e))&&(e.value=h),s==null&&e.removeAttribute(t),e._value=s;return}let u=!1;if(s===""||s==null){const c=typeof e[t];c==="boolean"?s=Rc(s):s==null&&c==="string"?(s="",u=!0):c==="number"&&(s=0,u=!0)}try{e[t]=s}catch(c){({}).NODE_ENV!=="production"&&!u&&Qs(`Failed setting prop "${t}" on <${a.toLowerCase()}>: value ${s} is invalid.`,c)}u&&e.removeAttribute(n||t)}function kr(e,t,s,i){e.addEventListener(t,s,i)}function My(e,t,s,i){e.removeEventListener(t,s,i)}const Yf=Symbol("_vei");function Py(e,t,s,i,n=null){const a=e[Yf]||(e[Yf]={}),u=a[t];if(i&&u)u.value={}.NODE_ENV!=="production"?Zf(i,t):i;else{const[c,h]=ky(t);if(i){const m=a[t]=Uy({}.NODE_ENV!=="production"?Zf(i,t):i,n);kr(e,c,m,h)}else u&&(My(e,c,u,h),a[t]=void 0)}}const Qf=/(?:Once|Passive|Capture)$/;function ky(e){let t;if(Qf.test(e)){t={};let i;for(;i=e.match(Qf);)e=e.slice(0,e.length-i[0].length),t[i[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Or(e.slice(2)),t]}let Gl=0;const Vy=Promise.resolve(),Ry=()=>Gl||(Vy.then(()=>Gl=0),Gl=Date.now());function Uy(e,t){const s=i=>{if(!i._vts)i._vts=Date.now();else if(i._vts<=s.attached)return;Ps(Ly(i,s.value),t,5,[i])};return s.value=e,s.attached=Ry(),s}function Zf(e,t){return xe(e)||me(e)?e:(Qs(`Wrong type passed as event handler to ${t} - did you forget @ or : in front of your prop?
Expected function or array of functions, received type ${typeof e}.`),St)}function Ly(e,t){if(me(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(i=>n=>!n._stopped&&i&&i(n))}else return t}const Jf=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Fy=(e,t,s,i,n,a)=>{const u=n==="svg";t==="class"?Oy(e,i,u):t==="style"?Ny(e,s,i):to(t)?ni(t)||Py(e,t,s,i,a):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):By(e,t,i,u))?(Kf(e,t,i),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Gf(e,t,i,u,a,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!dt(i))?Kf(e,Kt(t),i,a,t):(t==="true-value"?e._trueValue=i:t==="false-value"&&(e._falseValue=i),Gf(e,t,i,u))};function By(e,t,s,i){if(i)return!!(t==="innerHTML"||t==="textContent"||t in e&&Jf(t)&&xe(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return Jf(t)&&dt(s)?!1:t in e}const kn=e=>{const t=e.props["onUpdate:modelValue"]||!1;return me(t)?s=>On(t,s):t};function $y(e){e.target.composing=!0}function Xf(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const dr=Symbol("_assign"),Xt={created(e,{modifiers:{lazy:t,trim:s,number:i}},n){e[dr]=kn(n);const a=i||n.props&&n.props.type==="number";kr(e,t?"change":"input",u=>{if(u.target.composing)return;let c=e.value;s&&(c=c.trim()),a&&(c=ai(c)),e[dr](c)}),s&&kr(e,"change",()=>{e.value=e.value.trim()}),t||(kr(e,"compositionstart",$y),kr(e,"compositionend",Xf),kr(e,"change",Xf))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:i,trim:n,number:a}},u){if(e[dr]=kn(u),e.composing)return;const c=(a||e.type==="number")&&!/^0\d/.test(e.value)?ai(e.value):e.value,h=t??"";c!==h&&(document.activeElement===e&&e.type!=="range"&&(i&&t===s||n&&e.value.trim()===h)||(e.value=h))}},$i={deep:!0,created(e,t,s){e[dr]=kn(s),kr(e,"change",()=>{const i=e._modelValue,n=Io(e),a=e.checked,u=e[dr];if(me(i)){const c=Za(i,n),h=c!==-1;if(a&&!h)u(i.concat(n));else if(!a&&h){const m=[...i];m.splice(c,1),u(m)}}else if(xn(i)){const c=new Set(i);a?c.add(n):c.delete(n),u(c)}else u(sh(e,a))})},mounted:eh,beforeUpdate(e,t,s){e[dr]=kn(s),eh(e,t,s)}};function eh(e,{value:t,oldValue:s},i){e._modelValue=t;let n;if(me(t))n=Za(t,i.props.value)>-1;else if(xn(t))n=t.has(i.props.value);else{if(t===s)return;n=oo(t,sh(e,!0))}e.checked!==n&&(e.checked=n)}const Kl={deep:!0,created(e,{value:t,modifiers:{number:s}},i){const n=xn(t);kr(e,"change",()=>{const a=Array.prototype.filter.call(e.options,u=>u.selected).map(u=>s?ai(Io(u)):Io(u));e[dr](e.multiple?n?new Set(a):a:a[0]),e._assigning=!0,pl(()=>{e._assigning=!1})}),e[dr]=kn(i)},mounted(e,{value:t}){th(e,t)},beforeUpdate(e,t,s){e[dr]=kn(s)},updated(e,{value:t}){e._assigning||th(e,t)}};function th(e,t){const s=e.multiple,i=me(t);if(s&&!i&&!xn(t)){({}).NODE_ENV!=="production"&&Qs(`<select multiple v-model> expects an Array or Set value for its binding, but got ${Object.prototype.toString.call(t).slice(8,-1)}.`);return}for(let n=0,a=e.options.length;n<a;n++){const u=e.options[n],c=Io(u);if(s)if(i){const h=typeof c;h==="string"||h==="number"?u.selected=t.some(m=>String(m)===String(c)):u.selected=Za(t,c)>-1}else u.selected=t.has(c);else if(oo(Io(u),t)){e.selectedIndex!==n&&(e.selectedIndex=n);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}function Io(e){return"_value"in e?e._value:e.value}function sh(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const jy=["ctrl","shift","alt","meta"],Hy={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>jy.some(s=>e[`${s}Key`]&&!t.includes(s))},Ut=(e,t)=>{const s=e._withMods||(e._withMods={}),i=t.join(".");return s[i]||(s[i]=(n,...a)=>{for(let u=0;u<t.length;u++){const c=Hy[t[u]];if(c&&c(n,t))return}return e(n,...a)})},qy=pt({patchProp:Fy},by);let rh;function zy(){return rh||(rh=B_(qy))}const Wy=(...e)=>{const t=zy().createApp(...e);({}).NODE_ENV!=="production"&&(Ky(t),Yy(t));const{mount:s}=t;return t.mount=i=>{const n=Qy(i);if(!n)return;const a=t._component;!xe(a)&&!a.render&&!a.template&&(a.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const u=s(n,!1,Gy(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),u},t};function Gy(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Ky(e){Object.defineProperty(e.config,"isNativeTag",{value:t=>Jg(t)||Xg(t)||ev(t),writable:!1})}function Yy(e){{const t=e.config.isCustomElement;Object.defineProperty(e.config,"isCustomElement",{get(){return t},set(){Qs("The `isCustomElement` config option is deprecated. Use `compilerOptions.isCustomElement` instead.")}});const s=e.config.compilerOptions,i='The `compilerOptions` config option is only respected when using a build of Vue.js that includes the runtime compiler (aka "full build"). Since you are using the runtime-only build, `compilerOptions` must be passed to `@vue/compiler-dom` in the build setup instead.\n- For vue-loader: pass it via vue-loader\'s `compilerOptions` loader option.\n- For vue-cli: see https://cli.vuejs.org/guide/webpack.html#modifying-options-of-a-loader\n- For vite: pass it via @vitejs/plugin-vue options. See https://github.com/vitejs/vite-plugin-vue/tree/main/packages/plugin-vue#example-for-passing-options-to-vuecompiler-sfc';Object.defineProperty(e.config,"compilerOptions",{get(){return Qs(i),s},set(){Qs(i)}})}}function Qy(e){if(dt(e)){const t=document.querySelector(e);return{}.NODE_ENV!=="production"&&!t&&Qs(`Failed to mount app: mount target selector "${e}" returned null.`),t}return{}.NODE_ENV!=="production"&&window.ShadowRoot&&e instanceof window.ShadowRoot&&e.mode==="closed"&&Qs('mounting on a ShadowRoot with `{mode: "closed"}` may lead to unpredictable bugs'),e}/**
* vue v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Zy(){vy()}({}).NODE_ENV!=="production"&&Zy();var Jy=!1;function Xy(){return nh().__VUE_DEVTOOLS_GLOBAL_HOOK__}function nh(){return typeof navigator<"u"&&typeof window<"u"?window:typeof globalThis<"u"?globalThis:{}}const eb=typeof Proxy=="function",tb="devtools-plugin:setup",sb="plugin:settings:set";let Vn,Yl;function rb(){var e;return Vn!==void 0||(typeof window<"u"&&window.performance?(Vn=!0,Yl=window.performance):typeof globalThis<"u"&&(!((e=globalThis.perf_hooks)===null||e===void 0)&&e.performance)?(Vn=!0,Yl=globalThis.perf_hooks.performance):Vn=!1),Vn}function nb(){return rb()?Yl.now():Date.now()}class ob{constructor(t,s){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=t,this.hook=s;const i={};if(t.settings)for(const u in t.settings){const c=t.settings[u];i[u]=c.defaultValue}const n=`__vue-devtools-plugin-settings__${t.id}`;let a=Object.assign({},i);try{const u=localStorage.getItem(n),c=JSON.parse(u);Object.assign(a,c)}catch{}this.fallbacks={getSettings(){return a},setSettings(u){try{localStorage.setItem(n,JSON.stringify(u))}catch{}a=u},now(){return nb()}},s&&s.on(sb,(u,c)=>{u===this.plugin.id&&this.fallbacks.setSettings(c)}),this.proxiedOn=new Proxy({},{get:(u,c)=>this.target?this.target.on[c]:(...h)=>{this.onQueue.push({method:c,args:h})}}),this.proxiedTarget=new Proxy({},{get:(u,c)=>this.target?this.target[c]:c==="on"?this.proxiedOn:Object.keys(this.fallbacks).includes(c)?(...h)=>(this.targetQueue.push({method:c,args:h,resolve:()=>{}}),this.fallbacks[c](...h)):(...h)=>new Promise(m=>{this.targetQueue.push({method:c,args:h,resolve:m})})})}async setRealTarget(t){this.target=t;for(const s of this.onQueue)this.target.on[s.method](...s.args);for(const s of this.targetQueue)s.resolve(await this.target[s.method](...s.args))}}function Ql(e,t){const s=e,i=nh(),n=Xy(),a=eb&&s.enableEarlyProxy;if(n&&(i.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__||!a))n.emit(tb,e,t);else{const u=a?new ob(s,n):null;(i.__VUE_DEVTOOLS_PLUGINS__=i.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:s,setupFn:t,proxy:u}),u&&t(u.proxiedTarget)}}/*!
 * pinia v2.3.1
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const ib={}.NODE_ENV!=="production"?Symbol("pinia"):Symbol();var dn;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(dn||(dn={}));const Zl=typeof window<"u",oh=(()=>typeof window=="object"&&window.window===window?window:typeof self=="object"&&self.self===self?self:typeof global=="object"&&global.global===global?global:typeof globalThis=="object"?globalThis:{HTMLElement:null})();function ab(e,{autoBom:t=!1}={}){return t&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e}function Jl(e,t,s){const i=new XMLHttpRequest;i.open("GET",e),i.responseType="blob",i.onload=function(){lh(i.response,t,s)},i.onerror=function(){console.error("could not download file")},i.send()}function ih(e){const t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch{}return t.status>=200&&t.status<=299}function ji(e){try{e.dispatchEvent(new MouseEvent("click"))}catch{const s=document.createEvent("MouseEvents");s.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(s)}}const Hi=typeof navigator=="object"?navigator:{userAgent:""},ah=(()=>/Macintosh/.test(Hi.userAgent)&&/AppleWebKit/.test(Hi.userAgent)&&!/Safari/.test(Hi.userAgent))(),lh=Zl?typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype&&!ah?lb:"msSaveOrOpenBlob"in Hi?ub:cb:()=>{};function lb(e,t="download",s){const i=document.createElement("a");i.download=t,i.rel="noopener",typeof e=="string"?(i.href=e,i.origin!==location.origin?ih(i.href)?Jl(e,t,s):(i.target="_blank",ji(i)):ji(i)):(i.href=URL.createObjectURL(e),setTimeout(function(){URL.revokeObjectURL(i.href)},4e4),setTimeout(function(){ji(i)},0))}function ub(e,t="download",s){if(typeof e=="string")if(ih(e))Jl(e,t,s);else{const i=document.createElement("a");i.href=e,i.target="_blank",setTimeout(function(){ji(i)})}else navigator.msSaveOrOpenBlob(ab(e,s),t)}function cb(e,t,s,i){if(i=i||open("","_blank"),i&&(i.document.title=i.document.body.innerText="downloading..."),typeof e=="string")return Jl(e,t,s);const n=e.type==="application/octet-stream",a=/constructor/i.test(String(oh.HTMLElement))||"safari"in oh,u=/CriOS\/[\d]+/.test(navigator.userAgent);if((u||n&&a||ah)&&typeof FileReader<"u"){const c=new FileReader;c.onloadend=function(){let h=c.result;if(typeof h!="string")throw i=null,new Error("Wrong reader.result type");h=u?h:h.replace(/^data:[^;]*;/,"data:attachment/file;"),i?i.location.href=h:location.assign(h),i=null},c.readAsDataURL(e)}else{const c=URL.createObjectURL(e);i?i.location.assign(c):location.href=c,i=null,setTimeout(function(){URL.revokeObjectURL(c)},4e4)}}function Pt(e,t){const s="🍍 "+e;typeof __VUE_DEVTOOLS_TOAST__=="function"?__VUE_DEVTOOLS_TOAST__(s,t):t==="error"?console.error(s):t==="warn"?console.warn(s):console.log(s)}function Xl(e){return"_a"in e&&"install"in e}function uh(){if(!("clipboard"in navigator))return Pt("Your browser doesn't support the Clipboard API","error"),!0}function ch(e){return e instanceof Error&&e.message.toLowerCase().includes("document is not focused")?(Pt('You need to activate the "Emulate a focused page" setting in the "Rendering" panel of devtools.',"warn"),!0):!1}async function db(e){if(!uh())try{await navigator.clipboard.writeText(JSON.stringify(e.state.value)),Pt("Global state copied to clipboard.")}catch(t){if(ch(t))return;Pt("Failed to serialize the state. Check the console for more details.","error"),console.error(t)}}async function fb(e){if(!uh())try{dh(e,JSON.parse(await navigator.clipboard.readText())),Pt("Global state pasted from clipboard.")}catch(t){if(ch(t))return;Pt("Failed to deserialize the state from clipboard. Check the console for more details.","error"),console.error(t)}}async function hb(e){try{lh(new Blob([JSON.stringify(e.state.value)],{type:"text/plain;charset=utf-8"}),"pinia-state.json")}catch(t){Pt("Failed to export the state as JSON. Check the console for more details.","error"),console.error(t)}}let fr;function pb(){fr||(fr=document.createElement("input"),fr.type="file",fr.accept=".json");function e(){return new Promise((t,s)=>{fr.onchange=async()=>{const i=fr.files;if(!i)return t(null);const n=i.item(0);return t(n?{text:await n.text(),file:n}:null)},fr.oncancel=()=>t(null),fr.onerror=s,fr.click()})}return e}async function mb(e){try{const s=await pb()();if(!s)return;const{text:i,file:n}=s;dh(e,JSON.parse(i)),Pt(`Global state imported from "${n.name}".`)}catch(t){Pt("Failed to import the state from JSON. Check the console for more details.","error"),console.error(t)}}function dh(e,t){for(const s in t){const i=e.state.value[s];i?Object.assign(i,t[s]):e.state.value[s]=t[s]}}function Ls(e){return{_custom:{display:e}}}const fh="🍍 Pinia (root)",qi="_root";function gb(e){return Xl(e)?{id:qi,label:fh}:{id:e.$id,label:e.$id}}function vb(e){if(Xl(e)){const s=Array.from(e._s.keys()),i=e._s;return{state:s.map(a=>({editable:!0,key:a,value:e.state.value[a]})),getters:s.filter(a=>i.get(a)._getters).map(a=>{const u=i.get(a);return{editable:!1,key:a,value:u._getters.reduce((c,h)=>(c[h]=u[h],c),{})}})}}const t={state:Object.keys(e.$state).map(s=>({editable:!0,key:s,value:e.$state[s]}))};return e._getters&&e._getters.length&&(t.getters=e._getters.map(s=>({editable:!1,key:s,value:e[s]}))),e._customProperties.size&&(t.customProperties=Array.from(e._customProperties).map(s=>({editable:!0,key:s,value:e[s]}))),t}function _b(e){return e?Array.isArray(e)?e.reduce((t,s)=>(t.keys.push(s.key),t.operations.push(s.type),t.oldValue[s.key]=s.oldValue,t.newValue[s.key]=s.newValue,t),{oldValue:{},keys:[],operations:[],newValue:{}}):{operation:Ls(e.type),key:Ls(e.key),oldValue:e.oldValue,newValue:e.newValue}:{}}function yb(e){switch(e){case dn.direct:return"mutation";case dn.patchFunction:return"$patch";case dn.patchObject:return"$patch";default:return"unknown"}}let Rn=!0;const zi=[],fn="pinia:mutations",qt="pinia",{assign:bb}=Object,Wi=e=>"🍍 "+e;function wb(e,t){Ql({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:zi,app:e},s=>{typeof s.now!="function"&&Pt("You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),s.addTimelineLayer({id:fn,label:"Pinia 🍍",color:15064968}),s.addInspector({id:qt,label:"Pinia 🍍",icon:"storage",treeFilterPlaceholder:"Search stores",actions:[{icon:"content_copy",action:()=>{db(t)},tooltip:"Serialize and copy the state"},{icon:"content_paste",action:async()=>{await fb(t),s.sendInspectorTree(qt),s.sendInspectorState(qt)},tooltip:"Replace the state with the content of your clipboard"},{icon:"save",action:()=>{hb(t)},tooltip:"Save the state as a JSON file"},{icon:"folder_open",action:async()=>{await mb(t),s.sendInspectorTree(qt),s.sendInspectorState(qt)},tooltip:"Import the state from a JSON file"}],nodeActions:[{icon:"restore",tooltip:'Reset the state (with "$reset")',action:i=>{const n=t._s.get(i);n?typeof n.$reset!="function"?Pt(`Cannot reset "${i}" store because it doesn't have a "$reset" method implemented.`,"warn"):(n.$reset(),Pt(`Store "${i}" reset.`)):Pt(`Cannot reset "${i}" store because it wasn't found.`,"warn")}}]}),s.on.inspectComponent((i,n)=>{const a=i.componentInstance&&i.componentInstance.proxy;if(a&&a._pStores){const u=i.componentInstance.proxy._pStores;Object.values(u).forEach(c=>{i.instanceData.state.push({type:Wi(c.$id),key:"state",editable:!0,value:c._isOptionsAPI?{_custom:{value:Me(c.$state),actions:[{icon:"restore",tooltip:"Reset the state of this store",action:()=>c.$reset()}]}}:Object.keys(c.$state).reduce((h,m)=>(h[m]=c.$state[m],h),{})}),c._getters&&c._getters.length&&i.instanceData.state.push({type:Wi(c.$id),key:"getters",editable:!1,value:c._getters.reduce((h,m)=>{try{h[m]=c[m]}catch(p){h[m]=p}return h},{})})})}}),s.on.getInspectorTree(i=>{if(i.app===e&&i.inspectorId===qt){let n=[t];n=n.concat(Array.from(t._s.values())),i.rootNodes=(i.filter?n.filter(a=>"$id"in a?a.$id.toLowerCase().includes(i.filter.toLowerCase()):fh.toLowerCase().includes(i.filter.toLowerCase())):n).map(gb)}}),globalThis.$pinia=t,s.on.getInspectorState(i=>{if(i.app===e&&i.inspectorId===qt){const n=i.nodeId===qi?t:t._s.get(i.nodeId);if(!n)return;n&&(i.nodeId!==qi&&(globalThis.$store=Me(n)),i.state=vb(n))}}),s.on.editInspectorState((i,n)=>{if(i.app===e&&i.inspectorId===qt){const a=i.nodeId===qi?t:t._s.get(i.nodeId);if(!a)return Pt(`store "${i.nodeId}" not found`,"error");const{path:u}=i;Xl(a)?u.unshift("state"):(u.length!==1||!a._customProperties.has(u[0])||u[0]in a.$state)&&u.unshift("$state"),Rn=!1,i.set(a,u,i.state.value),Rn=!0}}),s.on.editComponentState(i=>{if(i.type.startsWith("🍍")){const n=i.type.replace(/^🍍\s*/,""),a=t._s.get(n);if(!a)return Pt(`store "${n}" not found`,"error");const{path:u}=i;if(u[0]!=="state")return Pt(`Invalid path for store "${n}":
${u}
Only state can be modified.`);u[0]="$state",Rn=!1,i.set(a,u,i.state.value),Rn=!0}})})}function Eb(e,t){zi.includes(Wi(t.$id))||zi.push(Wi(t.$id)),Ql({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:zi,app:e,settings:{logStoreChanges:{label:"Notify about new/deleted stores",type:"boolean",defaultValue:!0}}},s=>{const i=typeof s.now=="function"?s.now.bind(s):Date.now;t.$onAction(({after:u,onError:c,name:h,args:m})=>{const p=hh++;s.addTimelineEvent({layerId:fn,event:{time:i(),title:"🛫 "+h,subtitle:"start",data:{store:Ls(t.$id),action:Ls(h),args:m},groupId:p}}),u(v=>{Vr=void 0,s.addTimelineEvent({layerId:fn,event:{time:i(),title:"🛬 "+h,subtitle:"end",data:{store:Ls(t.$id),action:Ls(h),args:m,result:v},groupId:p}})}),c(v=>{Vr=void 0,s.addTimelineEvent({layerId:fn,event:{time:i(),logType:"error",title:"💥 "+h,subtitle:"end",data:{store:Ls(t.$id),action:Ls(h),args:m,error:v},groupId:p}})})},!0),t._customProperties.forEach(u=>{Pn(()=>Tr(t[u]),(c,h)=>{s.notifyComponentUpdate(),s.sendInspectorState(qt),Rn&&s.addTimelineEvent({layerId:fn,event:{time:i(),title:"Change",subtitle:u,data:{newValue:c,oldValue:h},groupId:Vr}})},{deep:!0})}),t.$subscribe(({events:u,type:c},h)=>{if(s.notifyComponentUpdate(),s.sendInspectorState(qt),!Rn)return;const m={time:i(),title:yb(c),data:bb({store:Ls(t.$id)},_b(u)),groupId:Vr};c===dn.patchFunction?m.subtitle="⤵️":c===dn.patchObject?m.subtitle="🧩":u&&!Array.isArray(u)&&(m.subtitle=u.type),u&&(m.data["rawEvent(s)"]={_custom:{display:"DebuggerEvent",type:"object",tooltip:"raw DebuggerEvent[]",value:u}}),s.addTimelineEvent({layerId:fn,event:m})},{detached:!0,flush:"sync"});const n=t._hotUpdate;t._hotUpdate=cl(u=>{n(u),s.addTimelineEvent({layerId:fn,event:{time:i(),title:"🔥 "+t.$id,subtitle:"HMR update",data:{store:Ls(t.$id),info:Ls("HMR update")}}}),s.notifyComponentUpdate(),s.sendInspectorTree(qt),s.sendInspectorState(qt)});const{$dispose:a}=t;t.$dispose=()=>{a(),s.notifyComponentUpdate(),s.sendInspectorTree(qt),s.sendInspectorState(qt),s.getSettings().logStoreChanges&&Pt(`Disposed "${t.$id}" store 🗑`)},s.notifyComponentUpdate(),s.sendInspectorTree(qt),s.sendInspectorState(qt),s.getSettings().logStoreChanges&&Pt(`"${t.$id}" store installed 🆕`)})}let hh=0,Vr;function ph(e,t,s){const i=t.reduce((n,a)=>(n[a]=Me(e)[a],n),{});for(const n in i)e[n]=function(){const a=hh,u=s?new Proxy(e,{get(...h){return Vr=a,Reflect.get(...h)},set(...h){return Vr=a,Reflect.set(...h)}}):e;Vr=a;const c=i[n].apply(u,arguments);return Vr=void 0,c}}function Cb({app:e,store:t,options:s}){if(!t.$id.startsWith("__hot:")){if(t._isOptionsAPI=!!s.state,!t._p._testing){ph(t,Object.keys(s.actions),t._isOptionsAPI);const i=t._hotUpdate;Me(t)._hotUpdate=function(n){i.apply(this,arguments),ph(t,Object.keys(n._hmrPayload.actions),!!t._isOptionsAPI)}}Eb(e,t)}}function Db(){const e=rv(!0),t=e.run(()=>ad({}));let s=[],i=[];const n=cl({install(a){n._a=a,a.provide(ib,n),a.config.globalProperties.$pinia=n,{}.NODE_ENV!=="production"&&{}.NODE_ENV!=="test"&&Zl&&wb(a,n),i.forEach(u=>s.push(u)),i=[]},use(a){return!this._a&&!Jy?i.push(a):s.push(a),this},_p:s,_a:null,_e:e,_s:new Map,state:t});return{}.NODE_ENV!=="production"&&{}.NODE_ENV!=="test"&&Zl&&typeof Proxy<"u"&&n.use(Cb),n}const pR="",He=(e,t)=>{const s=e.__vccOpts||e;for(const[i,n]of t)s[i]=n;return s},xb={name:"App",mounted(){if(!document.querySelector('link[href*="font-awesome"]')){const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)}}},Ob={id:"app"};function Sb(e,t,s,i,n,a){const u=te("router-view");return O(),S("div",Ob,[k(u)])}const Tb=He(xb,[["render",Sb]]);/*!
  * vue-router v4.5.0
  * (c) 2024 Eduardo San Martin Morote
  * @license MIT
  */const hr=typeof document<"u";function mh(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Nb(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&mh(e.default)}const Xe=Object.assign;function eu(e,t){const s={};for(const i in t){const n=t[i];s[i]=fs(n)?n.map(e):e(n)}return s}const Ao=()=>{},fs=Array.isArray;function $e(e){const t=Array.from(arguments).slice(1);console.warn.apply(console,["[Vue Router warn]: "+e].concat(t))}const gh=/#/g,Ib=/&/g,Ab=/\//g,Mb=/=/g,Pb=/\?/g,vh=/\+/g,kb=/%5B/g,Vb=/%5D/g,_h=/%5E/g,Rb=/%60/g,yh=/%7B/g,Ub=/%7C/g,bh=/%7D/g,Lb=/%20/g;function tu(e){return encodeURI(""+e).replace(Ub,"|").replace(kb,"[").replace(Vb,"]")}function Fb(e){return tu(e).replace(yh,"{").replace(bh,"}").replace(_h,"^")}function su(e){return tu(e).replace(vh,"%2B").replace(Lb,"+").replace(gh,"%23").replace(Ib,"%26").replace(Rb,"`").replace(yh,"{").replace(bh,"}").replace(_h,"^")}function Bb(e){return su(e).replace(Mb,"%3D")}function $b(e){return tu(e).replace(gh,"%23").replace(Pb,"%3F")}function jb(e){return e==null?"":$b(e).replace(Ab,"%2F")}function Un(e){try{return decodeURIComponent(""+e)}catch{({}).NODE_ENV!=="production"&&$e(`Error decoding "${e}". Using original value`)}return""+e}const Hb=/\/$/,qb=e=>e.replace(Hb,"");function ru(e,t,s="/"){let i,n={},a="",u="";const c=t.indexOf("#");let h=t.indexOf("?");return c<h&&c>=0&&(h=-1),h>-1&&(i=t.slice(0,h),a=t.slice(h+1,c>-1?c:t.length),n=e(a)),c>-1&&(i=i||t.slice(0,c),u=t.slice(c,t.length)),i=Gb(i??t,s),{fullPath:i+(a&&"?")+a+u,path:i,query:n,hash:Un(u)}}function zb(e,t){const s=t.query?e(t.query):"";return t.path+(s&&"?")+s+(t.hash||"")}function wh(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Eh(e,t,s){const i=t.matched.length-1,n=s.matched.length-1;return i>-1&&i===n&&Rr(t.matched[i],s.matched[n])&&Ch(t.params,s.params)&&e(t.query)===e(s.query)&&t.hash===s.hash}function Rr(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Ch(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(!Wb(e[s],t[s]))return!1;return!0}function Wb(e,t){return fs(e)?Dh(e,t):fs(t)?Dh(t,e):e===t}function Dh(e,t){return fs(t)?e.length===t.length&&e.every((s,i)=>s===t[i]):e.length===1&&e[0]===t}function Gb(e,t){if(e.startsWith("/"))return e;if({}.NODE_ENV!=="production"&&!t.startsWith("/"))return $e(`Cannot resolve a relative location without an absolute path. Trying to resolve "${e}" from "${t}". It should look like "/${t}".`),e;if(!e)return t;const s=t.split("/"),i=e.split("/"),n=i[i.length-1];(n===".."||n===".")&&i.push("");let a=s.length-1,u,c;for(u=0;u<i.length;u++)if(c=i[u],c!==".")if(c==="..")a>1&&a--;else break;return s.slice(0,a).join("/")+"/"+i.slice(u).join("/")}const Ur={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Mo;(function(e){e.pop="pop",e.push="push"})(Mo||(Mo={}));var Po;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Po||(Po={}));function Kb(e){if(!e)if(hr){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),qb(e)}const Yb=/^[^#]+#/;function Qb(e,t){return e.replace(Yb,"#")+t}function Zb(e,t){const s=document.documentElement.getBoundingClientRect(),i=e.getBoundingClientRect();return{behavior:t.behavior,left:i.left-s.left-(t.left||0),top:i.top-s.top-(t.top||0)}}const Gi=()=>({left:window.scrollX,top:window.scrollY});function Jb(e){let t;if("el"in e){const s=e.el,i=typeof s=="string"&&s.startsWith("#");if({}.NODE_ENV!=="production"&&typeof e.el=="string"&&(!i||!document.getElementById(e.el.slice(1))))try{const a=document.querySelector(e.el);if(i&&a){$e(`The selector "${e.el}" should be passed as "el: document.querySelector('${e.el}')" because it starts with "#".`);return}}catch{$e(`The selector "${e.el}" is invalid. If you are using an id selector, make sure to escape it. You can find more information about escaping characters in selectors at https://mathiasbynens.be/notes/css-escapes or use CSS.escape (https://developer.mozilla.org/en-US/docs/Web/API/CSS/escape).`);return}const n=typeof s=="string"?i?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!n){({}).NODE_ENV!=="production"&&$e(`Couldn't find element using selector "${e.el}" returned by scrollBehavior.`);return}t=Zb(n,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function xh(e,t){return(history.state?history.state.position-t:-1)+e}const nu=new Map;function Xb(e,t){nu.set(e,t)}function ew(e){const t=nu.get(e);return nu.delete(e),t}let tw=()=>location.protocol+"//"+location.host;function Oh(e,t){const{pathname:s,search:i,hash:n}=t,a=e.indexOf("#");if(a>-1){let c=n.includes(e.slice(a))?e.slice(a).length:1,h=n.slice(c);return h[0]!=="/"&&(h="/"+h),wh(h,"")}return wh(s,e)+i+n}function sw(e,t,s,i){let n=[],a=[],u=null;const c=({state:w})=>{const D=Oh(e,location),V=s.value,F=t.value;let se=0;if(w){if(s.value=D,t.value=w,u&&u===V){u=null;return}se=F?w.position-F.position:0}else i(D);n.forEach(A=>{A(s.value,V,{delta:se,type:Mo.pop,direction:se?se>0?Po.forward:Po.back:Po.unknown})})};function h(){u=s.value}function m(w){n.push(w);const D=()=>{const V=n.indexOf(w);V>-1&&n.splice(V,1)};return a.push(D),D}function p(){const{history:w}=window;w.state&&w.replaceState(Xe({},w.state,{scroll:Gi()}),"")}function v(){for(const w of a)w();a=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",p)}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",p,{passive:!0}),{pauseListeners:h,listen:m,destroy:v}}function Sh(e,t,s,i=!1,n=!1){return{back:e,current:t,forward:s,replaced:i,position:window.history.length,scroll:n?Gi():null}}function rw(e){const{history:t,location:s}=window,i={value:Oh(e,s)},n={value:t.state};n.value||a(i.value,{back:null,current:i.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function a(h,m,p){const v=e.indexOf("#"),w=v>-1?(s.host&&document.querySelector("base")?e:e.slice(v))+h:tw()+e+h;try{t[p?"replaceState":"pushState"](m,"",w),n.value=m}catch(D){({}).NODE_ENV!=="production"?$e("Error with push/replace State",D):console.error(D),s[p?"replace":"assign"](w)}}function u(h,m){const p=Xe({},t.state,Sh(n.value.back,h,n.value.forward,!0),m,{position:n.value.position});a(h,p,!0),i.value=h}function c(h,m){const p=Xe({},n.value,t.state,{forward:h,scroll:Gi()});({}).NODE_ENV!=="production"&&!t.state&&$e(`history.state seems to have been manually replaced without preserving the necessary values. Make sure to preserve existing history state if you are manually calling history.replaceState:

history.replaceState(history.state, '', url)

You can find more information at https://router.vuejs.org/guide/migration/#Usage-of-history-state`),a(p.current,p,!0);const v=Xe({},Sh(i.value,h,null),{position:p.position+1},m);a(h,v,!1),i.value=h}return{location:i,state:n,push:c,replace:u}}function nw(e){e=Kb(e);const t=rw(e),s=sw(e,t.state,t.location,t.replace);function i(a,u=!0){u||s.pauseListeners(),history.go(a)}const n=Xe({location:"",base:e,go:i,createHref:Qb.bind(null,e)},t,s);return Object.defineProperty(n,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(n,"state",{enumerable:!0,get:()=>t.state.value}),n}function Ki(e){return typeof e=="string"||e&&typeof e=="object"}function Th(e){return typeof e=="string"||typeof e=="symbol"}const ou=Symbol({}.NODE_ENV!=="production"?"navigation failure":"");var Nh;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(Nh||(Nh={}));const ow={1({location:e,currentLocation:t}){return`No match for
 ${JSON.stringify(e)}${t?`
while being at
`+JSON.stringify(t):""}`},2({from:e,to:t}){return`Redirected from "${e.fullPath}" to "${aw(t)}" via a navigation guard.`},4({from:e,to:t}){return`Navigation aborted from "${e.fullPath}" to "${t.fullPath}" via a navigation guard.`},8({from:e,to:t}){return`Navigation cancelled from "${e.fullPath}" to "${t.fullPath}" with a new navigation.`},16({from:e,to:t}){return`Avoided redundant navigation to current location: "${e.fullPath}".`}};function Ln(e,t){return{}.NODE_ENV!=="production"?Xe(new Error(ow[e](t)),{type:e,[ou]:!0},t):Xe(new Error,{type:e,[ou]:!0},t)}function pr(e,t){return e instanceof Error&&ou in e&&(t==null||!!(e.type&t))}const iw=["params","query","hash"];function aw(e){if(typeof e=="string")return e;if(e.path!=null)return e.path;const t={};for(const s of iw)s in e&&(t[s]=e[s]);return JSON.stringify(t,null,2)}const Ih="[^/]+?",lw={sensitive:!1,strict:!1,start:!0,end:!0},uw=/[.+*?^${}()[\]/\\]/g;function cw(e,t){const s=Xe({},lw,t),i=[];let n=s.start?"^":"";const a=[];for(const m of e){const p=m.length?[]:[90];s.strict&&!m.length&&(n+="/");for(let v=0;v<m.length;v++){const w=m[v];let D=40+(s.sensitive?.25:0);if(w.type===0)v||(n+="/"),n+=w.value.replace(uw,"\\$&"),D+=40;else if(w.type===1){const{value:V,repeatable:F,optional:se,regexp:A}=w;a.push({name:V,repeatable:F,optional:se});const re=A||Ih;if(re!==Ih){D+=10;try{new RegExp(`(${re})`)}catch(ye){throw new Error(`Invalid custom RegExp for param "${V}" (${re}): `+ye.message)}}let Y=F?`((?:${re})(?:/(?:${re}))*)`:`(${re})`;v||(Y=se&&m.length<2?`(?:/${Y})`:"/"+Y),se&&(Y+="?"),n+=Y,D+=20,se&&(D+=-8),F&&(D+=-20),re===".*"&&(D+=-50)}p.push(D)}i.push(p)}if(s.strict&&s.end){const m=i.length-1;i[m][i[m].length-1]+=.7000000000000001}s.strict||(n+="/?"),s.end?n+="$":s.strict&&!n.endsWith("/")&&(n+="(?:/|$)");const u=new RegExp(n,s.sensitive?"":"i");function c(m){const p=m.match(u),v={};if(!p)return null;for(let w=1;w<p.length;w++){const D=p[w]||"",V=a[w-1];v[V.name]=D&&V.repeatable?D.split("/"):D}return v}function h(m){let p="",v=!1;for(const w of e){(!v||!p.endsWith("/"))&&(p+="/"),v=!1;for(const D of w)if(D.type===0)p+=D.value;else if(D.type===1){const{value:V,repeatable:F,optional:se}=D,A=V in m?m[V]:"";if(fs(A)&&!F)throw new Error(`Provided param "${V}" is an array but it is not repeatable (* or + modifiers)`);const re=fs(A)?A.join("/"):A;if(!re)if(se)w.length<2&&(p.endsWith("/")?p=p.slice(0,-1):v=!0);else throw new Error(`Missing required param "${V}"`);p+=re}}return p||"/"}return{re:u,score:i,keys:a,parse:c,stringify:h}}function dw(e,t){let s=0;for(;s<e.length&&s<t.length;){const i=t[s]-e[s];if(i)return i;s++}return e.length<t.length?e.length===1&&e[0]===40+40?-1:1:e.length>t.length?t.length===1&&t[0]===40+40?1:-1:0}function Ah(e,t){let s=0;const i=e.score,n=t.score;for(;s<i.length&&s<n.length;){const a=dw(i[s],n[s]);if(a)return a;s++}if(Math.abs(n.length-i.length)===1){if(Mh(i))return 1;if(Mh(n))return-1}return n.length-i.length}function Mh(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const fw={type:0,value:""},hw=/[a-zA-Z0-9_]/;function pw(e){if(!e)return[[]];if(e==="/")return[[fw]];if(!e.startsWith("/"))throw new Error({}.NODE_ENV!=="production"?`Route paths should start with a "/": "${e}" should be "/${e}".`:`Invalid path "${e}"`);function t(D){throw new Error(`ERR (${s})/"${m}": ${D}`)}let s=0,i=s;const n=[];let a;function u(){a&&n.push(a),a=[]}let c=0,h,m="",p="";function v(){m&&(s===0?a.push({type:0,value:m}):s===1||s===2||s===3?(a.length>1&&(h==="*"||h==="+")&&t(`A repeatable param (${m}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:m,regexp:p,repeatable:h==="*"||h==="+",optional:h==="*"||h==="?"})):t("Invalid state to consume buffer"),m="")}function w(){m+=h}for(;c<e.length;){if(h=e[c++],h==="\\"&&s!==2){i=s,s=4;continue}switch(s){case 0:h==="/"?(m&&v(),u()):h===":"?(v(),s=1):w();break;case 4:w(),s=i;break;case 1:h==="("?s=2:hw.test(h)?w():(v(),s=0,h!=="*"&&h!=="?"&&h!=="+"&&c--);break;case 2:h===")"?p[p.length-1]=="\\"?p=p.slice(0,-1)+h:s=3:p+=h;break;case 3:v(),s=0,h!=="*"&&h!=="?"&&h!=="+"&&c--,p="";break;default:t("Unknown state");break}}return s===2&&t(`Unfinished custom RegExp for param "${m}"`),v(),u(),n}function mw(e,t,s){const i=cw(pw(e.path),s);if({}.NODE_ENV!=="production"){const a=new Set;for(const u of i.keys)a.has(u.name)&&$e(`Found duplicated params with name "${u.name}" for path "${e.path}". Only the last one will be available on "$route.params".`),a.add(u.name)}const n=Xe(i,{record:e,parent:t,children:[],alias:[]});return t&&!n.record.aliasOf==!t.record.aliasOf&&t.children.push(n),n}function gw(e,t){const s=[],i=new Map;t=Rh({strict:!1,end:!0,sensitive:!1},t);function n(v){return i.get(v)}function a(v,w,D){const V=!D,F=kh(v);({}).NODE_ENV!=="production"&&bw(F,w),F.aliasOf=D&&D.record;const se=Rh(t,v),A=[F];if("alias"in v){const ye=typeof v.alias=="string"?[v.alias]:v.alias;for(const Z of ye)A.push(kh(Xe({},F,{components:D?D.record.components:F.components,path:Z,aliasOf:D?D.record:F})))}let re,Y;for(const ye of A){const{path:Z}=ye;if(w&&Z[0]!=="/"){const fe=w.record.path,_e=fe[fe.length-1]==="/"?"":"/";ye.path=w.record.path+(Z&&_e+Z)}if({}.NODE_ENV!=="production"&&ye.path==="*")throw new Error(`Catch all routes ("*") must now be defined using a param with a custom regexp.
See more at https://router.vuejs.org/guide/migration/#Removed-star-or-catch-all-routes.`);if(re=mw(ye,w,se),{}.NODE_ENV!=="production"&&w&&Z[0]==="/"&&Ew(re,w),D?(D.alias.push(re),{}.NODE_ENV!=="production"&&yw(D,re)):(Y=Y||re,Y!==re&&Y.alias.push(re),V&&v.name&&!Vh(re)&&({}.NODE_ENV!=="production"&&ww(v,w),u(v.name))),Uh(re)&&h(re),F.children){const fe=F.children;for(let _e=0;_e<fe.length;_e++)a(fe[_e],re,D&&D.children[_e])}D=D||re}return Y?()=>{u(Y)}:Ao}function u(v){if(Th(v)){const w=i.get(v);w&&(i.delete(v),s.splice(s.indexOf(w),1),w.children.forEach(u),w.alias.forEach(u))}else{const w=s.indexOf(v);w>-1&&(s.splice(w,1),v.record.name&&i.delete(v.record.name),v.children.forEach(u),v.alias.forEach(u))}}function c(){return s}function h(v){const w=Cw(v,s);s.splice(w,0,v),v.record.name&&!Vh(v)&&i.set(v.record.name,v)}function m(v,w){let D,V={},F,se;if("name"in v&&v.name){if(D=i.get(v.name),!D)throw Ln(1,{location:v});if({}.NODE_ENV!=="production"){const Y=Object.keys(v.params||{}).filter(ye=>!D.keys.find(Z=>Z.name===ye));Y.length&&$e(`Discarded invalid param(s) "${Y.join('", "')}" when navigating. See https://github.com/vuejs/router/blob/main/packages/router/CHANGELOG.md#414-2022-08-22 for more details.`)}se=D.record.name,V=Xe(Ph(w.params,D.keys.filter(Y=>!Y.optional).concat(D.parent?D.parent.keys.filter(Y=>Y.optional):[]).map(Y=>Y.name)),v.params&&Ph(v.params,D.keys.map(Y=>Y.name))),F=D.stringify(V)}else if(v.path!=null)F=v.path,{}.NODE_ENV!=="production"&&!F.startsWith("/")&&$e(`The Matcher cannot resolve relative paths but received "${F}". Unless you directly called \`matcher.resolve("${F}")\`, this is probably a bug in vue-router. Please open an issue at https://github.com/vuejs/router/issues/new/choose.`),D=s.find(Y=>Y.re.test(F)),D&&(V=D.parse(F),se=D.record.name);else{if(D=w.name?i.get(w.name):s.find(Y=>Y.re.test(w.path)),!D)throw Ln(1,{location:v,currentLocation:w});se=D.record.name,V=Xe({},w.params,v.params),F=D.stringify(V)}const A=[];let re=D;for(;re;)A.unshift(re.record),re=re.parent;return{name:se,path:F,params:V,matched:A,meta:_w(A)}}e.forEach(v=>a(v));function p(){s.length=0,i.clear()}return{addRoute:a,resolve:m,removeRoute:u,clearRoutes:p,getRoutes:c,getRecordMatcher:n}}function Ph(e,t){const s={};for(const i of t)i in e&&(s[i]=e[i]);return s}function kh(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:vw(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function vw(e){const t={},s=e.props||!1;if("component"in e)t.default=s;else for(const i in e.components)t[i]=typeof s=="object"?s[i]:s;return t}function Vh(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function _w(e){return e.reduce((t,s)=>Xe(t,s.meta),{})}function Rh(e,t){const s={};for(const i in e)s[i]=i in t?t[i]:e[i];return s}function iu(e,t){return e.name===t.name&&e.optional===t.optional&&e.repeatable===t.repeatable}function yw(e,t){for(const s of e.keys)if(!s.optional&&!t.keys.find(iu.bind(null,s)))return $e(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${s.name}"`);for(const s of t.keys)if(!s.optional&&!e.keys.find(iu.bind(null,s)))return $e(`Alias "${t.record.path}" and the original record: "${e.record.path}" must have the exact same param named "${s.name}"`)}function bw(e,t){t&&t.record.name&&!e.name&&!e.path&&$e(`The route named "${String(t.record.name)}" has a child without a name and an empty path. Using that name won't render the empty path child so you probably want to move the name to the child instead. If this is intentional, add a name to the child route to remove the warning.`)}function ww(e,t){for(let s=t;s;s=s.parent)if(s.record.name===e.name)throw new Error(`A route named "${String(e.name)}" has been added as a ${t===s?"child":"descendant"} of a route with the same name. Route names must be unique and a nested route cannot use the same name as an ancestor.`)}function Ew(e,t){for(const s of t.keys)if(!e.keys.find(iu.bind(null,s)))return $e(`Absolute path "${e.record.path}" must have the exact same param named "${s.name}" as its parent "${t.record.path}".`)}function Cw(e,t){let s=0,i=t.length;for(;s!==i;){const a=s+i>>1;Ah(e,t[a])<0?i=a:s=a+1}const n=Dw(e);return n&&(i=t.lastIndexOf(n,i-1),{}.NODE_ENV!=="production"&&i<0&&$e(`Finding ancestor route "${n.record.path}" failed for "${e.record.path}"`)),i}function Dw(e){let t=e;for(;t=t.parent;)if(Uh(t)&&Ah(e,t)===0)return t}function Uh({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function xw(e){const t={};if(e===""||e==="?")return t;const i=(e[0]==="?"?e.slice(1):e).split("&");for(let n=0;n<i.length;++n){const a=i[n].replace(vh," "),u=a.indexOf("="),c=Un(u<0?a:a.slice(0,u)),h=u<0?null:Un(a.slice(u+1));if(c in t){let m=t[c];fs(m)||(m=t[c]=[m]),m.push(h)}else t[c]=h}return t}function Lh(e){let t="";for(let s in e){const i=e[s];if(s=Bb(s),i==null){i!==void 0&&(t+=(t.length?"&":"")+s);continue}(fs(i)?i.map(a=>a&&su(a)):[i&&su(i)]).forEach(a=>{a!==void 0&&(t+=(t.length?"&":"")+s,a!=null&&(t+="="+a))})}return t}function Ow(e){const t={};for(const s in e){const i=e[s];i!==void 0&&(t[s]=fs(i)?i.map(n=>n==null?null:""+n):i==null?i:""+i)}return t}const Sw=Symbol({}.NODE_ENV!=="production"?"router view location matched":""),Fh=Symbol({}.NODE_ENV!=="production"?"router view depth":""),Yi=Symbol({}.NODE_ENV!=="production"?"router":""),au=Symbol({}.NODE_ENV!=="production"?"route location":""),lu=Symbol({}.NODE_ENV!=="production"?"router view location":"");function ko(){let e=[];function t(i){return e.push(i),()=>{const n=e.indexOf(i);n>-1&&e.splice(n,1)}}function s(){e=[]}return{add:t,list:()=>e.slice(),reset:s}}function Lr(e,t,s,i,n,a=u=>u()){const u=i&&(i.enterCallbacks[n]=i.enterCallbacks[n]||[]);return()=>new Promise((c,h)=>{const m=w=>{w===!1?h(Ln(4,{from:s,to:t})):w instanceof Error?h(w):Ki(w)?h(Ln(2,{from:t,to:w})):(u&&i.enterCallbacks[n]===u&&typeof w=="function"&&u.push(w),c())},p=a(()=>e.call(i&&i.instances[n],t,s,{}.NODE_ENV!=="production"?Tw(m,t,s):m));let v=Promise.resolve(p);if(e.length<3&&(v=v.then(m)),{}.NODE_ENV!=="production"&&e.length>2){const w=`The "next" callback was never called inside of ${e.name?'"'+e.name+'"':""}:
${e.toString()}
. If you are returning a value instead of calling "next", make sure to remove the "next" parameter from your function.`;if(typeof p=="object"&&"then"in p)v=v.then(D=>m._called?D:($e(w),Promise.reject(new Error("Invalid navigation guard"))));else if(p!==void 0&&!m._called){$e(w),h(new Error("Invalid navigation guard"));return}}v.catch(w=>h(w))})}function Tw(e,t,s){let i=0;return function(){i++===1&&$e(`The "next" callback was called more than once in one navigation guard when going from "${s.fullPath}" to "${t.fullPath}". It should be called exactly one time in each navigation guard. This will fail in production.`),e._called=!0,i===1&&e.apply(null,arguments)}}function uu(e,t,s,i,n=a=>a()){const a=[];for(const u of e){({}).NODE_ENV!=="production"&&!u.components&&!u.children.length&&$e(`Record with path "${u.path}" is either missing a "component(s)" or "children" property.`);for(const c in u.components){let h=u.components[c];if({}.NODE_ENV!=="production"){if(!h||typeof h!="object"&&typeof h!="function")throw $e(`Component "${c}" in record with path "${u.path}" is not a valid component. Received "${String(h)}".`),new Error("Invalid route component");if("then"in h){$e(`Component "${c}" in record with path "${u.path}" is a Promise instead of a function that returns a Promise. Did you write "import('./MyPage.vue')" instead of "() => import('./MyPage.vue')" ? This will break in production if not fixed.`);const m=h;h=()=>m}else h.__asyncLoader&&!h.__warnedDefineAsync&&(h.__warnedDefineAsync=!0,$e(`Component "${c}" in record with path "${u.path}" is defined using "defineAsyncComponent()". Write "() => import('./MyPage.vue')" instead of "defineAsyncComponent(() => import('./MyPage.vue'))".`))}if(!(t!=="beforeRouteEnter"&&!u.instances[c]))if(mh(h)){const p=(h.__vccOpts||h)[t];p&&a.push(Lr(p,s,i,u,c,n))}else{let m=h();({}).NODE_ENV!=="production"&&!("catch"in m)&&($e(`Component "${c}" in record with path "${u.path}" is a function that does not return a Promise. If you were passing a functional component, make sure to add a "displayName" to the component. This will break in production if not fixed.`),m=Promise.resolve(m)),a.push(()=>m.then(p=>{if(!p)throw new Error(`Couldn't resolve component "${c}" at "${u.path}"`);const v=Nb(p)?p.default:p;u.mods[c]=p,u.components[c]=v;const D=(v.__vccOpts||v)[t];return D&&Lr(D,s,i,u,c,n)()}))}}}return a}function Bh(e){const t=Vs(Yi),s=Vs(au);let i=!1,n=null;const a=Us(()=>{const p=Tr(e.to);return{}.NODE_ENV!=="production"&&(!i||p!==n)&&(Ki(p)||(i?$e(`Invalid value for prop "to" in useLink()
- to:`,p,`
- previous to:`,n,`
- props:`,e):$e(`Invalid value for prop "to" in useLink()
- to:`,p,`
- props:`,e)),n=p,i=!0),t.resolve(p)}),u=Us(()=>{const{matched:p}=a.value,{length:v}=p,w=p[v-1],D=s.matched;if(!w||!D.length)return-1;const V=D.findIndex(Rr.bind(null,w));if(V>-1)return V;const F=$h(p[v-2]);return v>1&&$h(w)===F&&D[D.length-1].path!==F?D.findIndex(Rr.bind(null,p[v-2])):V}),c=Us(()=>u.value>-1&&Mw(s.params,a.value.params)),h=Us(()=>u.value>-1&&u.value===s.matched.length-1&&Ch(s.params,a.value.params));function m(p={}){if(Aw(p)){const v=t[Tr(e.replace)?"replace":"push"](Tr(e.to)).catch(Ao);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>v),v}return Promise.resolve()}if({}.NODE_ENV!=="production"&&hr){const p=Vi();if(p){const v={route:a.value,isActive:c.value,isExactActive:h.value,error:null};p.__vrl_devtools=p.__vrl_devtools||[],p.__vrl_devtools.push(v),W_(()=>{v.route=a.value,v.isActive=c.value,v.isExactActive=h.value,v.error=Ki(Tr(e.to))?null:'Invalid "to" value'},{flush:"post"})}}return{route:a,href:Us(()=>a.value.href),isActive:c,isExactActive:h,navigate:m}}function Nw(e){return e.length===1?e[0]:e}const Iw=Ud({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"}},useLink:Bh,setup(e,{slots:t}){const s=fi(Bh(e)),{options:i}=Vs(Yi),n=Us(()=>({[jh(e.activeClass,i.linkActiveClass,"router-link-active")]:s.isActive,[jh(e.exactActiveClass,i.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const a=t.default&&Nw(t.default(s));return e.custom?a:jl("a",{"aria-current":s.isExactActive?e.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:n.value},a)}}});function Aw(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Mw(e,t){for(const s in t){const i=t[s],n=e[s];if(typeof i=="string"){if(i!==n)return!1}else if(!fs(n)||n.length!==i.length||i.some((a,u)=>a!==n[u]))return!1}return!0}function $h(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const jh=(e,t,s)=>e??t??s,Pw=Ud({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:s}){({}).NODE_ENV!=="production"&&Vw();const i=Vs(lu),n=Us(()=>e.route||i.value),a=Vs(Fh,0),u=Us(()=>{let m=Tr(a);const{matched:p}=n.value;let v;for(;(v=p[m])&&!v.components;)m++;return m}),c=Us(()=>n.value.matched[u.value]);Ii(Fh,Us(()=>u.value+1)),Ii(Sw,c),Ii(lu,n);const h=ad();return Pn(()=>[h.value,c.value,e.name],([m,p,v],[w,D,V])=>{p&&(p.instances[v]=m,D&&D!==p&&m&&m===w&&(p.leaveGuards.size||(p.leaveGuards=D.leaveGuards),p.updateGuards.size||(p.updateGuards=D.updateGuards))),m&&p&&(!D||!Rr(p,D)||!w)&&(p.enterCallbacks[v]||[]).forEach(F=>F(m))},{flush:"post"}),()=>{const m=n.value,p=e.name,v=c.value,w=v&&v.components[p];if(!w)return Hh(s.default,{Component:w,route:m});const D=v.props[p],V=D?D===!0?m.params:typeof D=="function"?D(m):D:null,se=jl(w,Xe({},V,t,{onVnodeUnmounted:A=>{A.component.isUnmounted&&(v.instances[p]=null)},ref:h}));if({}.NODE_ENV!=="production"&&hr&&se.ref){const A={depth:u.value,name:v.name,path:v.path,meta:v.meta};(fs(se.ref)?se.ref.map(Y=>Y.i):[se.ref.i]).forEach(Y=>{Y.__vrv_devtools=A})}return Hh(s.default,{Component:se,route:m})||se}}});function Hh(e,t){if(!e)return null;const s=e(t);return s.length===1?s[0]:s}const kw=Pw;function Vw(){const e=Vi(),t=e.parent&&e.parent.type.name,s=e.parent&&e.parent.subTree&&e.parent.subTree.type;if(t&&(t==="KeepAlive"||t.includes("Transition"))&&typeof s=="object"&&s.name==="RouterView"){const i=t==="KeepAlive"?"keep-alive":"transition";$e(`<router-view> can no longer be used directly inside <transition> or <keep-alive>.
Use slot props instead:

<router-view v-slot="{ Component }">
  <${i}>
    <component :is="Component" />
  </${i}>
</router-view>`)}}function Vo(e,t){const s=Xe({},e,{matched:e.matched.map(i=>Ww(i,["instances","children","aliasOf"]))});return{_custom:{type:null,readOnly:!0,display:e.fullPath,tooltip:t,value:s}}}function Qi(e){return{_custom:{display:e}}}let Rw=0;function Uw(e,t,s){if(t.__hasDevtools)return;t.__hasDevtools=!0;const i=Rw++;Ql({id:"org.vuejs.router"+(i?"."+i:""),label:"Vue Router",packageName:"vue-router",homepage:"https://router.vuejs.org",logo:"https://router.vuejs.org/logo.png",componentStateTypes:["Routing"],app:e},n=>{typeof n.now!="function"&&console.warn("[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),n.on.inspectComponent((p,v)=>{p.instanceData&&p.instanceData.state.push({type:"Routing",key:"$route",editable:!1,value:Vo(t.currentRoute.value,"Current Route")})}),n.on.visitComponentTree(({treeNode:p,componentInstance:v})=>{if(v.__vrv_devtools){const w=v.__vrv_devtools;p.tags.push({label:(w.name?`${w.name.toString()}: `:"")+w.path,textColor:0,tooltip:"This component is rendered by &lt;router-view&gt;",backgroundColor:qh})}fs(v.__vrl_devtools)&&(v.__devtoolsApi=n,v.__vrl_devtools.forEach(w=>{let D=w.route.path,V=Gh,F="",se=0;w.error?(D=w.error,V=jw,se=Hw):w.isExactActive?(V=Wh,F="This is exactly active"):w.isActive&&(V=zh,F="This link is active"),p.tags.push({label:D,textColor:se,tooltip:F,backgroundColor:V})}))}),Pn(t.currentRoute,()=>{h(),n.notifyComponentUpdate(),n.sendInspectorTree(c),n.sendInspectorState(c)});const a="router:navigations:"+i;n.addTimelineLayer({id:a,label:`Router${i?" "+i:""} Navigations`,color:4237508}),t.onError((p,v)=>{n.addTimelineEvent({layerId:a,event:{title:"Error during Navigation",subtitle:v.fullPath,logType:"error",time:n.now(),data:{error:p},groupId:v.meta.__navigationId}})});let u=0;t.beforeEach((p,v)=>{const w={guard:Qi("beforeEach"),from:Vo(v,"Current Location during this navigation"),to:Vo(p,"Target location")};Object.defineProperty(p.meta,"__navigationId",{value:u++}),n.addTimelineEvent({layerId:a,event:{time:n.now(),title:"Start of navigation",subtitle:p.fullPath,data:w,groupId:p.meta.__navigationId}})}),t.afterEach((p,v,w)=>{const D={guard:Qi("afterEach")};w?(D.failure={_custom:{type:Error,readOnly:!0,display:w?w.message:"",tooltip:"Navigation Failure",value:w}},D.status=Qi("❌")):D.status=Qi("✅"),D.from=Vo(v,"Current Location during this navigation"),D.to=Vo(p,"Target location"),n.addTimelineEvent({layerId:a,event:{title:"End of navigation",subtitle:p.fullPath,time:n.now(),data:D,logType:w?"warning":"default",groupId:p.meta.__navigationId}})});const c="router-inspector:"+i;n.addInspector({id:c,label:"Routes"+(i?" "+i:""),icon:"book",treeFilterPlaceholder:"Search routes"});function h(){if(!m)return;const p=m;let v=s.getRoutes().filter(w=>!w.parent||!w.parent.record.components);v.forEach(Qh),p.filter&&(v=v.filter(w=>cu(w,p.filter.toLowerCase()))),v.forEach(w=>Yh(w,t.currentRoute.value)),p.rootNodes=v.map(Kh)}let m;n.on.getInspectorTree(p=>{m=p,p.app===e&&p.inspectorId===c&&h()}),n.on.getInspectorState(p=>{if(p.app===e&&p.inspectorId===c){const w=s.getRoutes().find(D=>D.record.__vd_id===p.nodeId);w&&(p.state={options:Fw(w)})}}),n.sendInspectorTree(c),n.sendInspectorState(c)})}function Lw(e){return e.optional?e.repeatable?"*":"?":e.repeatable?"+":""}function Fw(e){const{record:t}=e,s=[{editable:!1,key:"path",value:t.path}];return t.name!=null&&s.push({editable:!1,key:"name",value:t.name}),s.push({editable:!1,key:"regexp",value:e.re}),e.keys.length&&s.push({editable:!1,key:"keys",value:{_custom:{type:null,readOnly:!0,display:e.keys.map(i=>`${i.name}${Lw(i)}`).join(" "),tooltip:"Param keys",value:e.keys}}}),t.redirect!=null&&s.push({editable:!1,key:"redirect",value:t.redirect}),e.alias.length&&s.push({editable:!1,key:"aliases",value:e.alias.map(i=>i.record.path)}),Object.keys(e.record.meta).length&&s.push({editable:!1,key:"meta",value:e.record.meta}),s.push({key:"score",editable:!1,value:{_custom:{type:null,readOnly:!0,display:e.score.map(i=>i.join(", ")).join(" | "),tooltip:"Score used to sort routes",value:e.score}}}),s}const qh=15485081,zh=2450411,Wh=8702998,Bw=2282478,Gh=16486972,$w=6710886,jw=16704226,Hw=12131356;function Kh(e){const t=[],{record:s}=e;s.name!=null&&t.push({label:String(s.name),textColor:0,backgroundColor:Bw}),s.aliasOf&&t.push({label:"alias",textColor:0,backgroundColor:Gh}),e.__vd_match&&t.push({label:"matches",textColor:0,backgroundColor:qh}),e.__vd_exactActive&&t.push({label:"exact",textColor:0,backgroundColor:Wh}),e.__vd_active&&t.push({label:"active",textColor:0,backgroundColor:zh}),s.redirect&&t.push({label:typeof s.redirect=="string"?`redirect: ${s.redirect}`:"redirects",textColor:16777215,backgroundColor:$w});let i=s.__vd_id;return i==null&&(i=String(qw++),s.__vd_id=i),{id:i,label:s.path,tags:t,children:e.children.map(Kh)}}let qw=0;const zw=/^\/(.*)\/([a-z]*)$/;function Yh(e,t){const s=t.matched.length&&Rr(t.matched[t.matched.length-1],e.record);e.__vd_exactActive=e.__vd_active=s,s||(e.__vd_active=t.matched.some(i=>Rr(i,e.record))),e.children.forEach(i=>Yh(i,t))}function Qh(e){e.__vd_match=!1,e.children.forEach(Qh)}function cu(e,t){const s=String(e.re).match(zw);if(e.__vd_match=!1,!s||s.length<3)return!1;if(new RegExp(s[1].replace(/\$$/,""),s[2]).test(t))return e.children.forEach(u=>cu(u,t)),e.record.path!=="/"||t==="/"?(e.__vd_match=e.re.test(t),!0):!1;const n=e.record.path.toLowerCase(),a=Un(n);return!t.startsWith("/")&&(a.includes(t)||n.includes(t))||a.startsWith(t)||n.startsWith(t)||e.record.name&&String(e.record.name).includes(t)?!0:e.children.some(u=>cu(u,t))}function Ww(e,t){const s={};for(const i in e)t.includes(i)||(s[i]=e[i]);return s}function Gw(e){const t=gw(e.routes,e),s=e.parseQuery||xw,i=e.stringifyQuery||Lh,n=e.history;if({}.NODE_ENV!=="production"&&!n)throw new Error('Provide the "history" option when calling "createRouter()": https://router.vuejs.org/api/interfaces/RouterOptions.html#history');const a=ko(),u=ko(),c=ko(),h=Cv(Ur);let m=Ur;hr&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const p=eu.bind(null,R=>""+R),v=eu.bind(null,jb),w=eu.bind(null,Un);function D(R,ie){let oe,pe;return Th(R)?(oe=t.getRecordMatcher(R),{}.NODE_ENV!=="production"&&!oe&&$e(`Parent route "${String(R)}" not found when adding child route`,ie),pe=ie):pe=R,t.addRoute(pe,oe)}function V(R){const ie=t.getRecordMatcher(R);ie?t.removeRoute(ie):{}.NODE_ENV!=="production"&&$e(`Cannot remove non-existent route "${String(R)}"`)}function F(){return t.getRoutes().map(R=>R.record)}function se(R){return!!t.getRecordMatcher(R)}function A(R,ie){if(ie=Xe({},ie||h.value),typeof R=="string"){const b=ru(s,R,ie.path),C=t.resolve({path:b.path},ie),P=n.createHref(b.fullPath);return{}.NODE_ENV!=="production"&&(P.startsWith("//")?$e(`Location "${R}" resolved to "${P}". A resolved location cannot start with multiple slashes.`):C.matched.length||$e(`No match found for location with path "${R}"`)),Xe(b,C,{params:w(C.params),hash:Un(b.hash),redirectedFrom:void 0,href:P})}if({}.NODE_ENV!=="production"&&!Ki(R))return $e(`router.resolve() was passed an invalid location. This will fail in production.
- Location:`,R),A({});let oe;if(R.path!=null)({}).NODE_ENV!=="production"&&"params"in R&&!("name"in R)&&Object.keys(R.params).length&&$e(`Path "${R.path}" was passed with params but they will be ignored. Use a named route alongside params instead.`),oe=Xe({},R,{path:ru(s,R.path,ie.path).path});else{const b=Xe({},R.params);for(const C in b)b[C]==null&&delete b[C];oe=Xe({},R,{params:v(b)}),ie.params=v(ie.params)}const pe=t.resolve(oe,ie),ke=R.hash||"";({}).NODE_ENV!=="production"&&ke&&!ke.startsWith("#")&&$e(`A \`hash\` should always start with the character "#". Replace "${ke}" with "#${ke}".`),pe.params=p(w(pe.params));const ot=zb(i,Xe({},R,{hash:Fb(ke),path:pe.path})),Ve=n.createHref(ot);return{}.NODE_ENV!=="production"&&(Ve.startsWith("//")?$e(`Location "${R}" resolved to "${Ve}". A resolved location cannot start with multiple slashes.`):pe.matched.length||$e(`No match found for location with path "${R.path!=null?R.path:R}"`)),Xe({fullPath:ot,hash:ke,query:i===Lh?Ow(R.query):R.query||{}},pe,{redirectedFrom:void 0,href:Ve})}function re(R){return typeof R=="string"?ru(s,R,h.value.path):Xe({},R)}function Y(R,ie){if(m!==R)return Ln(8,{from:ie,to:R})}function ye(R){return _e(R)}function Z(R){return ye(Xe(re(R),{replace:!0}))}function fe(R){const ie=R.matched[R.matched.length-1];if(ie&&ie.redirect){const{redirect:oe}=ie;let pe=typeof oe=="function"?oe(R):oe;if(typeof pe=="string"&&(pe=pe.includes("?")||pe.includes("#")?pe=re(pe):{path:pe},pe.params={}),{}.NODE_ENV!=="production"&&pe.path==null&&!("name"in pe))throw $e(`Invalid redirect found:
${JSON.stringify(pe,null,2)}
 when navigating to "${R.fullPath}". A redirect must contain a name or path. This will break in production.`),new Error("Invalid redirect");return Xe({query:R.query,hash:R.hash,params:pe.path!=null?{}:R.params},pe)}}function _e(R,ie){const oe=m=A(R),pe=h.value,ke=R.state,ot=R.force,Ve=R.replace===!0,b=fe(oe);if(b)return _e(Xe(re(b),{state:typeof b=="object"?Xe({},ke,b.state):ke,force:ot,replace:Ve}),ie||oe);const C=oe;C.redirectedFrom=ie;let P;return!ot&&Eh(i,pe,oe)&&(P=Ln(16,{to:C,from:pe}),yt(pe,pe,!0,!1)),(P?Promise.resolve(P):I(C,pe)).catch(L=>pr(L)?pr(L,2)?L:es(L):we(L,C,pe)).then(L=>{if(L){if(pr(L,2))return{}.NODE_ENV!=="production"&&Eh(i,A(L.to),C)&&ie&&(ie._count=ie._count?ie._count+1:1)>30?($e(`Detected a possibly infinite redirection in a navigation guard when going from "${pe.fullPath}" to "${C.fullPath}". Aborting to avoid a Stack Overflow.
 Are you always returning a new location within a navigation guard? That would lead to this error. Only return when redirecting or aborting, that should fix this. This might break in production if not fixed.`),Promise.reject(new Error("Infinite redirect in navigation guard"))):_e(Xe({replace:Ve},re(L.to),{state:typeof L.to=="object"?Xe({},ke,L.to.state):ke,force:ot}),ie||C)}else L=ue(C,pe,!0,Ve,ke);return be(C,pe,L),L})}function Ae(R,ie){const oe=Y(R,ie);return oe?Promise.reject(oe):Promise.resolve()}function ae(R){const ie=hs.values().next().value;return ie&&typeof ie.runWithContext=="function"?ie.runWithContext(R):R()}function I(R,ie){let oe;const[pe,ke,ot]=Kw(R,ie);oe=uu(pe.reverse(),"beforeRouteLeave",R,ie);for(const b of pe)b.leaveGuards.forEach(C=>{oe.push(Lr(C,R,ie))});const Ve=Ae.bind(null,R,ie);return oe.push(Ve),ps(oe).then(()=>{oe=[];for(const b of a.list())oe.push(Lr(b,R,ie));return oe.push(Ve),ps(oe)}).then(()=>{oe=uu(ke,"beforeRouteUpdate",R,ie);for(const b of ke)b.updateGuards.forEach(C=>{oe.push(Lr(C,R,ie))});return oe.push(Ve),ps(oe)}).then(()=>{oe=[];for(const b of ot)if(b.beforeEnter)if(fs(b.beforeEnter))for(const C of b.beforeEnter)oe.push(Lr(C,R,ie));else oe.push(Lr(b.beforeEnter,R,ie));return oe.push(Ve),ps(oe)}).then(()=>(R.matched.forEach(b=>b.enterCallbacks={}),oe=uu(ot,"beforeRouteEnter",R,ie,ae),oe.push(Ve),ps(oe))).then(()=>{oe=[];for(const b of u.list())oe.push(Lr(b,R,ie));return oe.push(Ve),ps(oe)}).catch(b=>pr(b,8)?b:Promise.reject(b))}function be(R,ie,oe){c.list().forEach(pe=>ae(()=>pe(R,ie,oe)))}function ue(R,ie,oe,pe,ke){const ot=Y(R,ie);if(ot)return ot;const Ve=ie===Ur,b=hr?history.state:{};oe&&(pe||Ve?n.replace(R.fullPath,Xe({scroll:Ve&&b&&b.scroll},ke)):n.push(R.fullPath,ke)),h.value=R,yt(R,ie,oe,Ve),es()}let Ge;function vt(){Ge||(Ge=n.listen((R,ie,oe)=>{if(!zt.listening)return;const pe=A(R),ke=fe(pe);if(ke){_e(Xe(ke,{replace:!0,force:!0}),pe).catch(Ao);return}m=pe;const ot=h.value;hr&&Xb(xh(ot.fullPath,oe.delta),Gi()),I(pe,ot).catch(Ve=>pr(Ve,12)?Ve:pr(Ve,2)?(_e(Xe(re(Ve.to),{force:!0}),pe).then(b=>{pr(b,20)&&!oe.delta&&oe.type===Mo.pop&&n.go(-1,!1)}).catch(Ao),Promise.reject()):(oe.delta&&n.go(-oe.delta,!1),we(Ve,pe,ot))).then(Ve=>{Ve=Ve||ue(pe,ot,!1),Ve&&(oe.delta&&!pr(Ve,8)?n.go(-oe.delta,!1):oe.type===Mo.pop&&pr(Ve,20)&&n.go(-1,!1)),be(pe,ot,Ve)}).catch(Ao)}))}let mt=ko(),ft=ko(),Oe;function we(R,ie,oe){es(R);const pe=ft.list();return pe.length?pe.forEach(ke=>ke(R,ie,oe)):({}.NODE_ENV!=="production"&&$e("uncaught error during route navigation:"),console.error(R)),Promise.reject(R)}function Lt(){return Oe&&h.value!==Ur?Promise.resolve():new Promise((R,ie)=>{mt.add([R,ie])})}function es(R){return Oe||(Oe=!R,vt(),mt.list().forEach(([ie,oe])=>R?oe(R):ie()),mt.reset()),R}function yt(R,ie,oe,pe){const{scrollBehavior:ke}=e;if(!hr||!ke)return Promise.resolve();const ot=!oe&&ew(xh(R.fullPath,0))||(pe||!oe)&&history.state&&history.state.scroll||null;return pl().then(()=>ke(R,ie,ot)).then(Ve=>Ve&&Jb(Ve)).catch(Ve=>we(Ve,R,ie))}const ce=R=>n.go(R);let ze;const hs=new Set,zt={currentRoute:h,listening:!0,addRoute:D,removeRoute:V,clearRoutes:t.clearRoutes,hasRoute:se,getRoutes:F,resolve:A,options:e,push:ye,replace:Z,go:ce,back:()=>ce(-1),forward:()=>ce(1),beforeEach:a.add,beforeResolve:u.add,afterEach:c.add,onError:ft.add,isReady:Lt,install(R){const ie=this;R.component("RouterLink",Iw),R.component("RouterView",kw),R.config.globalProperties.$router=ie,Object.defineProperty(R.config.globalProperties,"$route",{enumerable:!0,get:()=>Tr(h)}),hr&&!ze&&h.value===Ur&&(ze=!0,ye(n.location).catch(ke=>{({}).NODE_ENV!=="production"&&$e("Unexpected error when starting the router:",ke)}));const oe={};for(const ke in Ur)Object.defineProperty(oe,ke,{get:()=>h.value[ke],enumerable:!0});R.provide(Yi,ie),R.provide(au,od(oe)),R.provide(lu,h);const pe=R.unmount;hs.add(R),R.unmount=function(){hs.delete(R),hs.size<1&&(m=Ur,Ge&&Ge(),Ge=null,h.value=Ur,ze=!1,Oe=!1),pe()},{}.NODE_ENV!=="production"&&hr&&Uw(R,ie,t)}};function ps(R){return R.reduce((ie,oe)=>ie.then(()=>ae(oe)),Promise.resolve())}return zt}function Kw(e,t){const s=[],i=[],n=[],a=Math.max(t.matched.length,e.matched.length);for(let u=0;u<a;u++){const c=t.matched[u];c&&(e.matched.find(m=>Rr(m,c))?i.push(c):s.push(c));const h=e.matched[u];h&&(t.matched.find(m=>Rr(m,h))||n.push(h))}return[s,i,n]}function Zi(){return Vs(Yi)}function Zh(e){return Vs(au)}const Yw="data:image/svg+xml;base64,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";var Ro=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Ji={exports:{}};/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */Ji.exports,function(e,t){(function(){var s,i="4.17.21",n=200,a="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",u="Expected a function",c="Invalid `variable` option passed into `_.template`",h="__lodash_hash_undefined__",m=500,p="__lodash_placeholder__",v=1,w=2,D=4,V=1,F=2,se=1,A=2,re=4,Y=8,ye=16,Z=32,fe=64,_e=128,Ae=256,ae=512,I=30,be="...",ue=800,Ge=16,vt=1,mt=2,ft=3,Oe=1/0,we=9007199254740991,Lt=17976931348623157e292,es=0/0,yt=**********,ce=yt-1,ze=yt>>>1,hs=[["ary",_e],["bind",se],["bindKey",A],["curry",Y],["curryRight",ye],["flip",ae],["partial",Z],["partialRight",fe],["rearg",Ae]],zt="[object Arguments]",ps="[object Array]",R="[object AsyncFunction]",ie="[object Boolean]",oe="[object Date]",pe="[object DOMException]",ke="[object Error]",ot="[object Function]",Ve="[object GeneratorFunction]",b="[object Map]",C="[object Number]",P="[object Null]",L="[object Object]",H="[object Promise]",W="[object Proxy]",ee="[object RegExp]",K="[object Set]",J="[object String]",G="[object Symbol]",Ee="[object Undefined]",ne="[object WeakMap]",ve="[object WeakSet]",Ce="[object ArrayBuffer]",Le="[object DataView]",et="[object Float32Array]",Qe="[object Float64Array]",Ft="[object Int8Array]",Ot="[object Int16Array]",ts="[object Int32Array]",Bt="[object Uint8Array]",gr="[object Uint8ClampedArray]",Bn="[object Uint16Array]",It="[object Uint32Array]",Es=/\b__p \+= '';/g,na=/\b(__p \+=) '' \+/g,VN=/(__e\(.*?\)|\b__t\)) \+\n'';/g,cp=/&(?:amp|lt|gt|quot|#39);/g,dp=/[&<>"']/g,RN=RegExp(cp.source),UN=RegExp(dp.source),LN=/<%-([\s\S]+?)%>/g,FN=/<%([\s\S]+?)%>/g,fp=/<%=([\s\S]+?)%>/g,BN=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,$N=/^\w*$/,jN=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,bu=/[\\^$.*+?()[\]{}|]/g,HN=RegExp(bu.source),wu=/^\s+/,qN=/\s/,zN=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,WN=/\{\n\/\* \[wrapped with (.+)\] \*/,GN=/,? & /,KN=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,YN=/[()=,{}\[\]\/\s]/,QN=/\\(\\)?/g,ZN=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,hp=/\w*$/,JN=/^[-+]0x[0-9a-f]+$/i,XN=/^0b[01]+$/i,eI=/^\[object .+?Constructor\]$/,tI=/^0o[0-7]+$/i,sI=/^(?:0|[1-9]\d*)$/,rI=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,oa=/($^)/,nI=/['\n\r\u2028\u2029\\]/g,ia="\\ud800-\\udfff",oI="\\u0300-\\u036f",iI="\\ufe20-\\ufe2f",aI="\\u20d0-\\u20ff",pp=oI+iI+aI,mp="\\u2700-\\u27bf",gp="a-z\\xdf-\\xf6\\xf8-\\xff",lI="\\xac\\xb1\\xd7\\xf7",uI="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",cI="\\u2000-\\u206f",dI=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",vp="A-Z\\xc0-\\xd6\\xd8-\\xde",_p="\\ufe0e\\ufe0f",yp=lI+uI+cI+dI,Eu="['’]",fI="["+ia+"]",bp="["+yp+"]",aa="["+pp+"]",wp="\\d+",hI="["+mp+"]",Ep="["+gp+"]",Cp="[^"+ia+yp+wp+mp+gp+vp+"]",Cu="\\ud83c[\\udffb-\\udfff]",pI="(?:"+aa+"|"+Cu+")",Dp="[^"+ia+"]",Du="(?:\\ud83c[\\udde6-\\uddff]){2}",xu="[\\ud800-\\udbff][\\udc00-\\udfff]",$n="["+vp+"]",xp="\\u200d",Op="(?:"+Ep+"|"+Cp+")",mI="(?:"+$n+"|"+Cp+")",Sp="(?:"+Eu+"(?:d|ll|m|re|s|t|ve))?",Tp="(?:"+Eu+"(?:D|LL|M|RE|S|T|VE))?",Np=pI+"?",Ip="["+_p+"]?",gI="(?:"+xp+"(?:"+[Dp,Du,xu].join("|")+")"+Ip+Np+")*",vI="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",_I="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",Ap=Ip+Np+gI,yI="(?:"+[hI,Du,xu].join("|")+")"+Ap,bI="(?:"+[Dp+aa+"?",aa,Du,xu,fI].join("|")+")",wI=RegExp(Eu,"g"),EI=RegExp(aa,"g"),Ou=RegExp(Cu+"(?="+Cu+")|"+bI+Ap,"g"),CI=RegExp([$n+"?"+Ep+"+"+Sp+"(?="+[bp,$n,"$"].join("|")+")",mI+"+"+Tp+"(?="+[bp,$n+Op,"$"].join("|")+")",$n+"?"+Op+"+"+Sp,$n+"+"+Tp,_I,vI,wp,yI].join("|"),"g"),DI=RegExp("["+xp+ia+pp+_p+"]"),xI=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,OI=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],SI=-1,ht={};ht[et]=ht[Qe]=ht[Ft]=ht[Ot]=ht[ts]=ht[Bt]=ht[gr]=ht[Bn]=ht[It]=!0,ht[zt]=ht[ps]=ht[Ce]=ht[ie]=ht[Le]=ht[oe]=ht[ke]=ht[ot]=ht[b]=ht[C]=ht[L]=ht[ee]=ht[K]=ht[J]=ht[ne]=!1;var ct={};ct[zt]=ct[ps]=ct[Ce]=ct[Le]=ct[ie]=ct[oe]=ct[et]=ct[Qe]=ct[Ft]=ct[Ot]=ct[ts]=ct[b]=ct[C]=ct[L]=ct[ee]=ct[K]=ct[J]=ct[G]=ct[Bt]=ct[gr]=ct[Bn]=ct[It]=!0,ct[ke]=ct[ot]=ct[ne]=!1;var TI={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},NI={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},II={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},AI={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},MI=parseFloat,PI=parseInt,Mp=typeof Ro=="object"&&Ro&&Ro.Object===Object&&Ro,kI=typeof self=="object"&&self&&self.Object===Object&&self,$t=Mp||kI||Function("return this")(),Su=t&&!t.nodeType&&t,mn=Su&&!0&&e&&!e.nodeType&&e,Pp=mn&&mn.exports===Su,Tu=Pp&&Mp.process,Cs=function(){try{var T=mn&&mn.require&&mn.require("util").types;return T||Tu&&Tu.binding&&Tu.binding("util")}catch{}}(),kp=Cs&&Cs.isArrayBuffer,Vp=Cs&&Cs.isDate,Rp=Cs&&Cs.isMap,Up=Cs&&Cs.isRegExp,Lp=Cs&&Cs.isSet,Fp=Cs&&Cs.isTypedArray;function ms(T,U,M){switch(M.length){case 0:return T.call(U);case 1:return T.call(U,M[0]);case 2:return T.call(U,M[0],M[1]);case 3:return T.call(U,M[0],M[1],M[2])}return T.apply(U,M)}function VI(T,U,M,de){for(var Pe=-1,tt=T==null?0:T.length;++Pe<tt;){var At=T[Pe];U(de,At,M(At),T)}return de}function Ds(T,U){for(var M=-1,de=T==null?0:T.length;++M<de&&U(T[M],M,T)!==!1;);return T}function RI(T,U){for(var M=T==null?0:T.length;M--&&U(T[M],M,T)!==!1;);return T}function Bp(T,U){for(var M=-1,de=T==null?0:T.length;++M<de;)if(!U(T[M],M,T))return!1;return!0}function Fr(T,U){for(var M=-1,de=T==null?0:T.length,Pe=0,tt=[];++M<de;){var At=T[M];U(At,M,T)&&(tt[Pe++]=At)}return tt}function la(T,U){var M=T==null?0:T.length;return!!M&&jn(T,U,0)>-1}function Nu(T,U,M){for(var de=-1,Pe=T==null?0:T.length;++de<Pe;)if(M(U,T[de]))return!0;return!1}function gt(T,U){for(var M=-1,de=T==null?0:T.length,Pe=Array(de);++M<de;)Pe[M]=U(T[M],M,T);return Pe}function Br(T,U){for(var M=-1,de=U.length,Pe=T.length;++M<de;)T[Pe+M]=U[M];return T}function Iu(T,U,M,de){var Pe=-1,tt=T==null?0:T.length;for(de&&tt&&(M=T[++Pe]);++Pe<tt;)M=U(M,T[Pe],Pe,T);return M}function UI(T,U,M,de){var Pe=T==null?0:T.length;for(de&&Pe&&(M=T[--Pe]);Pe--;)M=U(M,T[Pe],Pe,T);return M}function Au(T,U){for(var M=-1,de=T==null?0:T.length;++M<de;)if(U(T[M],M,T))return!0;return!1}var LI=Mu("length");function FI(T){return T.split("")}function BI(T){return T.match(KN)||[]}function $p(T,U,M){var de;return M(T,function(Pe,tt,At){if(U(Pe,tt,At))return de=tt,!1}),de}function ua(T,U,M,de){for(var Pe=T.length,tt=M+(de?1:-1);de?tt--:++tt<Pe;)if(U(T[tt],tt,T))return tt;return-1}function jn(T,U,M){return U===U?JI(T,U,M):ua(T,jp,M)}function $I(T,U,M,de){for(var Pe=M-1,tt=T.length;++Pe<tt;)if(de(T[Pe],U))return Pe;return-1}function jp(T){return T!==T}function Hp(T,U){var M=T==null?0:T.length;return M?ku(T,U)/M:es}function Mu(T){return function(U){return U==null?s:U[T]}}function Pu(T){return function(U){return T==null?s:T[U]}}function qp(T,U,M,de,Pe){return Pe(T,function(tt,At,lt){M=de?(de=!1,tt):U(M,tt,At,lt)}),M}function jI(T,U){var M=T.length;for(T.sort(U);M--;)T[M]=T[M].value;return T}function ku(T,U){for(var M,de=-1,Pe=T.length;++de<Pe;){var tt=U(T[de]);tt!==s&&(M=M===s?tt:M+tt)}return M}function Vu(T,U){for(var M=-1,de=Array(T);++M<T;)de[M]=U(M);return de}function HI(T,U){return gt(U,function(M){return[M,T[M]]})}function zp(T){return T&&T.slice(0,Yp(T)+1).replace(wu,"")}function gs(T){return function(U){return T(U)}}function Ru(T,U){return gt(U,function(M){return T[M]})}function Ho(T,U){return T.has(U)}function Wp(T,U){for(var M=-1,de=T.length;++M<de&&jn(U,T[M],0)>-1;);return M}function Gp(T,U){for(var M=T.length;M--&&jn(U,T[M],0)>-1;);return M}function qI(T,U){for(var M=T.length,de=0;M--;)T[M]===U&&++de;return de}var zI=Pu(TI),WI=Pu(NI);function GI(T){return"\\"+AI[T]}function KI(T,U){return T==null?s:T[U]}function Hn(T){return DI.test(T)}function YI(T){return xI.test(T)}function QI(T){for(var U,M=[];!(U=T.next()).done;)M.push(U.value);return M}function Uu(T){var U=-1,M=Array(T.size);return T.forEach(function(de,Pe){M[++U]=[Pe,de]}),M}function Kp(T,U){return function(M){return T(U(M))}}function $r(T,U){for(var M=-1,de=T.length,Pe=0,tt=[];++M<de;){var At=T[M];(At===U||At===p)&&(T[M]=p,tt[Pe++]=M)}return tt}function ca(T){var U=-1,M=Array(T.size);return T.forEach(function(de){M[++U]=de}),M}function ZI(T){var U=-1,M=Array(T.size);return T.forEach(function(de){M[++U]=[de,de]}),M}function JI(T,U,M){for(var de=M-1,Pe=T.length;++de<Pe;)if(T[de]===U)return de;return-1}function XI(T,U,M){for(var de=M+1;de--;)if(T[de]===U)return de;return de}function qn(T){return Hn(T)?tA(T):LI(T)}function Fs(T){return Hn(T)?sA(T):FI(T)}function Yp(T){for(var U=T.length;U--&&qN.test(T.charAt(U)););return U}var eA=Pu(II);function tA(T){for(var U=Ou.lastIndex=0;Ou.test(T);)++U;return U}function sA(T){return T.match(Ou)||[]}function rA(T){return T.match(CI)||[]}var nA=function T(U){U=U==null?$t:zn.defaults($t.Object(),U,zn.pick($t,OI));var M=U.Array,de=U.Date,Pe=U.Error,tt=U.Function,At=U.Math,lt=U.Object,Lu=U.RegExp,oA=U.String,xs=U.TypeError,da=M.prototype,iA=tt.prototype,Wn=lt.prototype,fa=U["__core-js_shared__"],ha=iA.toString,it=Wn.hasOwnProperty,aA=0,Qp=function(){var r=/[^.]+$/.exec(fa&&fa.keys&&fa.keys.IE_PROTO||"");return r?"Symbol(src)_1."+r:""}(),pa=Wn.toString,lA=ha.call(lt),uA=$t._,cA=Lu("^"+ha.call(it).replace(bu,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),ma=Pp?U.Buffer:s,jr=U.Symbol,ga=U.Uint8Array,Zp=ma?ma.allocUnsafe:s,va=Kp(lt.getPrototypeOf,lt),Jp=lt.create,Xp=Wn.propertyIsEnumerable,_a=da.splice,em=jr?jr.isConcatSpreadable:s,qo=jr?jr.iterator:s,gn=jr?jr.toStringTag:s,ya=function(){try{var r=wn(lt,"defineProperty");return r({},"",{}),r}catch{}}(),dA=U.clearTimeout!==$t.clearTimeout&&U.clearTimeout,fA=de&&de.now!==$t.Date.now&&de.now,hA=U.setTimeout!==$t.setTimeout&&U.setTimeout,ba=At.ceil,wa=At.floor,Fu=lt.getOwnPropertySymbols,pA=ma?ma.isBuffer:s,tm=U.isFinite,mA=da.join,gA=Kp(lt.keys,lt),Mt=At.max,Wt=At.min,vA=de.now,_A=U.parseInt,sm=At.random,yA=da.reverse,Bu=wn(U,"DataView"),zo=wn(U,"Map"),$u=wn(U,"Promise"),Gn=wn(U,"Set"),Wo=wn(U,"WeakMap"),Go=wn(lt,"create"),Ea=Wo&&new Wo,Kn={},bA=En(Bu),wA=En(zo),EA=En($u),CA=En(Gn),DA=En(Wo),Ca=jr?jr.prototype:s,Ko=Ca?Ca.valueOf:s,rm=Ca?Ca.toString:s;function _(r){if(bt(r)&&!Re(r)&&!(r instanceof We)){if(r instanceof Os)return r;if(it.call(r,"__wrapped__"))return ng(r)}return new Os(r)}var Yn=function(){function r(){}return function(o){if(!_t(o))return{};if(Jp)return Jp(o);r.prototype=o;var l=new r;return r.prototype=s,l}}();function Da(){}function Os(r,o){this.__wrapped__=r,this.__actions__=[],this.__chain__=!!o,this.__index__=0,this.__values__=s}_.templateSettings={escape:LN,evaluate:FN,interpolate:fp,variable:"",imports:{_}},_.prototype=Da.prototype,_.prototype.constructor=_,Os.prototype=Yn(Da.prototype),Os.prototype.constructor=Os;function We(r){this.__wrapped__=r,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=yt,this.__views__=[]}function xA(){var r=new We(this.__wrapped__);return r.__actions__=os(this.__actions__),r.__dir__=this.__dir__,r.__filtered__=this.__filtered__,r.__iteratees__=os(this.__iteratees__),r.__takeCount__=this.__takeCount__,r.__views__=os(this.__views__),r}function OA(){if(this.__filtered__){var r=new We(this);r.__dir__=-1,r.__filtered__=!0}else r=this.clone(),r.__dir__*=-1;return r}function SA(){var r=this.__wrapped__.value(),o=this.__dir__,l=Re(r),d=o<0,g=l?r.length:0,y=FM(0,g,this.__views__),E=y.start,x=y.end,N=x-E,B=d?x:E-1,j=this.__iteratees__,z=j.length,le=0,ge=Wt(N,this.__takeCount__);if(!l||!d&&g==N&&ge==N)return Sm(r,this.__actions__);var Se=[];e:for(;N--&&le<ge;){B+=o;for(var Be=-1,Te=r[B];++Be<z;){var qe=j[Be],Ke=qe.iteratee,ys=qe.type,ns=Ke(Te);if(ys==mt)Te=ns;else if(!ns){if(ys==vt)continue e;break e}}Se[le++]=Te}return Se}We.prototype=Yn(Da.prototype),We.prototype.constructor=We;function vn(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var d=r[o];this.set(d[0],d[1])}}function TA(){this.__data__=Go?Go(null):{},this.size=0}function NA(r){var o=this.has(r)&&delete this.__data__[r];return this.size-=o?1:0,o}function IA(r){var o=this.__data__;if(Go){var l=o[r];return l===h?s:l}return it.call(o,r)?o[r]:s}function AA(r){var o=this.__data__;return Go?o[r]!==s:it.call(o,r)}function MA(r,o){var l=this.__data__;return this.size+=this.has(r)?0:1,l[r]=Go&&o===s?h:o,this}vn.prototype.clear=TA,vn.prototype.delete=NA,vn.prototype.get=IA,vn.prototype.has=AA,vn.prototype.set=MA;function vr(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var d=r[o];this.set(d[0],d[1])}}function PA(){this.__data__=[],this.size=0}function kA(r){var o=this.__data__,l=xa(o,r);if(l<0)return!1;var d=o.length-1;return l==d?o.pop():_a.call(o,l,1),--this.size,!0}function VA(r){var o=this.__data__,l=xa(o,r);return l<0?s:o[l][1]}function RA(r){return xa(this.__data__,r)>-1}function UA(r,o){var l=this.__data__,d=xa(l,r);return d<0?(++this.size,l.push([r,o])):l[d][1]=o,this}vr.prototype.clear=PA,vr.prototype.delete=kA,vr.prototype.get=VA,vr.prototype.has=RA,vr.prototype.set=UA;function _r(r){var o=-1,l=r==null?0:r.length;for(this.clear();++o<l;){var d=r[o];this.set(d[0],d[1])}}function LA(){this.size=0,this.__data__={hash:new vn,map:new(zo||vr),string:new vn}}function FA(r){var o=Ua(this,r).delete(r);return this.size-=o?1:0,o}function BA(r){return Ua(this,r).get(r)}function $A(r){return Ua(this,r).has(r)}function jA(r,o){var l=Ua(this,r),d=l.size;return l.set(r,o),this.size+=l.size==d?0:1,this}_r.prototype.clear=LA,_r.prototype.delete=FA,_r.prototype.get=BA,_r.prototype.has=$A,_r.prototype.set=jA;function _n(r){var o=-1,l=r==null?0:r.length;for(this.__data__=new _r;++o<l;)this.add(r[o])}function HA(r){return this.__data__.set(r,h),this}function qA(r){return this.__data__.has(r)}_n.prototype.add=_n.prototype.push=HA,_n.prototype.has=qA;function Bs(r){var o=this.__data__=new vr(r);this.size=o.size}function zA(){this.__data__=new vr,this.size=0}function WA(r){var o=this.__data__,l=o.delete(r);return this.size=o.size,l}function GA(r){return this.__data__.get(r)}function KA(r){return this.__data__.has(r)}function YA(r,o){var l=this.__data__;if(l instanceof vr){var d=l.__data__;if(!zo||d.length<n-1)return d.push([r,o]),this.size=++l.size,this;l=this.__data__=new _r(d)}return l.set(r,o),this.size=l.size,this}Bs.prototype.clear=zA,Bs.prototype.delete=WA,Bs.prototype.get=GA,Bs.prototype.has=KA,Bs.prototype.set=YA;function nm(r,o){var l=Re(r),d=!l&&Cn(r),g=!l&&!d&&Gr(r),y=!l&&!d&&!g&&Xn(r),E=l||d||g||y,x=E?Vu(r.length,oA):[],N=x.length;for(var B in r)(o||it.call(r,B))&&!(E&&(B=="length"||g&&(B=="offset"||B=="parent")||y&&(B=="buffer"||B=="byteLength"||B=="byteOffset")||Er(B,N)))&&x.push(B);return x}function om(r){var o=r.length;return o?r[Ju(0,o-1)]:s}function QA(r,o){return La(os(r),yn(o,0,r.length))}function ZA(r){return La(os(r))}function ju(r,o,l){(l!==s&&!$s(r[o],l)||l===s&&!(o in r))&&yr(r,o,l)}function Yo(r,o,l){var d=r[o];(!(it.call(r,o)&&$s(d,l))||l===s&&!(o in r))&&yr(r,o,l)}function xa(r,o){for(var l=r.length;l--;)if($s(r[l][0],o))return l;return-1}function JA(r,o,l,d){return Hr(r,function(g,y,E){o(d,g,l(g),E)}),d}function im(r,o){return r&&Js(o,kt(o),r)}function XA(r,o){return r&&Js(o,as(o),r)}function yr(r,o,l){o=="__proto__"&&ya?ya(r,o,{configurable:!0,enumerable:!0,value:l,writable:!0}):r[o]=l}function Hu(r,o){for(var l=-1,d=o.length,g=M(d),y=r==null;++l<d;)g[l]=y?s:Cc(r,o[l]);return g}function yn(r,o,l){return r===r&&(l!==s&&(r=r<=l?r:l),o!==s&&(r=r>=o?r:o)),r}function Ss(r,o,l,d,g,y){var E,x=o&v,N=o&w,B=o&D;if(l&&(E=g?l(r,d,g,y):l(r)),E!==s)return E;if(!_t(r))return r;var j=Re(r);if(j){if(E=$M(r),!x)return os(r,E)}else{var z=Gt(r),le=z==ot||z==Ve;if(Gr(r))return Im(r,x);if(z==L||z==zt||le&&!g){if(E=N||le?{}:Ym(r),!x)return N?IM(r,XA(E,r)):NM(r,im(E,r))}else{if(!ct[z])return g?r:{};E=jM(r,z,x)}}y||(y=new Bs);var ge=y.get(r);if(ge)return ge;y.set(r,E),Dg(r)?r.forEach(function(Te){E.add(Ss(Te,o,l,Te,r,y))}):Eg(r)&&r.forEach(function(Te,qe){E.set(qe,Ss(Te,o,l,qe,r,y))});var Se=B?N?uc:lc:N?as:kt,Be=j?s:Se(r);return Ds(Be||r,function(Te,qe){Be&&(qe=Te,Te=r[qe]),Yo(E,qe,Ss(Te,o,l,qe,r,y))}),E}function eM(r){var o=kt(r);return function(l){return am(l,r,o)}}function am(r,o,l){var d=l.length;if(r==null)return!d;for(r=lt(r);d--;){var g=l[d],y=o[g],E=r[g];if(E===s&&!(g in r)||!y(E))return!1}return!0}function lm(r,o,l){if(typeof r!="function")throw new xs(u);return si(function(){r.apply(s,l)},o)}function Qo(r,o,l,d){var g=-1,y=la,E=!0,x=r.length,N=[],B=o.length;if(!x)return N;l&&(o=gt(o,gs(l))),d?(y=Nu,E=!1):o.length>=n&&(y=Ho,E=!1,o=new _n(o));e:for(;++g<x;){var j=r[g],z=l==null?j:l(j);if(j=d||j!==0?j:0,E&&z===z){for(var le=B;le--;)if(o[le]===z)continue e;N.push(j)}else y(o,z,d)||N.push(j)}return N}var Hr=Vm(Zs),um=Vm(zu,!0);function tM(r,o){var l=!0;return Hr(r,function(d,g,y){return l=!!o(d,g,y),l}),l}function Oa(r,o,l){for(var d=-1,g=r.length;++d<g;){var y=r[d],E=o(y);if(E!=null&&(x===s?E===E&&!_s(E):l(E,x)))var x=E,N=y}return N}function sM(r,o,l,d){var g=r.length;for(l=Fe(l),l<0&&(l=-l>g?0:g+l),d=d===s||d>g?g:Fe(d),d<0&&(d+=g),d=l>d?0:Og(d);l<d;)r[l++]=o;return r}function cm(r,o){var l=[];return Hr(r,function(d,g,y){o(d,g,y)&&l.push(d)}),l}function jt(r,o,l,d,g){var y=-1,E=r.length;for(l||(l=qM),g||(g=[]);++y<E;){var x=r[y];o>0&&l(x)?o>1?jt(x,o-1,l,d,g):Br(g,x):d||(g[g.length]=x)}return g}var qu=Rm(),dm=Rm(!0);function Zs(r,o){return r&&qu(r,o,kt)}function zu(r,o){return r&&dm(r,o,kt)}function Sa(r,o){return Fr(o,function(l){return Cr(r[l])})}function bn(r,o){o=zr(o,r);for(var l=0,d=o.length;r!=null&&l<d;)r=r[Xs(o[l++])];return l&&l==d?r:s}function fm(r,o,l){var d=o(r);return Re(r)?d:Br(d,l(r))}function ss(r){return r==null?r===s?Ee:P:gn&&gn in lt(r)?LM(r):ZM(r)}function Wu(r,o){return r>o}function rM(r,o){return r!=null&&it.call(r,o)}function nM(r,o){return r!=null&&o in lt(r)}function oM(r,o,l){return r>=Wt(o,l)&&r<Mt(o,l)}function Gu(r,o,l){for(var d=l?Nu:la,g=r[0].length,y=r.length,E=y,x=M(y),N=1/0,B=[];E--;){var j=r[E];E&&o&&(j=gt(j,gs(o))),N=Wt(j.length,N),x[E]=!l&&(o||g>=120&&j.length>=120)?new _n(E&&j):s}j=r[0];var z=-1,le=x[0];e:for(;++z<g&&B.length<N;){var ge=j[z],Se=o?o(ge):ge;if(ge=l||ge!==0?ge:0,!(le?Ho(le,Se):d(B,Se,l))){for(E=y;--E;){var Be=x[E];if(!(Be?Ho(Be,Se):d(r[E],Se,l)))continue e}le&&le.push(Se),B.push(ge)}}return B}function iM(r,o,l,d){return Zs(r,function(g,y,E){o(d,l(g),y,E)}),d}function Zo(r,o,l){o=zr(o,r),r=Xm(r,o);var d=r==null?r:r[Xs(Ns(o))];return d==null?s:ms(d,r,l)}function hm(r){return bt(r)&&ss(r)==zt}function aM(r){return bt(r)&&ss(r)==Ce}function lM(r){return bt(r)&&ss(r)==oe}function Jo(r,o,l,d,g){return r===o?!0:r==null||o==null||!bt(r)&&!bt(o)?r!==r&&o!==o:uM(r,o,l,d,Jo,g)}function uM(r,o,l,d,g,y){var E=Re(r),x=Re(o),N=E?ps:Gt(r),B=x?ps:Gt(o);N=N==zt?L:N,B=B==zt?L:B;var j=N==L,z=B==L,le=N==B;if(le&&Gr(r)){if(!Gr(o))return!1;E=!0,j=!1}if(le&&!j)return y||(y=new Bs),E||Xn(r)?Wm(r,o,l,d,g,y):RM(r,o,N,l,d,g,y);if(!(l&V)){var ge=j&&it.call(r,"__wrapped__"),Se=z&&it.call(o,"__wrapped__");if(ge||Se){var Be=ge?r.value():r,Te=Se?o.value():o;return y||(y=new Bs),g(Be,Te,l,d,y)}}return le?(y||(y=new Bs),UM(r,o,l,d,g,y)):!1}function cM(r){return bt(r)&&Gt(r)==b}function Ku(r,o,l,d){var g=l.length,y=g,E=!d;if(r==null)return!y;for(r=lt(r);g--;){var x=l[g];if(E&&x[2]?x[1]!==r[x[0]]:!(x[0]in r))return!1}for(;++g<y;){x=l[g];var N=x[0],B=r[N],j=x[1];if(E&&x[2]){if(B===s&&!(N in r))return!1}else{var z=new Bs;if(d)var le=d(B,j,N,r,o,z);if(!(le===s?Jo(j,B,V|F,d,z):le))return!1}}return!0}function pm(r){if(!_t(r)||WM(r))return!1;var o=Cr(r)?cA:eI;return o.test(En(r))}function dM(r){return bt(r)&&ss(r)==ee}function fM(r){return bt(r)&&Gt(r)==K}function hM(r){return bt(r)&&qa(r.length)&&!!ht[ss(r)]}function mm(r){return typeof r=="function"?r:r==null?ls:typeof r=="object"?Re(r)?_m(r[0],r[1]):vm(r):Ug(r)}function Yu(r){if(!ti(r))return gA(r);var o=[];for(var l in lt(r))it.call(r,l)&&l!="constructor"&&o.push(l);return o}function pM(r){if(!_t(r))return QM(r);var o=ti(r),l=[];for(var d in r)d=="constructor"&&(o||!it.call(r,d))||l.push(d);return l}function Qu(r,o){return r<o}function gm(r,o){var l=-1,d=is(r)?M(r.length):[];return Hr(r,function(g,y,E){d[++l]=o(g,y,E)}),d}function vm(r){var o=dc(r);return o.length==1&&o[0][2]?Zm(o[0][0],o[0][1]):function(l){return l===r||Ku(l,r,o)}}function _m(r,o){return hc(r)&&Qm(o)?Zm(Xs(r),o):function(l){var d=Cc(l,r);return d===s&&d===o?Dc(l,r):Jo(o,d,V|F)}}function Ta(r,o,l,d,g){r!==o&&qu(o,function(y,E){if(g||(g=new Bs),_t(y))mM(r,o,E,l,Ta,d,g);else{var x=d?d(mc(r,E),y,E+"",r,o,g):s;x===s&&(x=y),ju(r,E,x)}},as)}function mM(r,o,l,d,g,y,E){var x=mc(r,l),N=mc(o,l),B=E.get(N);if(B){ju(r,l,B);return}var j=y?y(x,N,l+"",r,o,E):s,z=j===s;if(z){var le=Re(N),ge=!le&&Gr(N),Se=!le&&!ge&&Xn(N);j=N,le||ge||Se?Re(x)?j=x:Et(x)?j=os(x):ge?(z=!1,j=Im(N,!0)):Se?(z=!1,j=Am(N,!0)):j=[]:ri(N)||Cn(N)?(j=x,Cn(x)?j=Sg(x):(!_t(x)||Cr(x))&&(j=Ym(N))):z=!1}z&&(E.set(N,j),g(j,N,d,y,E),E.delete(N)),ju(r,l,j)}function ym(r,o){var l=r.length;if(l)return o+=o<0?l:0,Er(o,l)?r[o]:s}function bm(r,o,l){o.length?o=gt(o,function(y){return Re(y)?function(E){return bn(E,y.length===1?y[0]:y)}:y}):o=[ls];var d=-1;o=gt(o,gs(De()));var g=gm(r,function(y,E,x){var N=gt(o,function(B){return B(y)});return{criteria:N,index:++d,value:y}});return jI(g,function(y,E){return TM(y,E,l)})}function gM(r,o){return wm(r,o,function(l,d){return Dc(r,d)})}function wm(r,o,l){for(var d=-1,g=o.length,y={};++d<g;){var E=o[d],x=bn(r,E);l(x,E)&&Xo(y,zr(E,r),x)}return y}function vM(r){return function(o){return bn(o,r)}}function Zu(r,o,l,d){var g=d?$I:jn,y=-1,E=o.length,x=r;for(r===o&&(o=os(o)),l&&(x=gt(r,gs(l)));++y<E;)for(var N=0,B=o[y],j=l?l(B):B;(N=g(x,j,N,d))>-1;)x!==r&&_a.call(x,N,1),_a.call(r,N,1);return r}function Em(r,o){for(var l=r?o.length:0,d=l-1;l--;){var g=o[l];if(l==d||g!==y){var y=g;Er(g)?_a.call(r,g,1):tc(r,g)}}return r}function Ju(r,o){return r+wa(sm()*(o-r+1))}function _M(r,o,l,d){for(var g=-1,y=Mt(ba((o-r)/(l||1)),0),E=M(y);y--;)E[d?y:++g]=r,r+=l;return E}function Xu(r,o){var l="";if(!r||o<1||o>we)return l;do o%2&&(l+=r),o=wa(o/2),o&&(r+=r);while(o);return l}function je(r,o){return gc(Jm(r,o,ls),r+"")}function yM(r){return om(eo(r))}function bM(r,o){var l=eo(r);return La(l,yn(o,0,l.length))}function Xo(r,o,l,d){if(!_t(r))return r;o=zr(o,r);for(var g=-1,y=o.length,E=y-1,x=r;x!=null&&++g<y;){var N=Xs(o[g]),B=l;if(N==="__proto__"||N==="constructor"||N==="prototype")return r;if(g!=E){var j=x[N];B=d?d(j,N,x):s,B===s&&(B=_t(j)?j:Er(o[g+1])?[]:{})}Yo(x,N,B),x=x[N]}return r}var Cm=Ea?function(r,o){return Ea.set(r,o),r}:ls,wM=ya?function(r,o){return ya(r,"toString",{configurable:!0,enumerable:!1,value:Oc(o),writable:!0})}:ls;function EM(r){return La(eo(r))}function Ts(r,o,l){var d=-1,g=r.length;o<0&&(o=-o>g?0:g+o),l=l>g?g:l,l<0&&(l+=g),g=o>l?0:l-o>>>0,o>>>=0;for(var y=M(g);++d<g;)y[d]=r[d+o];return y}function CM(r,o){var l;return Hr(r,function(d,g,y){return l=o(d,g,y),!l}),!!l}function Na(r,o,l){var d=0,g=r==null?d:r.length;if(typeof o=="number"&&o===o&&g<=ze){for(;d<g;){var y=d+g>>>1,E=r[y];E!==null&&!_s(E)&&(l?E<=o:E<o)?d=y+1:g=y}return g}return ec(r,o,ls,l)}function ec(r,o,l,d){var g=0,y=r==null?0:r.length;if(y===0)return 0;o=l(o);for(var E=o!==o,x=o===null,N=_s(o),B=o===s;g<y;){var j=wa((g+y)/2),z=l(r[j]),le=z!==s,ge=z===null,Se=z===z,Be=_s(z);if(E)var Te=d||Se;else B?Te=Se&&(d||le):x?Te=Se&&le&&(d||!ge):N?Te=Se&&le&&!ge&&(d||!Be):ge||Be?Te=!1:Te=d?z<=o:z<o;Te?g=j+1:y=j}return Wt(y,ce)}function Dm(r,o){for(var l=-1,d=r.length,g=0,y=[];++l<d;){var E=r[l],x=o?o(E):E;if(!l||!$s(x,N)){var N=x;y[g++]=E===0?0:E}}return y}function xm(r){return typeof r=="number"?r:_s(r)?es:+r}function vs(r){if(typeof r=="string")return r;if(Re(r))return gt(r,vs)+"";if(_s(r))return rm?rm.call(r):"";var o=r+"";return o=="0"&&1/r==-Oe?"-0":o}function qr(r,o,l){var d=-1,g=la,y=r.length,E=!0,x=[],N=x;if(l)E=!1,g=Nu;else if(y>=n){var B=o?null:kM(r);if(B)return ca(B);E=!1,g=Ho,N=new _n}else N=o?[]:x;e:for(;++d<y;){var j=r[d],z=o?o(j):j;if(j=l||j!==0?j:0,E&&z===z){for(var le=N.length;le--;)if(N[le]===z)continue e;o&&N.push(z),x.push(j)}else g(N,z,l)||(N!==x&&N.push(z),x.push(j))}return x}function tc(r,o){return o=zr(o,r),r=Xm(r,o),r==null||delete r[Xs(Ns(o))]}function Om(r,o,l,d){return Xo(r,o,l(bn(r,o)),d)}function Ia(r,o,l,d){for(var g=r.length,y=d?g:-1;(d?y--:++y<g)&&o(r[y],y,r););return l?Ts(r,d?0:y,d?y+1:g):Ts(r,d?y+1:0,d?g:y)}function Sm(r,o){var l=r;return l instanceof We&&(l=l.value()),Iu(o,function(d,g){return g.func.apply(g.thisArg,Br([d],g.args))},l)}function sc(r,o,l){var d=r.length;if(d<2)return d?qr(r[0]):[];for(var g=-1,y=M(d);++g<d;)for(var E=r[g],x=-1;++x<d;)x!=g&&(y[g]=Qo(y[g]||E,r[x],o,l));return qr(jt(y,1),o,l)}function Tm(r,o,l){for(var d=-1,g=r.length,y=o.length,E={};++d<g;){var x=d<y?o[d]:s;l(E,r[d],x)}return E}function rc(r){return Et(r)?r:[]}function nc(r){return typeof r=="function"?r:ls}function zr(r,o){return Re(r)?r:hc(r,o)?[r]:rg(nt(r))}var DM=je;function Wr(r,o,l){var d=r.length;return l=l===s?d:l,!o&&l>=d?r:Ts(r,o,l)}var Nm=dA||function(r){return $t.clearTimeout(r)};function Im(r,o){if(o)return r.slice();var l=r.length,d=Zp?Zp(l):new r.constructor(l);return r.copy(d),d}function oc(r){var o=new r.constructor(r.byteLength);return new ga(o).set(new ga(r)),o}function xM(r,o){var l=o?oc(r.buffer):r.buffer;return new r.constructor(l,r.byteOffset,r.byteLength)}function OM(r){var o=new r.constructor(r.source,hp.exec(r));return o.lastIndex=r.lastIndex,o}function SM(r){return Ko?lt(Ko.call(r)):{}}function Am(r,o){var l=o?oc(r.buffer):r.buffer;return new r.constructor(l,r.byteOffset,r.length)}function Mm(r,o){if(r!==o){var l=r!==s,d=r===null,g=r===r,y=_s(r),E=o!==s,x=o===null,N=o===o,B=_s(o);if(!x&&!B&&!y&&r>o||y&&E&&N&&!x&&!B||d&&E&&N||!l&&N||!g)return 1;if(!d&&!y&&!B&&r<o||B&&l&&g&&!d&&!y||x&&l&&g||!E&&g||!N)return-1}return 0}function TM(r,o,l){for(var d=-1,g=r.criteria,y=o.criteria,E=g.length,x=l.length;++d<E;){var N=Mm(g[d],y[d]);if(N){if(d>=x)return N;var B=l[d];return N*(B=="desc"?-1:1)}}return r.index-o.index}function Pm(r,o,l,d){for(var g=-1,y=r.length,E=l.length,x=-1,N=o.length,B=Mt(y-E,0),j=M(N+B),z=!d;++x<N;)j[x]=o[x];for(;++g<E;)(z||g<y)&&(j[l[g]]=r[g]);for(;B--;)j[x++]=r[g++];return j}function km(r,o,l,d){for(var g=-1,y=r.length,E=-1,x=l.length,N=-1,B=o.length,j=Mt(y-x,0),z=M(j+B),le=!d;++g<j;)z[g]=r[g];for(var ge=g;++N<B;)z[ge+N]=o[N];for(;++E<x;)(le||g<y)&&(z[ge+l[E]]=r[g++]);return z}function os(r,o){var l=-1,d=r.length;for(o||(o=M(d));++l<d;)o[l]=r[l];return o}function Js(r,o,l,d){var g=!l;l||(l={});for(var y=-1,E=o.length;++y<E;){var x=o[y],N=d?d(l[x],r[x],x,l,r):s;N===s&&(N=r[x]),g?yr(l,x,N):Yo(l,x,N)}return l}function NM(r,o){return Js(r,fc(r),o)}function IM(r,o){return Js(r,Gm(r),o)}function Aa(r,o){return function(l,d){var g=Re(l)?VI:JA,y=o?o():{};return g(l,r,De(d,2),y)}}function Qn(r){return je(function(o,l){var d=-1,g=l.length,y=g>1?l[g-1]:s,E=g>2?l[2]:s;for(y=r.length>3&&typeof y=="function"?(g--,y):s,E&&rs(l[0],l[1],E)&&(y=g<3?s:y,g=1),o=lt(o);++d<g;){var x=l[d];x&&r(o,x,d,y)}return o})}function Vm(r,o){return function(l,d){if(l==null)return l;if(!is(l))return r(l,d);for(var g=l.length,y=o?g:-1,E=lt(l);(o?y--:++y<g)&&d(E[y],y,E)!==!1;);return l}}function Rm(r){return function(o,l,d){for(var g=-1,y=lt(o),E=d(o),x=E.length;x--;){var N=E[r?x:++g];if(l(y[N],N,y)===!1)break}return o}}function AM(r,o,l){var d=o&se,g=ei(r);function y(){var E=this&&this!==$t&&this instanceof y?g:r;return E.apply(d?l:this,arguments)}return y}function Um(r){return function(o){o=nt(o);var l=Hn(o)?Fs(o):s,d=l?l[0]:o.charAt(0),g=l?Wr(l,1).join(""):o.slice(1);return d[r]()+g}}function Zn(r){return function(o){return Iu(Vg(kg(o).replace(wI,"")),r,"")}}function ei(r){return function(){var o=arguments;switch(o.length){case 0:return new r;case 1:return new r(o[0]);case 2:return new r(o[0],o[1]);case 3:return new r(o[0],o[1],o[2]);case 4:return new r(o[0],o[1],o[2],o[3]);case 5:return new r(o[0],o[1],o[2],o[3],o[4]);case 6:return new r(o[0],o[1],o[2],o[3],o[4],o[5]);case 7:return new r(o[0],o[1],o[2],o[3],o[4],o[5],o[6])}var l=Yn(r.prototype),d=r.apply(l,o);return _t(d)?d:l}}function MM(r,o,l){var d=ei(r);function g(){for(var y=arguments.length,E=M(y),x=y,N=Jn(g);x--;)E[x]=arguments[x];var B=y<3&&E[0]!==N&&E[y-1]!==N?[]:$r(E,N);if(y-=B.length,y<l)return jm(r,o,Ma,g.placeholder,s,E,B,s,s,l-y);var j=this&&this!==$t&&this instanceof g?d:r;return ms(j,this,E)}return g}function Lm(r){return function(o,l,d){var g=lt(o);if(!is(o)){var y=De(l,3);o=kt(o),l=function(x){return y(g[x],x,g)}}var E=r(o,l,d);return E>-1?g[y?o[E]:E]:s}}function Fm(r){return wr(function(o){var l=o.length,d=l,g=Os.prototype.thru;for(r&&o.reverse();d--;){var y=o[d];if(typeof y!="function")throw new xs(u);if(g&&!E&&Ra(y)=="wrapper")var E=new Os([],!0)}for(d=E?d:l;++d<l;){y=o[d];var x=Ra(y),N=x=="wrapper"?cc(y):s;N&&pc(N[0])&&N[1]==(_e|Y|Z|Ae)&&!N[4].length&&N[9]==1?E=E[Ra(N[0])].apply(E,N[3]):E=y.length==1&&pc(y)?E[x]():E.thru(y)}return function(){var B=arguments,j=B[0];if(E&&B.length==1&&Re(j))return E.plant(j).value();for(var z=0,le=l?o[z].apply(this,B):j;++z<l;)le=o[z].call(this,le);return le}})}function Ma(r,o,l,d,g,y,E,x,N,B){var j=o&_e,z=o&se,le=o&A,ge=o&(Y|ye),Se=o&ae,Be=le?s:ei(r);function Te(){for(var qe=arguments.length,Ke=M(qe),ys=qe;ys--;)Ke[ys]=arguments[ys];if(ge)var ns=Jn(Te),bs=qI(Ke,ns);if(d&&(Ke=Pm(Ke,d,g,ge)),y&&(Ke=km(Ke,y,E,ge)),qe-=bs,ge&&qe<B){var Ct=$r(Ke,ns);return jm(r,o,Ma,Te.placeholder,l,Ke,Ct,x,N,B-qe)}var js=z?l:this,xr=le?js[r]:r;return qe=Ke.length,x?Ke=JM(Ke,x):Se&&qe>1&&Ke.reverse(),j&&N<qe&&(Ke.length=N),this&&this!==$t&&this instanceof Te&&(xr=Be||ei(xr)),xr.apply(js,Ke)}return Te}function Bm(r,o){return function(l,d){return iM(l,r,o(d),{})}}function Pa(r,o){return function(l,d){var g;if(l===s&&d===s)return o;if(l!==s&&(g=l),d!==s){if(g===s)return d;typeof l=="string"||typeof d=="string"?(l=vs(l),d=vs(d)):(l=xm(l),d=xm(d)),g=r(l,d)}return g}}function ic(r){return wr(function(o){return o=gt(o,gs(De())),je(function(l){var d=this;return r(o,function(g){return ms(g,d,l)})})})}function ka(r,o){o=o===s?" ":vs(o);var l=o.length;if(l<2)return l?Xu(o,r):o;var d=Xu(o,ba(r/qn(o)));return Hn(o)?Wr(Fs(d),0,r).join(""):d.slice(0,r)}function PM(r,o,l,d){var g=o&se,y=ei(r);function E(){for(var x=-1,N=arguments.length,B=-1,j=d.length,z=M(j+N),le=this&&this!==$t&&this instanceof E?y:r;++B<j;)z[B]=d[B];for(;N--;)z[B++]=arguments[++x];return ms(le,g?l:this,z)}return E}function $m(r){return function(o,l,d){return d&&typeof d!="number"&&rs(o,l,d)&&(l=d=s),o=Dr(o),l===s?(l=o,o=0):l=Dr(l),d=d===s?o<l?1:-1:Dr(d),_M(o,l,d,r)}}function Va(r){return function(o,l){return typeof o=="string"&&typeof l=="string"||(o=Is(o),l=Is(l)),r(o,l)}}function jm(r,o,l,d,g,y,E,x,N,B){var j=o&Y,z=j?E:s,le=j?s:E,ge=j?y:s,Se=j?s:y;o|=j?Z:fe,o&=~(j?fe:Z),o&re||(o&=~(se|A));var Be=[r,o,g,ge,z,Se,le,x,N,B],Te=l.apply(s,Be);return pc(r)&&eg(Te,Be),Te.placeholder=d,tg(Te,r,o)}function ac(r){var o=At[r];return function(l,d){if(l=Is(l),d=d==null?0:Wt(Fe(d),292),d&&tm(l)){var g=(nt(l)+"e").split("e"),y=o(g[0]+"e"+(+g[1]+d));return g=(nt(y)+"e").split("e"),+(g[0]+"e"+(+g[1]-d))}return o(l)}}var kM=Gn&&1/ca(new Gn([,-0]))[1]==Oe?function(r){return new Gn(r)}:Nc;function Hm(r){return function(o){var l=Gt(o);return l==b?Uu(o):l==K?ZI(o):HI(o,r(o))}}function br(r,o,l,d,g,y,E,x){var N=o&A;if(!N&&typeof r!="function")throw new xs(u);var B=d?d.length:0;if(B||(o&=~(Z|fe),d=g=s),E=E===s?E:Mt(Fe(E),0),x=x===s?x:Fe(x),B-=g?g.length:0,o&fe){var j=d,z=g;d=g=s}var le=N?s:cc(r),ge=[r,o,l,d,g,j,z,y,E,x];if(le&&YM(ge,le),r=ge[0],o=ge[1],l=ge[2],d=ge[3],g=ge[4],x=ge[9]=ge[9]===s?N?0:r.length:Mt(ge[9]-B,0),!x&&o&(Y|ye)&&(o&=~(Y|ye)),!o||o==se)var Se=AM(r,o,l);else o==Y||o==ye?Se=MM(r,o,x):(o==Z||o==(se|Z))&&!g.length?Se=PM(r,o,l,d):Se=Ma.apply(s,ge);var Be=le?Cm:eg;return tg(Be(Se,ge),r,o)}function qm(r,o,l,d){return r===s||$s(r,Wn[l])&&!it.call(d,l)?o:r}function zm(r,o,l,d,g,y){return _t(r)&&_t(o)&&(y.set(o,r),Ta(r,o,s,zm,y),y.delete(o)),r}function VM(r){return ri(r)?s:r}function Wm(r,o,l,d,g,y){var E=l&V,x=r.length,N=o.length;if(x!=N&&!(E&&N>x))return!1;var B=y.get(r),j=y.get(o);if(B&&j)return B==o&&j==r;var z=-1,le=!0,ge=l&F?new _n:s;for(y.set(r,o),y.set(o,r);++z<x;){var Se=r[z],Be=o[z];if(d)var Te=E?d(Be,Se,z,o,r,y):d(Se,Be,z,r,o,y);if(Te!==s){if(Te)continue;le=!1;break}if(ge){if(!Au(o,function(qe,Ke){if(!Ho(ge,Ke)&&(Se===qe||g(Se,qe,l,d,y)))return ge.push(Ke)})){le=!1;break}}else if(!(Se===Be||g(Se,Be,l,d,y))){le=!1;break}}return y.delete(r),y.delete(o),le}function RM(r,o,l,d,g,y,E){switch(l){case Le:if(r.byteLength!=o.byteLength||r.byteOffset!=o.byteOffset)return!1;r=r.buffer,o=o.buffer;case Ce:return!(r.byteLength!=o.byteLength||!y(new ga(r),new ga(o)));case ie:case oe:case C:return $s(+r,+o);case ke:return r.name==o.name&&r.message==o.message;case ee:case J:return r==o+"";case b:var x=Uu;case K:var N=d&V;if(x||(x=ca),r.size!=o.size&&!N)return!1;var B=E.get(r);if(B)return B==o;d|=F,E.set(r,o);var j=Wm(x(r),x(o),d,g,y,E);return E.delete(r),j;case G:if(Ko)return Ko.call(r)==Ko.call(o)}return!1}function UM(r,o,l,d,g,y){var E=l&V,x=lc(r),N=x.length,B=lc(o),j=B.length;if(N!=j&&!E)return!1;for(var z=N;z--;){var le=x[z];if(!(E?le in o:it.call(o,le)))return!1}var ge=y.get(r),Se=y.get(o);if(ge&&Se)return ge==o&&Se==r;var Be=!0;y.set(r,o),y.set(o,r);for(var Te=E;++z<N;){le=x[z];var qe=r[le],Ke=o[le];if(d)var ys=E?d(Ke,qe,le,o,r,y):d(qe,Ke,le,r,o,y);if(!(ys===s?qe===Ke||g(qe,Ke,l,d,y):ys)){Be=!1;break}Te||(Te=le=="constructor")}if(Be&&!Te){var ns=r.constructor,bs=o.constructor;ns!=bs&&"constructor"in r&&"constructor"in o&&!(typeof ns=="function"&&ns instanceof ns&&typeof bs=="function"&&bs instanceof bs)&&(Be=!1)}return y.delete(r),y.delete(o),Be}function wr(r){return gc(Jm(r,s,ag),r+"")}function lc(r){return fm(r,kt,fc)}function uc(r){return fm(r,as,Gm)}var cc=Ea?function(r){return Ea.get(r)}:Nc;function Ra(r){for(var o=r.name+"",l=Kn[o],d=it.call(Kn,o)?l.length:0;d--;){var g=l[d],y=g.func;if(y==null||y==r)return g.name}return o}function Jn(r){var o=it.call(_,"placeholder")?_:r;return o.placeholder}function De(){var r=_.iteratee||Sc;return r=r===Sc?mm:r,arguments.length?r(arguments[0],arguments[1]):r}function Ua(r,o){var l=r.__data__;return zM(o)?l[typeof o=="string"?"string":"hash"]:l.map}function dc(r){for(var o=kt(r),l=o.length;l--;){var d=o[l],g=r[d];o[l]=[d,g,Qm(g)]}return o}function wn(r,o){var l=KI(r,o);return pm(l)?l:s}function LM(r){var o=it.call(r,gn),l=r[gn];try{r[gn]=s;var d=!0}catch{}var g=pa.call(r);return d&&(o?r[gn]=l:delete r[gn]),g}var fc=Fu?function(r){return r==null?[]:(r=lt(r),Fr(Fu(r),function(o){return Xp.call(r,o)}))}:Ic,Gm=Fu?function(r){for(var o=[];r;)Br(o,fc(r)),r=va(r);return o}:Ic,Gt=ss;(Bu&&Gt(new Bu(new ArrayBuffer(1)))!=Le||zo&&Gt(new zo)!=b||$u&&Gt($u.resolve())!=H||Gn&&Gt(new Gn)!=K||Wo&&Gt(new Wo)!=ne)&&(Gt=function(r){var o=ss(r),l=o==L?r.constructor:s,d=l?En(l):"";if(d)switch(d){case bA:return Le;case wA:return b;case EA:return H;case CA:return K;case DA:return ne}return o});function FM(r,o,l){for(var d=-1,g=l.length;++d<g;){var y=l[d],E=y.size;switch(y.type){case"drop":r+=E;break;case"dropRight":o-=E;break;case"take":o=Wt(o,r+E);break;case"takeRight":r=Mt(r,o-E);break}}return{start:r,end:o}}function BM(r){var o=r.match(WN);return o?o[1].split(GN):[]}function Km(r,o,l){o=zr(o,r);for(var d=-1,g=o.length,y=!1;++d<g;){var E=Xs(o[d]);if(!(y=r!=null&&l(r,E)))break;r=r[E]}return y||++d!=g?y:(g=r==null?0:r.length,!!g&&qa(g)&&Er(E,g)&&(Re(r)||Cn(r)))}function $M(r){var o=r.length,l=new r.constructor(o);return o&&typeof r[0]=="string"&&it.call(r,"index")&&(l.index=r.index,l.input=r.input),l}function Ym(r){return typeof r.constructor=="function"&&!ti(r)?Yn(va(r)):{}}function jM(r,o,l){var d=r.constructor;switch(o){case Ce:return oc(r);case ie:case oe:return new d(+r);case Le:return xM(r,l);case et:case Qe:case Ft:case Ot:case ts:case Bt:case gr:case Bn:case It:return Am(r,l);case b:return new d;case C:case J:return new d(r);case ee:return OM(r);case K:return new d;case G:return SM(r)}}function HM(r,o){var l=o.length;if(!l)return r;var d=l-1;return o[d]=(l>1?"& ":"")+o[d],o=o.join(l>2?", ":" "),r.replace(zN,`{
/* [wrapped with `+o+`] */
`)}function qM(r){return Re(r)||Cn(r)||!!(em&&r&&r[em])}function Er(r,o){var l=typeof r;return o=o??we,!!o&&(l=="number"||l!="symbol"&&sI.test(r))&&r>-1&&r%1==0&&r<o}function rs(r,o,l){if(!_t(l))return!1;var d=typeof o;return(d=="number"?is(l)&&Er(o,l.length):d=="string"&&o in l)?$s(l[o],r):!1}function hc(r,o){if(Re(r))return!1;var l=typeof r;return l=="number"||l=="symbol"||l=="boolean"||r==null||_s(r)?!0:$N.test(r)||!BN.test(r)||o!=null&&r in lt(o)}function zM(r){var o=typeof r;return o=="string"||o=="number"||o=="symbol"||o=="boolean"?r!=="__proto__":r===null}function pc(r){var o=Ra(r),l=_[o];if(typeof l!="function"||!(o in We.prototype))return!1;if(r===l)return!0;var d=cc(l);return!!d&&r===d[0]}function WM(r){return!!Qp&&Qp in r}var GM=fa?Cr:Ac;function ti(r){var o=r&&r.constructor,l=typeof o=="function"&&o.prototype||Wn;return r===l}function Qm(r){return r===r&&!_t(r)}function Zm(r,o){return function(l){return l==null?!1:l[r]===o&&(o!==s||r in lt(l))}}function KM(r){var o=ja(r,function(d){return l.size===m&&l.clear(),d}),l=o.cache;return o}function YM(r,o){var l=r[1],d=o[1],g=l|d,y=g<(se|A|_e),E=d==_e&&l==Y||d==_e&&l==Ae&&r[7].length<=o[8]||d==(_e|Ae)&&o[7].length<=o[8]&&l==Y;if(!(y||E))return r;d&se&&(r[2]=o[2],g|=l&se?0:re);var x=o[3];if(x){var N=r[3];r[3]=N?Pm(N,x,o[4]):x,r[4]=N?$r(r[3],p):o[4]}return x=o[5],x&&(N=r[5],r[5]=N?km(N,x,o[6]):x,r[6]=N?$r(r[5],p):o[6]),x=o[7],x&&(r[7]=x),d&_e&&(r[8]=r[8]==null?o[8]:Wt(r[8],o[8])),r[9]==null&&(r[9]=o[9]),r[0]=o[0],r[1]=g,r}function QM(r){var o=[];if(r!=null)for(var l in lt(r))o.push(l);return o}function ZM(r){return pa.call(r)}function Jm(r,o,l){return o=Mt(o===s?r.length-1:o,0),function(){for(var d=arguments,g=-1,y=Mt(d.length-o,0),E=M(y);++g<y;)E[g]=d[o+g];g=-1;for(var x=M(o+1);++g<o;)x[g]=d[g];return x[o]=l(E),ms(r,this,x)}}function Xm(r,o){return o.length<2?r:bn(r,Ts(o,0,-1))}function JM(r,o){for(var l=r.length,d=Wt(o.length,l),g=os(r);d--;){var y=o[d];r[d]=Er(y,l)?g[y]:s}return r}function mc(r,o){if(!(o==="constructor"&&typeof r[o]=="function")&&o!="__proto__")return r[o]}var eg=sg(Cm),si=hA||function(r,o){return $t.setTimeout(r,o)},gc=sg(wM);function tg(r,o,l){var d=o+"";return gc(r,HM(d,XM(BM(d),l)))}function sg(r){var o=0,l=0;return function(){var d=vA(),g=Ge-(d-l);if(l=d,g>0){if(++o>=ue)return arguments[0]}else o=0;return r.apply(s,arguments)}}function La(r,o){var l=-1,d=r.length,g=d-1;for(o=o===s?d:o;++l<o;){var y=Ju(l,g),E=r[y];r[y]=r[l],r[l]=E}return r.length=o,r}var rg=KM(function(r){var o=[];return r.charCodeAt(0)===46&&o.push(""),r.replace(jN,function(l,d,g,y){o.push(g?y.replace(QN,"$1"):d||l)}),o});function Xs(r){if(typeof r=="string"||_s(r))return r;var o=r+"";return o=="0"&&1/r==-Oe?"-0":o}function En(r){if(r!=null){try{return ha.call(r)}catch{}try{return r+""}catch{}}return""}function XM(r,o){return Ds(hs,function(l){var d="_."+l[0];o&l[1]&&!la(r,d)&&r.push(d)}),r.sort()}function ng(r){if(r instanceof We)return r.clone();var o=new Os(r.__wrapped__,r.__chain__);return o.__actions__=os(r.__actions__),o.__index__=r.__index__,o.__values__=r.__values__,o}function e2(r,o,l){(l?rs(r,o,l):o===s)?o=1:o=Mt(Fe(o),0);var d=r==null?0:r.length;if(!d||o<1)return[];for(var g=0,y=0,E=M(ba(d/o));g<d;)E[y++]=Ts(r,g,g+=o);return E}function t2(r){for(var o=-1,l=r==null?0:r.length,d=0,g=[];++o<l;){var y=r[o];y&&(g[d++]=y)}return g}function s2(){var r=arguments.length;if(!r)return[];for(var o=M(r-1),l=arguments[0],d=r;d--;)o[d-1]=arguments[d];return Br(Re(l)?os(l):[l],jt(o,1))}var r2=je(function(r,o){return Et(r)?Qo(r,jt(o,1,Et,!0)):[]}),n2=je(function(r,o){var l=Ns(o);return Et(l)&&(l=s),Et(r)?Qo(r,jt(o,1,Et,!0),De(l,2)):[]}),o2=je(function(r,o){var l=Ns(o);return Et(l)&&(l=s),Et(r)?Qo(r,jt(o,1,Et,!0),s,l):[]});function i2(r,o,l){var d=r==null?0:r.length;return d?(o=l||o===s?1:Fe(o),Ts(r,o<0?0:o,d)):[]}function a2(r,o,l){var d=r==null?0:r.length;return d?(o=l||o===s?1:Fe(o),o=d-o,Ts(r,0,o<0?0:o)):[]}function l2(r,o){return r&&r.length?Ia(r,De(o,3),!0,!0):[]}function u2(r,o){return r&&r.length?Ia(r,De(o,3),!0):[]}function c2(r,o,l,d){var g=r==null?0:r.length;return g?(l&&typeof l!="number"&&rs(r,o,l)&&(l=0,d=g),sM(r,o,l,d)):[]}function og(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var g=l==null?0:Fe(l);return g<0&&(g=Mt(d+g,0)),ua(r,De(o,3),g)}function ig(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var g=d-1;return l!==s&&(g=Fe(l),g=l<0?Mt(d+g,0):Wt(g,d-1)),ua(r,De(o,3),g,!0)}function ag(r){var o=r==null?0:r.length;return o?jt(r,1):[]}function d2(r){var o=r==null?0:r.length;return o?jt(r,Oe):[]}function f2(r,o){var l=r==null?0:r.length;return l?(o=o===s?1:Fe(o),jt(r,o)):[]}function h2(r){for(var o=-1,l=r==null?0:r.length,d={};++o<l;){var g=r[o];d[g[0]]=g[1]}return d}function lg(r){return r&&r.length?r[0]:s}function p2(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var g=l==null?0:Fe(l);return g<0&&(g=Mt(d+g,0)),jn(r,o,g)}function m2(r){var o=r==null?0:r.length;return o?Ts(r,0,-1):[]}var g2=je(function(r){var o=gt(r,rc);return o.length&&o[0]===r[0]?Gu(o):[]}),v2=je(function(r){var o=Ns(r),l=gt(r,rc);return o===Ns(l)?o=s:l.pop(),l.length&&l[0]===r[0]?Gu(l,De(o,2)):[]}),_2=je(function(r){var o=Ns(r),l=gt(r,rc);return o=typeof o=="function"?o:s,o&&l.pop(),l.length&&l[0]===r[0]?Gu(l,s,o):[]});function y2(r,o){return r==null?"":mA.call(r,o)}function Ns(r){var o=r==null?0:r.length;return o?r[o-1]:s}function b2(r,o,l){var d=r==null?0:r.length;if(!d)return-1;var g=d;return l!==s&&(g=Fe(l),g=g<0?Mt(d+g,0):Wt(g,d-1)),o===o?XI(r,o,g):ua(r,jp,g,!0)}function w2(r,o){return r&&r.length?ym(r,Fe(o)):s}var E2=je(ug);function ug(r,o){return r&&r.length&&o&&o.length?Zu(r,o):r}function C2(r,o,l){return r&&r.length&&o&&o.length?Zu(r,o,De(l,2)):r}function D2(r,o,l){return r&&r.length&&o&&o.length?Zu(r,o,s,l):r}var x2=wr(function(r,o){var l=r==null?0:r.length,d=Hu(r,o);return Em(r,gt(o,function(g){return Er(g,l)?+g:g}).sort(Mm)),d});function O2(r,o){var l=[];if(!(r&&r.length))return l;var d=-1,g=[],y=r.length;for(o=De(o,3);++d<y;){var E=r[d];o(E,d,r)&&(l.push(E),g.push(d))}return Em(r,g),l}function vc(r){return r==null?r:yA.call(r)}function S2(r,o,l){var d=r==null?0:r.length;return d?(l&&typeof l!="number"&&rs(r,o,l)?(o=0,l=d):(o=o==null?0:Fe(o),l=l===s?d:Fe(l)),Ts(r,o,l)):[]}function T2(r,o){return Na(r,o)}function N2(r,o,l){return ec(r,o,De(l,2))}function I2(r,o){var l=r==null?0:r.length;if(l){var d=Na(r,o);if(d<l&&$s(r[d],o))return d}return-1}function A2(r,o){return Na(r,o,!0)}function M2(r,o,l){return ec(r,o,De(l,2),!0)}function P2(r,o){var l=r==null?0:r.length;if(l){var d=Na(r,o,!0)-1;if($s(r[d],o))return d}return-1}function k2(r){return r&&r.length?Dm(r):[]}function V2(r,o){return r&&r.length?Dm(r,De(o,2)):[]}function R2(r){var o=r==null?0:r.length;return o?Ts(r,1,o):[]}function U2(r,o,l){return r&&r.length?(o=l||o===s?1:Fe(o),Ts(r,0,o<0?0:o)):[]}function L2(r,o,l){var d=r==null?0:r.length;return d?(o=l||o===s?1:Fe(o),o=d-o,Ts(r,o<0?0:o,d)):[]}function F2(r,o){return r&&r.length?Ia(r,De(o,3),!1,!0):[]}function B2(r,o){return r&&r.length?Ia(r,De(o,3)):[]}var $2=je(function(r){return qr(jt(r,1,Et,!0))}),j2=je(function(r){var o=Ns(r);return Et(o)&&(o=s),qr(jt(r,1,Et,!0),De(o,2))}),H2=je(function(r){var o=Ns(r);return o=typeof o=="function"?o:s,qr(jt(r,1,Et,!0),s,o)});function q2(r){return r&&r.length?qr(r):[]}function z2(r,o){return r&&r.length?qr(r,De(o,2)):[]}function W2(r,o){return o=typeof o=="function"?o:s,r&&r.length?qr(r,s,o):[]}function _c(r){if(!(r&&r.length))return[];var o=0;return r=Fr(r,function(l){if(Et(l))return o=Mt(l.length,o),!0}),Vu(o,function(l){return gt(r,Mu(l))})}function cg(r,o){if(!(r&&r.length))return[];var l=_c(r);return o==null?l:gt(l,function(d){return ms(o,s,d)})}var G2=je(function(r,o){return Et(r)?Qo(r,o):[]}),K2=je(function(r){return sc(Fr(r,Et))}),Y2=je(function(r){var o=Ns(r);return Et(o)&&(o=s),sc(Fr(r,Et),De(o,2))}),Q2=je(function(r){var o=Ns(r);return o=typeof o=="function"?o:s,sc(Fr(r,Et),s,o)}),Z2=je(_c);function J2(r,o){return Tm(r||[],o||[],Yo)}function X2(r,o){return Tm(r||[],o||[],Xo)}var eP=je(function(r){var o=r.length,l=o>1?r[o-1]:s;return l=typeof l=="function"?(r.pop(),l):s,cg(r,l)});function dg(r){var o=_(r);return o.__chain__=!0,o}function tP(r,o){return o(r),r}function Fa(r,o){return o(r)}var sP=wr(function(r){var o=r.length,l=o?r[0]:0,d=this.__wrapped__,g=function(y){return Hu(y,r)};return o>1||this.__actions__.length||!(d instanceof We)||!Er(l)?this.thru(g):(d=d.slice(l,+l+(o?1:0)),d.__actions__.push({func:Fa,args:[g],thisArg:s}),new Os(d,this.__chain__).thru(function(y){return o&&!y.length&&y.push(s),y}))});function rP(){return dg(this)}function nP(){return new Os(this.value(),this.__chain__)}function oP(){this.__values__===s&&(this.__values__=xg(this.value()));var r=this.__index__>=this.__values__.length,o=r?s:this.__values__[this.__index__++];return{done:r,value:o}}function iP(){return this}function aP(r){for(var o,l=this;l instanceof Da;){var d=ng(l);d.__index__=0,d.__values__=s,o?g.__wrapped__=d:o=d;var g=d;l=l.__wrapped__}return g.__wrapped__=r,o}function lP(){var r=this.__wrapped__;if(r instanceof We){var o=r;return this.__actions__.length&&(o=new We(this)),o=o.reverse(),o.__actions__.push({func:Fa,args:[vc],thisArg:s}),new Os(o,this.__chain__)}return this.thru(vc)}function uP(){return Sm(this.__wrapped__,this.__actions__)}var cP=Aa(function(r,o,l){it.call(r,l)?++r[l]:yr(r,l,1)});function dP(r,o,l){var d=Re(r)?Bp:tM;return l&&rs(r,o,l)&&(o=s),d(r,De(o,3))}function fP(r,o){var l=Re(r)?Fr:cm;return l(r,De(o,3))}var hP=Lm(og),pP=Lm(ig);function mP(r,o){return jt(Ba(r,o),1)}function gP(r,o){return jt(Ba(r,o),Oe)}function vP(r,o,l){return l=l===s?1:Fe(l),jt(Ba(r,o),l)}function fg(r,o){var l=Re(r)?Ds:Hr;return l(r,De(o,3))}function hg(r,o){var l=Re(r)?RI:um;return l(r,De(o,3))}var _P=Aa(function(r,o,l){it.call(r,l)?r[l].push(o):yr(r,l,[o])});function yP(r,o,l,d){r=is(r)?r:eo(r),l=l&&!d?Fe(l):0;var g=r.length;return l<0&&(l=Mt(g+l,0)),za(r)?l<=g&&r.indexOf(o,l)>-1:!!g&&jn(r,o,l)>-1}var bP=je(function(r,o,l){var d=-1,g=typeof o=="function",y=is(r)?M(r.length):[];return Hr(r,function(E){y[++d]=g?ms(o,E,l):Zo(E,o,l)}),y}),wP=Aa(function(r,o,l){yr(r,l,o)});function Ba(r,o){var l=Re(r)?gt:gm;return l(r,De(o,3))}function EP(r,o,l,d){return r==null?[]:(Re(o)||(o=o==null?[]:[o]),l=d?s:l,Re(l)||(l=l==null?[]:[l]),bm(r,o,l))}var CP=Aa(function(r,o,l){r[l?0:1].push(o)},function(){return[[],[]]});function DP(r,o,l){var d=Re(r)?Iu:qp,g=arguments.length<3;return d(r,De(o,4),l,g,Hr)}function xP(r,o,l){var d=Re(r)?UI:qp,g=arguments.length<3;return d(r,De(o,4),l,g,um)}function OP(r,o){var l=Re(r)?Fr:cm;return l(r,Ha(De(o,3)))}function SP(r){var o=Re(r)?om:yM;return o(r)}function TP(r,o,l){(l?rs(r,o,l):o===s)?o=1:o=Fe(o);var d=Re(r)?QA:bM;return d(r,o)}function NP(r){var o=Re(r)?ZA:EM;return o(r)}function IP(r){if(r==null)return 0;if(is(r))return za(r)?qn(r):r.length;var o=Gt(r);return o==b||o==K?r.size:Yu(r).length}function AP(r,o,l){var d=Re(r)?Au:CM;return l&&rs(r,o,l)&&(o=s),d(r,De(o,3))}var MP=je(function(r,o){if(r==null)return[];var l=o.length;return l>1&&rs(r,o[0],o[1])?o=[]:l>2&&rs(o[0],o[1],o[2])&&(o=[o[0]]),bm(r,jt(o,1),[])}),$a=fA||function(){return $t.Date.now()};function PP(r,o){if(typeof o!="function")throw new xs(u);return r=Fe(r),function(){if(--r<1)return o.apply(this,arguments)}}function pg(r,o,l){return o=l?s:o,o=r&&o==null?r.length:o,br(r,_e,s,s,s,s,o)}function mg(r,o){var l;if(typeof o!="function")throw new xs(u);return r=Fe(r),function(){return--r>0&&(l=o.apply(this,arguments)),r<=1&&(o=s),l}}var yc=je(function(r,o,l){var d=se;if(l.length){var g=$r(l,Jn(yc));d|=Z}return br(r,d,o,l,g)}),gg=je(function(r,o,l){var d=se|A;if(l.length){var g=$r(l,Jn(gg));d|=Z}return br(o,d,r,l,g)});function vg(r,o,l){o=l?s:o;var d=br(r,Y,s,s,s,s,s,o);return d.placeholder=vg.placeholder,d}function _g(r,o,l){o=l?s:o;var d=br(r,ye,s,s,s,s,s,o);return d.placeholder=_g.placeholder,d}function yg(r,o,l){var d,g,y,E,x,N,B=0,j=!1,z=!1,le=!0;if(typeof r!="function")throw new xs(u);o=Is(o)||0,_t(l)&&(j=!!l.leading,z="maxWait"in l,y=z?Mt(Is(l.maxWait)||0,o):y,le="trailing"in l?!!l.trailing:le);function ge(Ct){var js=d,xr=g;return d=g=s,B=Ct,E=r.apply(xr,js),E}function Se(Ct){return B=Ct,x=si(qe,o),j?ge(Ct):E}function Be(Ct){var js=Ct-N,xr=Ct-B,Lg=o-js;return z?Wt(Lg,y-xr):Lg}function Te(Ct){var js=Ct-N,xr=Ct-B;return N===s||js>=o||js<0||z&&xr>=y}function qe(){var Ct=$a();if(Te(Ct))return Ke(Ct);x=si(qe,Be(Ct))}function Ke(Ct){return x=s,le&&d?ge(Ct):(d=g=s,E)}function ys(){x!==s&&Nm(x),B=0,d=N=g=x=s}function ns(){return x===s?E:Ke($a())}function bs(){var Ct=$a(),js=Te(Ct);if(d=arguments,g=this,N=Ct,js){if(x===s)return Se(N);if(z)return Nm(x),x=si(qe,o),ge(N)}return x===s&&(x=si(qe,o)),E}return bs.cancel=ys,bs.flush=ns,bs}var kP=je(function(r,o){return lm(r,1,o)}),VP=je(function(r,o,l){return lm(r,Is(o)||0,l)});function RP(r){return br(r,ae)}function ja(r,o){if(typeof r!="function"||o!=null&&typeof o!="function")throw new xs(u);var l=function(){var d=arguments,g=o?o.apply(this,d):d[0],y=l.cache;if(y.has(g))return y.get(g);var E=r.apply(this,d);return l.cache=y.set(g,E)||y,E};return l.cache=new(ja.Cache||_r),l}ja.Cache=_r;function Ha(r){if(typeof r!="function")throw new xs(u);return function(){var o=arguments;switch(o.length){case 0:return!r.call(this);case 1:return!r.call(this,o[0]);case 2:return!r.call(this,o[0],o[1]);case 3:return!r.call(this,o[0],o[1],o[2])}return!r.apply(this,o)}}function UP(r){return mg(2,r)}var LP=DM(function(r,o){o=o.length==1&&Re(o[0])?gt(o[0],gs(De())):gt(jt(o,1),gs(De()));var l=o.length;return je(function(d){for(var g=-1,y=Wt(d.length,l);++g<y;)d[g]=o[g].call(this,d[g]);return ms(r,this,d)})}),bc=je(function(r,o){var l=$r(o,Jn(bc));return br(r,Z,s,o,l)}),bg=je(function(r,o){var l=$r(o,Jn(bg));return br(r,fe,s,o,l)}),FP=wr(function(r,o){return br(r,Ae,s,s,s,o)});function BP(r,o){if(typeof r!="function")throw new xs(u);return o=o===s?o:Fe(o),je(r,o)}function $P(r,o){if(typeof r!="function")throw new xs(u);return o=o==null?0:Mt(Fe(o),0),je(function(l){var d=l[o],g=Wr(l,0,o);return d&&Br(g,d),ms(r,this,g)})}function jP(r,o,l){var d=!0,g=!0;if(typeof r!="function")throw new xs(u);return _t(l)&&(d="leading"in l?!!l.leading:d,g="trailing"in l?!!l.trailing:g),yg(r,o,{leading:d,maxWait:o,trailing:g})}function HP(r){return pg(r,1)}function qP(r,o){return bc(nc(o),r)}function zP(){if(!arguments.length)return[];var r=arguments[0];return Re(r)?r:[r]}function WP(r){return Ss(r,D)}function GP(r,o){return o=typeof o=="function"?o:s,Ss(r,D,o)}function KP(r){return Ss(r,v|D)}function YP(r,o){return o=typeof o=="function"?o:s,Ss(r,v|D,o)}function QP(r,o){return o==null||am(r,o,kt(o))}function $s(r,o){return r===o||r!==r&&o!==o}var ZP=Va(Wu),JP=Va(function(r,o){return r>=o}),Cn=hm(function(){return arguments}())?hm:function(r){return bt(r)&&it.call(r,"callee")&&!Xp.call(r,"callee")},Re=M.isArray,XP=kp?gs(kp):aM;function is(r){return r!=null&&qa(r.length)&&!Cr(r)}function Et(r){return bt(r)&&is(r)}function ek(r){return r===!0||r===!1||bt(r)&&ss(r)==ie}var Gr=pA||Ac,tk=Vp?gs(Vp):lM;function sk(r){return bt(r)&&r.nodeType===1&&!ri(r)}function rk(r){if(r==null)return!0;if(is(r)&&(Re(r)||typeof r=="string"||typeof r.splice=="function"||Gr(r)||Xn(r)||Cn(r)))return!r.length;var o=Gt(r);if(o==b||o==K)return!r.size;if(ti(r))return!Yu(r).length;for(var l in r)if(it.call(r,l))return!1;return!0}function nk(r,o){return Jo(r,o)}function ok(r,o,l){l=typeof l=="function"?l:s;var d=l?l(r,o):s;return d===s?Jo(r,o,s,l):!!d}function wc(r){if(!bt(r))return!1;var o=ss(r);return o==ke||o==pe||typeof r.message=="string"&&typeof r.name=="string"&&!ri(r)}function ik(r){return typeof r=="number"&&tm(r)}function Cr(r){if(!_t(r))return!1;var o=ss(r);return o==ot||o==Ve||o==R||o==W}function wg(r){return typeof r=="number"&&r==Fe(r)}function qa(r){return typeof r=="number"&&r>-1&&r%1==0&&r<=we}function _t(r){var o=typeof r;return r!=null&&(o=="object"||o=="function")}function bt(r){return r!=null&&typeof r=="object"}var Eg=Rp?gs(Rp):cM;function ak(r,o){return r===o||Ku(r,o,dc(o))}function lk(r,o,l){return l=typeof l=="function"?l:s,Ku(r,o,dc(o),l)}function uk(r){return Cg(r)&&r!=+r}function ck(r){if(GM(r))throw new Pe(a);return pm(r)}function dk(r){return r===null}function fk(r){return r==null}function Cg(r){return typeof r=="number"||bt(r)&&ss(r)==C}function ri(r){if(!bt(r)||ss(r)!=L)return!1;var o=va(r);if(o===null)return!0;var l=it.call(o,"constructor")&&o.constructor;return typeof l=="function"&&l instanceof l&&ha.call(l)==lA}var Ec=Up?gs(Up):dM;function hk(r){return wg(r)&&r>=-we&&r<=we}var Dg=Lp?gs(Lp):fM;function za(r){return typeof r=="string"||!Re(r)&&bt(r)&&ss(r)==J}function _s(r){return typeof r=="symbol"||bt(r)&&ss(r)==G}var Xn=Fp?gs(Fp):hM;function pk(r){return r===s}function mk(r){return bt(r)&&Gt(r)==ne}function gk(r){return bt(r)&&ss(r)==ve}var vk=Va(Qu),_k=Va(function(r,o){return r<=o});function xg(r){if(!r)return[];if(is(r))return za(r)?Fs(r):os(r);if(qo&&r[qo])return QI(r[qo]());var o=Gt(r),l=o==b?Uu:o==K?ca:eo;return l(r)}function Dr(r){if(!r)return r===0?r:0;if(r=Is(r),r===Oe||r===-Oe){var o=r<0?-1:1;return o*Lt}return r===r?r:0}function Fe(r){var o=Dr(r),l=o%1;return o===o?l?o-l:o:0}function Og(r){return r?yn(Fe(r),0,yt):0}function Is(r){if(typeof r=="number")return r;if(_s(r))return es;if(_t(r)){var o=typeof r.valueOf=="function"?r.valueOf():r;r=_t(o)?o+"":o}if(typeof r!="string")return r===0?r:+r;r=zp(r);var l=XN.test(r);return l||tI.test(r)?PI(r.slice(2),l?2:8):JN.test(r)?es:+r}function Sg(r){return Js(r,as(r))}function yk(r){return r?yn(Fe(r),-we,we):r===0?r:0}function nt(r){return r==null?"":vs(r)}var bk=Qn(function(r,o){if(ti(o)||is(o)){Js(o,kt(o),r);return}for(var l in o)it.call(o,l)&&Yo(r,l,o[l])}),Tg=Qn(function(r,o){Js(o,as(o),r)}),Wa=Qn(function(r,o,l,d){Js(o,as(o),r,d)}),wk=Qn(function(r,o,l,d){Js(o,kt(o),r,d)}),Ek=wr(Hu);function Ck(r,o){var l=Yn(r);return o==null?l:im(l,o)}var Dk=je(function(r,o){r=lt(r);var l=-1,d=o.length,g=d>2?o[2]:s;for(g&&rs(o[0],o[1],g)&&(d=1);++l<d;)for(var y=o[l],E=as(y),x=-1,N=E.length;++x<N;){var B=E[x],j=r[B];(j===s||$s(j,Wn[B])&&!it.call(r,B))&&(r[B]=y[B])}return r}),xk=je(function(r){return r.push(s,zm),ms(Ng,s,r)});function Ok(r,o){return $p(r,De(o,3),Zs)}function Sk(r,o){return $p(r,De(o,3),zu)}function Tk(r,o){return r==null?r:qu(r,De(o,3),as)}function Nk(r,o){return r==null?r:dm(r,De(o,3),as)}function Ik(r,o){return r&&Zs(r,De(o,3))}function Ak(r,o){return r&&zu(r,De(o,3))}function Mk(r){return r==null?[]:Sa(r,kt(r))}function Pk(r){return r==null?[]:Sa(r,as(r))}function Cc(r,o,l){var d=r==null?s:bn(r,o);return d===s?l:d}function kk(r,o){return r!=null&&Km(r,o,rM)}function Dc(r,o){return r!=null&&Km(r,o,nM)}var Vk=Bm(function(r,o,l){o!=null&&typeof o.toString!="function"&&(o=pa.call(o)),r[o]=l},Oc(ls)),Rk=Bm(function(r,o,l){o!=null&&typeof o.toString!="function"&&(o=pa.call(o)),it.call(r,o)?r[o].push(l):r[o]=[l]},De),Uk=je(Zo);function kt(r){return is(r)?nm(r):Yu(r)}function as(r){return is(r)?nm(r,!0):pM(r)}function Lk(r,o){var l={};return o=De(o,3),Zs(r,function(d,g,y){yr(l,o(d,g,y),d)}),l}function Fk(r,o){var l={};return o=De(o,3),Zs(r,function(d,g,y){yr(l,g,o(d,g,y))}),l}var Bk=Qn(function(r,o,l){Ta(r,o,l)}),Ng=Qn(function(r,o,l,d){Ta(r,o,l,d)}),$k=wr(function(r,o){var l={};if(r==null)return l;var d=!1;o=gt(o,function(y){return y=zr(y,r),d||(d=y.length>1),y}),Js(r,uc(r),l),d&&(l=Ss(l,v|w|D,VM));for(var g=o.length;g--;)tc(l,o[g]);return l});function jk(r,o){return Ig(r,Ha(De(o)))}var Hk=wr(function(r,o){return r==null?{}:gM(r,o)});function Ig(r,o){if(r==null)return{};var l=gt(uc(r),function(d){return[d]});return o=De(o),wm(r,l,function(d,g){return o(d,g[0])})}function qk(r,o,l){o=zr(o,r);var d=-1,g=o.length;for(g||(g=1,r=s);++d<g;){var y=r==null?s:r[Xs(o[d])];y===s&&(d=g,y=l),r=Cr(y)?y.call(r):y}return r}function zk(r,o,l){return r==null?r:Xo(r,o,l)}function Wk(r,o,l,d){return d=typeof d=="function"?d:s,r==null?r:Xo(r,o,l,d)}var Ag=Hm(kt),Mg=Hm(as);function Gk(r,o,l){var d=Re(r),g=d||Gr(r)||Xn(r);if(o=De(o,4),l==null){var y=r&&r.constructor;g?l=d?new y:[]:_t(r)?l=Cr(y)?Yn(va(r)):{}:l={}}return(g?Ds:Zs)(r,function(E,x,N){return o(l,E,x,N)}),l}function Kk(r,o){return r==null?!0:tc(r,o)}function Yk(r,o,l){return r==null?r:Om(r,o,nc(l))}function Qk(r,o,l,d){return d=typeof d=="function"?d:s,r==null?r:Om(r,o,nc(l),d)}function eo(r){return r==null?[]:Ru(r,kt(r))}function Zk(r){return r==null?[]:Ru(r,as(r))}function Jk(r,o,l){return l===s&&(l=o,o=s),l!==s&&(l=Is(l),l=l===l?l:0),o!==s&&(o=Is(o),o=o===o?o:0),yn(Is(r),o,l)}function Xk(r,o,l){return o=Dr(o),l===s?(l=o,o=0):l=Dr(l),r=Is(r),oM(r,o,l)}function eV(r,o,l){if(l&&typeof l!="boolean"&&rs(r,o,l)&&(o=l=s),l===s&&(typeof o=="boolean"?(l=o,o=s):typeof r=="boolean"&&(l=r,r=s)),r===s&&o===s?(r=0,o=1):(r=Dr(r),o===s?(o=r,r=0):o=Dr(o)),r>o){var d=r;r=o,o=d}if(l||r%1||o%1){var g=sm();return Wt(r+g*(o-r+MI("1e-"+((g+"").length-1))),o)}return Ju(r,o)}var tV=Zn(function(r,o,l){return o=o.toLowerCase(),r+(l?Pg(o):o)});function Pg(r){return xc(nt(r).toLowerCase())}function kg(r){return r=nt(r),r&&r.replace(rI,zI).replace(EI,"")}function sV(r,o,l){r=nt(r),o=vs(o);var d=r.length;l=l===s?d:yn(Fe(l),0,d);var g=l;return l-=o.length,l>=0&&r.slice(l,g)==o}function rV(r){return r=nt(r),r&&UN.test(r)?r.replace(dp,WI):r}function nV(r){return r=nt(r),r&&HN.test(r)?r.replace(bu,"\\$&"):r}var oV=Zn(function(r,o,l){return r+(l?"-":"")+o.toLowerCase()}),iV=Zn(function(r,o,l){return r+(l?" ":"")+o.toLowerCase()}),aV=Um("toLowerCase");function lV(r,o,l){r=nt(r),o=Fe(o);var d=o?qn(r):0;if(!o||d>=o)return r;var g=(o-d)/2;return ka(wa(g),l)+r+ka(ba(g),l)}function uV(r,o,l){r=nt(r),o=Fe(o);var d=o?qn(r):0;return o&&d<o?r+ka(o-d,l):r}function cV(r,o,l){r=nt(r),o=Fe(o);var d=o?qn(r):0;return o&&d<o?ka(o-d,l)+r:r}function dV(r,o,l){return l||o==null?o=0:o&&(o=+o),_A(nt(r).replace(wu,""),o||0)}function fV(r,o,l){return(l?rs(r,o,l):o===s)?o=1:o=Fe(o),Xu(nt(r),o)}function hV(){var r=arguments,o=nt(r[0]);return r.length<3?o:o.replace(r[1],r[2])}var pV=Zn(function(r,o,l){return r+(l?"_":"")+o.toLowerCase()});function mV(r,o,l){return l&&typeof l!="number"&&rs(r,o,l)&&(o=l=s),l=l===s?yt:l>>>0,l?(r=nt(r),r&&(typeof o=="string"||o!=null&&!Ec(o))&&(o=vs(o),!o&&Hn(r))?Wr(Fs(r),0,l):r.split(o,l)):[]}var gV=Zn(function(r,o,l){return r+(l?" ":"")+xc(o)});function vV(r,o,l){return r=nt(r),l=l==null?0:yn(Fe(l),0,r.length),o=vs(o),r.slice(l,l+o.length)==o}function _V(r,o,l){var d=_.templateSettings;l&&rs(r,o,l)&&(o=s),r=nt(r),o=Wa({},o,d,qm);var g=Wa({},o.imports,d.imports,qm),y=kt(g),E=Ru(g,y),x,N,B=0,j=o.interpolate||oa,z="__p += '",le=Lu((o.escape||oa).source+"|"+j.source+"|"+(j===fp?ZN:oa).source+"|"+(o.evaluate||oa).source+"|$","g"),ge="//# sourceURL="+(it.call(o,"sourceURL")?(o.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++SI+"]")+`
`;r.replace(le,function(Te,qe,Ke,ys,ns,bs){return Ke||(Ke=ys),z+=r.slice(B,bs).replace(nI,GI),qe&&(x=!0,z+=`' +
__e(`+qe+`) +
'`),ns&&(N=!0,z+=`';
`+ns+`;
__p += '`),Ke&&(z+=`' +
((__t = (`+Ke+`)) == null ? '' : __t) +
'`),B=bs+Te.length,Te}),z+=`';
`;var Se=it.call(o,"variable")&&o.variable;if(!Se)z=`with (obj) {
`+z+`
}
`;else if(YN.test(Se))throw new Pe(c);z=(N?z.replace(Es,""):z).replace(na,"$1").replace(VN,"$1;"),z="function("+(Se||"obj")+`) {
`+(Se?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(x?", __e = _.escape":"")+(N?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+z+`return __p
}`;var Be=Rg(function(){return tt(y,ge+"return "+z).apply(s,E)});if(Be.source=z,wc(Be))throw Be;return Be}function yV(r){return nt(r).toLowerCase()}function bV(r){return nt(r).toUpperCase()}function wV(r,o,l){if(r=nt(r),r&&(l||o===s))return zp(r);if(!r||!(o=vs(o)))return r;var d=Fs(r),g=Fs(o),y=Wp(d,g),E=Gp(d,g)+1;return Wr(d,y,E).join("")}function EV(r,o,l){if(r=nt(r),r&&(l||o===s))return r.slice(0,Yp(r)+1);if(!r||!(o=vs(o)))return r;var d=Fs(r),g=Gp(d,Fs(o))+1;return Wr(d,0,g).join("")}function CV(r,o,l){if(r=nt(r),r&&(l||o===s))return r.replace(wu,"");if(!r||!(o=vs(o)))return r;var d=Fs(r),g=Wp(d,Fs(o));return Wr(d,g).join("")}function DV(r,o){var l=I,d=be;if(_t(o)){var g="separator"in o?o.separator:g;l="length"in o?Fe(o.length):l,d="omission"in o?vs(o.omission):d}r=nt(r);var y=r.length;if(Hn(r)){var E=Fs(r);y=E.length}if(l>=y)return r;var x=l-qn(d);if(x<1)return d;var N=E?Wr(E,0,x).join(""):r.slice(0,x);if(g===s)return N+d;if(E&&(x+=N.length-x),Ec(g)){if(r.slice(x).search(g)){var B,j=N;for(g.global||(g=Lu(g.source,nt(hp.exec(g))+"g")),g.lastIndex=0;B=g.exec(j);)var z=B.index;N=N.slice(0,z===s?x:z)}}else if(r.indexOf(vs(g),x)!=x){var le=N.lastIndexOf(g);le>-1&&(N=N.slice(0,le))}return N+d}function xV(r){return r=nt(r),r&&RN.test(r)?r.replace(cp,eA):r}var OV=Zn(function(r,o,l){return r+(l?" ":"")+o.toUpperCase()}),xc=Um("toUpperCase");function Vg(r,o,l){return r=nt(r),o=l?s:o,o===s?YI(r)?rA(r):BI(r):r.match(o)||[]}var Rg=je(function(r,o){try{return ms(r,s,o)}catch(l){return wc(l)?l:new Pe(l)}}),SV=wr(function(r,o){return Ds(o,function(l){l=Xs(l),yr(r,l,yc(r[l],r))}),r});function TV(r){var o=r==null?0:r.length,l=De();return r=o?gt(r,function(d){if(typeof d[1]!="function")throw new xs(u);return[l(d[0]),d[1]]}):[],je(function(d){for(var g=-1;++g<o;){var y=r[g];if(ms(y[0],this,d))return ms(y[1],this,d)}})}function NV(r){return eM(Ss(r,v))}function Oc(r){return function(){return r}}function IV(r,o){return r==null||r!==r?o:r}var AV=Fm(),MV=Fm(!0);function ls(r){return r}function Sc(r){return mm(typeof r=="function"?r:Ss(r,v))}function PV(r){return vm(Ss(r,v))}function kV(r,o){return _m(r,Ss(o,v))}var VV=je(function(r,o){return function(l){return Zo(l,r,o)}}),RV=je(function(r,o){return function(l){return Zo(r,l,o)}});function Tc(r,o,l){var d=kt(o),g=Sa(o,d);l==null&&!(_t(o)&&(g.length||!d.length))&&(l=o,o=r,r=this,g=Sa(o,kt(o)));var y=!(_t(l)&&"chain"in l)||!!l.chain,E=Cr(r);return Ds(g,function(x){var N=o[x];r[x]=N,E&&(r.prototype[x]=function(){var B=this.__chain__;if(y||B){var j=r(this.__wrapped__),z=j.__actions__=os(this.__actions__);return z.push({func:N,args:arguments,thisArg:r}),j.__chain__=B,j}return N.apply(r,Br([this.value()],arguments))})}),r}function UV(){return $t._===this&&($t._=uA),this}function Nc(){}function LV(r){return r=Fe(r),je(function(o){return ym(o,r)})}var FV=ic(gt),BV=ic(Bp),$V=ic(Au);function Ug(r){return hc(r)?Mu(Xs(r)):vM(r)}function jV(r){return function(o){return r==null?s:bn(r,o)}}var HV=$m(),qV=$m(!0);function Ic(){return[]}function Ac(){return!1}function zV(){return{}}function WV(){return""}function GV(){return!0}function KV(r,o){if(r=Fe(r),r<1||r>we)return[];var l=yt,d=Wt(r,yt);o=De(o),r-=yt;for(var g=Vu(d,o);++l<r;)o(l);return g}function YV(r){return Re(r)?gt(r,Xs):_s(r)?[r]:os(rg(nt(r)))}function QV(r){var o=++aA;return nt(r)+o}var ZV=Pa(function(r,o){return r+o},0),JV=ac("ceil"),XV=Pa(function(r,o){return r/o},1),eR=ac("floor");function tR(r){return r&&r.length?Oa(r,ls,Wu):s}function sR(r,o){return r&&r.length?Oa(r,De(o,2),Wu):s}function rR(r){return Hp(r,ls)}function nR(r,o){return Hp(r,De(o,2))}function oR(r){return r&&r.length?Oa(r,ls,Qu):s}function iR(r,o){return r&&r.length?Oa(r,De(o,2),Qu):s}var aR=Pa(function(r,o){return r*o},1),lR=ac("round"),uR=Pa(function(r,o){return r-o},0);function cR(r){return r&&r.length?ku(r,ls):0}function dR(r,o){return r&&r.length?ku(r,De(o,2)):0}return _.after=PP,_.ary=pg,_.assign=bk,_.assignIn=Tg,_.assignInWith=Wa,_.assignWith=wk,_.at=Ek,_.before=mg,_.bind=yc,_.bindAll=SV,_.bindKey=gg,_.castArray=zP,_.chain=dg,_.chunk=e2,_.compact=t2,_.concat=s2,_.cond=TV,_.conforms=NV,_.constant=Oc,_.countBy=cP,_.create=Ck,_.curry=vg,_.curryRight=_g,_.debounce=yg,_.defaults=Dk,_.defaultsDeep=xk,_.defer=kP,_.delay=VP,_.difference=r2,_.differenceBy=n2,_.differenceWith=o2,_.drop=i2,_.dropRight=a2,_.dropRightWhile=l2,_.dropWhile=u2,_.fill=c2,_.filter=fP,_.flatMap=mP,_.flatMapDeep=gP,_.flatMapDepth=vP,_.flatten=ag,_.flattenDeep=d2,_.flattenDepth=f2,_.flip=RP,_.flow=AV,_.flowRight=MV,_.fromPairs=h2,_.functions=Mk,_.functionsIn=Pk,_.groupBy=_P,_.initial=m2,_.intersection=g2,_.intersectionBy=v2,_.intersectionWith=_2,_.invert=Vk,_.invertBy=Rk,_.invokeMap=bP,_.iteratee=Sc,_.keyBy=wP,_.keys=kt,_.keysIn=as,_.map=Ba,_.mapKeys=Lk,_.mapValues=Fk,_.matches=PV,_.matchesProperty=kV,_.memoize=ja,_.merge=Bk,_.mergeWith=Ng,_.method=VV,_.methodOf=RV,_.mixin=Tc,_.negate=Ha,_.nthArg=LV,_.omit=$k,_.omitBy=jk,_.once=UP,_.orderBy=EP,_.over=FV,_.overArgs=LP,_.overEvery=BV,_.overSome=$V,_.partial=bc,_.partialRight=bg,_.partition=CP,_.pick=Hk,_.pickBy=Ig,_.property=Ug,_.propertyOf=jV,_.pull=E2,_.pullAll=ug,_.pullAllBy=C2,_.pullAllWith=D2,_.pullAt=x2,_.range=HV,_.rangeRight=qV,_.rearg=FP,_.reject=OP,_.remove=O2,_.rest=BP,_.reverse=vc,_.sampleSize=TP,_.set=zk,_.setWith=Wk,_.shuffle=NP,_.slice=S2,_.sortBy=MP,_.sortedUniq=k2,_.sortedUniqBy=V2,_.split=mV,_.spread=$P,_.tail=R2,_.take=U2,_.takeRight=L2,_.takeRightWhile=F2,_.takeWhile=B2,_.tap=tP,_.throttle=jP,_.thru=Fa,_.toArray=xg,_.toPairs=Ag,_.toPairsIn=Mg,_.toPath=YV,_.toPlainObject=Sg,_.transform=Gk,_.unary=HP,_.union=$2,_.unionBy=j2,_.unionWith=H2,_.uniq=q2,_.uniqBy=z2,_.uniqWith=W2,_.unset=Kk,_.unzip=_c,_.unzipWith=cg,_.update=Yk,_.updateWith=Qk,_.values=eo,_.valuesIn=Zk,_.without=G2,_.words=Vg,_.wrap=qP,_.xor=K2,_.xorBy=Y2,_.xorWith=Q2,_.zip=Z2,_.zipObject=J2,_.zipObjectDeep=X2,_.zipWith=eP,_.entries=Ag,_.entriesIn=Mg,_.extend=Tg,_.extendWith=Wa,Tc(_,_),_.add=ZV,_.attempt=Rg,_.camelCase=tV,_.capitalize=Pg,_.ceil=JV,_.clamp=Jk,_.clone=WP,_.cloneDeep=KP,_.cloneDeepWith=YP,_.cloneWith=GP,_.conformsTo=QP,_.deburr=kg,_.defaultTo=IV,_.divide=XV,_.endsWith=sV,_.eq=$s,_.escape=rV,_.escapeRegExp=nV,_.every=dP,_.find=hP,_.findIndex=og,_.findKey=Ok,_.findLast=pP,_.findLastIndex=ig,_.findLastKey=Sk,_.floor=eR,_.forEach=fg,_.forEachRight=hg,_.forIn=Tk,_.forInRight=Nk,_.forOwn=Ik,_.forOwnRight=Ak,_.get=Cc,_.gt=ZP,_.gte=JP,_.has=kk,_.hasIn=Dc,_.head=lg,_.identity=ls,_.includes=yP,_.indexOf=p2,_.inRange=Xk,_.invoke=Uk,_.isArguments=Cn,_.isArray=Re,_.isArrayBuffer=XP,_.isArrayLike=is,_.isArrayLikeObject=Et,_.isBoolean=ek,_.isBuffer=Gr,_.isDate=tk,_.isElement=sk,_.isEmpty=rk,_.isEqual=nk,_.isEqualWith=ok,_.isError=wc,_.isFinite=ik,_.isFunction=Cr,_.isInteger=wg,_.isLength=qa,_.isMap=Eg,_.isMatch=ak,_.isMatchWith=lk,_.isNaN=uk,_.isNative=ck,_.isNil=fk,_.isNull=dk,_.isNumber=Cg,_.isObject=_t,_.isObjectLike=bt,_.isPlainObject=ri,_.isRegExp=Ec,_.isSafeInteger=hk,_.isSet=Dg,_.isString=za,_.isSymbol=_s,_.isTypedArray=Xn,_.isUndefined=pk,_.isWeakMap=mk,_.isWeakSet=gk,_.join=y2,_.kebabCase=oV,_.last=Ns,_.lastIndexOf=b2,_.lowerCase=iV,_.lowerFirst=aV,_.lt=vk,_.lte=_k,_.max=tR,_.maxBy=sR,_.mean=rR,_.meanBy=nR,_.min=oR,_.minBy=iR,_.stubArray=Ic,_.stubFalse=Ac,_.stubObject=zV,_.stubString=WV,_.stubTrue=GV,_.multiply=aR,_.nth=w2,_.noConflict=UV,_.noop=Nc,_.now=$a,_.pad=lV,_.padEnd=uV,_.padStart=cV,_.parseInt=dV,_.random=eV,_.reduce=DP,_.reduceRight=xP,_.repeat=fV,_.replace=hV,_.result=qk,_.round=lR,_.runInContext=T,_.sample=SP,_.size=IP,_.snakeCase=pV,_.some=AP,_.sortedIndex=T2,_.sortedIndexBy=N2,_.sortedIndexOf=I2,_.sortedLastIndex=A2,_.sortedLastIndexBy=M2,_.sortedLastIndexOf=P2,_.startCase=gV,_.startsWith=vV,_.subtract=uR,_.sum=cR,_.sumBy=dR,_.template=_V,_.times=KV,_.toFinite=Dr,_.toInteger=Fe,_.toLength=Og,_.toLower=yV,_.toNumber=Is,_.toSafeInteger=yk,_.toString=nt,_.toUpper=bV,_.trim=wV,_.trimEnd=EV,_.trimStart=CV,_.truncate=DV,_.unescape=xV,_.uniqueId=QV,_.upperCase=OV,_.upperFirst=xc,_.each=fg,_.eachRight=hg,_.first=lg,Tc(_,function(){var r={};return Zs(_,function(o,l){it.call(_.prototype,l)||(r[l]=o)}),r}(),{chain:!1}),_.VERSION=i,Ds(["bind","bindKey","curry","curryRight","partial","partialRight"],function(r){_[r].placeholder=_}),Ds(["drop","take"],function(r,o){We.prototype[r]=function(l){l=l===s?1:Mt(Fe(l),0);var d=this.__filtered__&&!o?new We(this):this.clone();return d.__filtered__?d.__takeCount__=Wt(l,d.__takeCount__):d.__views__.push({size:Wt(l,yt),type:r+(d.__dir__<0?"Right":"")}),d},We.prototype[r+"Right"]=function(l){return this.reverse()[r](l).reverse()}}),Ds(["filter","map","takeWhile"],function(r,o){var l=o+1,d=l==vt||l==ft;We.prototype[r]=function(g){var y=this.clone();return y.__iteratees__.push({iteratee:De(g,3),type:l}),y.__filtered__=y.__filtered__||d,y}}),Ds(["head","last"],function(r,o){var l="take"+(o?"Right":"");We.prototype[r]=function(){return this[l](1).value()[0]}}),Ds(["initial","tail"],function(r,o){var l="drop"+(o?"":"Right");We.prototype[r]=function(){return this.__filtered__?new We(this):this[l](1)}}),We.prototype.compact=function(){return this.filter(ls)},We.prototype.find=function(r){return this.filter(r).head()},We.prototype.findLast=function(r){return this.reverse().find(r)},We.prototype.invokeMap=je(function(r,o){return typeof r=="function"?new We(this):this.map(function(l){return Zo(l,r,o)})}),We.prototype.reject=function(r){return this.filter(Ha(De(r)))},We.prototype.slice=function(r,o){r=Fe(r);var l=this;return l.__filtered__&&(r>0||o<0)?new We(l):(r<0?l=l.takeRight(-r):r&&(l=l.drop(r)),o!==s&&(o=Fe(o),l=o<0?l.dropRight(-o):l.take(o-r)),l)},We.prototype.takeRightWhile=function(r){return this.reverse().takeWhile(r).reverse()},We.prototype.toArray=function(){return this.take(yt)},Zs(We.prototype,function(r,o){var l=/^(?:filter|find|map|reject)|While$/.test(o),d=/^(?:head|last)$/.test(o),g=_[d?"take"+(o=="last"?"Right":""):o],y=d||/^find/.test(o);g&&(_.prototype[o]=function(){var E=this.__wrapped__,x=d?[1]:arguments,N=E instanceof We,B=x[0],j=N||Re(E),z=function(qe){var Ke=g.apply(_,Br([qe],x));return d&&le?Ke[0]:Ke};j&&l&&typeof B=="function"&&B.length!=1&&(N=j=!1);var le=this.__chain__,ge=!!this.__actions__.length,Se=y&&!le,Be=N&&!ge;if(!y&&j){E=Be?E:new We(this);var Te=r.apply(E,x);return Te.__actions__.push({func:Fa,args:[z],thisArg:s}),new Os(Te,le)}return Se&&Be?r.apply(this,x):(Te=this.thru(z),Se?d?Te.value()[0]:Te.value():Te)})}),Ds(["pop","push","shift","sort","splice","unshift"],function(r){var o=da[r],l=/^(?:push|sort|unshift)$/.test(r)?"tap":"thru",d=/^(?:pop|shift)$/.test(r);_.prototype[r]=function(){var g=arguments;if(d&&!this.__chain__){var y=this.value();return o.apply(Re(y)?y:[],g)}return this[l](function(E){return o.apply(Re(E)?E:[],g)})}}),Zs(We.prototype,function(r,o){var l=_[o];if(l){var d=l.name+"";it.call(Kn,d)||(Kn[d]=[]),Kn[d].push({name:o,func:l})}}),Kn[Ma(s,A).name]=[{name:"wrapper",func:s}],We.prototype.clone=xA,We.prototype.reverse=OA,We.prototype.value=SA,_.prototype.at=sP,_.prototype.chain=rP,_.prototype.commit=nP,_.prototype.next=oP,_.prototype.plant=aP,_.prototype.reverse=lP,_.prototype.toJSON=_.prototype.valueOf=_.prototype.value=uP,_.prototype.first=_.prototype.head,qo&&(_.prototype[qo]=iP),_},zn=nA();mn?((mn.exports=zn)._=zn,Su._=zn):$t._=zn}).call(Ro)}(Ji,Ji.exports);var Jh=Ji.exports;const Ue=async(e,t)=>{try{const s=window.M.cfg.wwwroot+"/lib/ajax/service.php?sesskey="+window.M.cfg.sesskey+"&info="+e,n=await fetch(s,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify([{index:0,methodname:e,args:t}])}),a=n.clone();try{return(await n.json())[0]}catch{return{error:await a.text()}}}catch(s){throw console.error("Erro na chamada AJAX:",s),s}};async function Qw(e={}){try{return await Ue("local_offermanager_fetch",{search_string:e.search||"",type:e.type||null,only_active:e.onlyActive===!0,page:e.page||1,per_page:e.perPage||25,sort_by:e.sortBy||"name",sort_direction:e.sortDesc?"DESC":"ASC"})}catch(t){throw new Error(t.message||"Erro ao buscar ofertas")}}async function Xh(e){try{return await Ue("local_offermanager_get",{id:e})}catch(t){throw new Error(t.message||"Erro ao buscar oferta")}}async function ep(e){try{return await Ue("local_offermanager_save",{id:e.id||0,name:e.name,description:e.description||"",type:e.type||"",audienceids:e.audiences||[]})}catch(t){throw new Error(t.message||"Erro ao salvar oferta")}}async function Zw(e){try{return await Ue("local_offermanager_delete",{id:e})}catch(t){throw new Error(t.message||"Erro ao excluir oferta")}}async function du(){const e=await Ue("local_offermanager_get_type_options",{});if(e.error)throw new Error(error.message||"Erro ao buscar opções de tipos");return e}async function Jw(e,t){try{return await Ue("local_offermanager_add_course_to_offer",{offer_id:e,course:t})}catch(s){throw new Error(s.message||"Erro ao adicionar curso à oferta")}}async function Xw(e,t){try{return await Ue("local_offermanager_delete_course",{offercourseid:t})}catch(s){throw new Error(s.message||"Erro ao remover curso da oferta")}}async function e0(e,t,s){try{return await Ue("local_offermanager_set_course_status",{id:t,status:s?1:0})}catch(i){throw new Error(i.message||"Erro ao alterar status do curso")}}async function tp(e){var t;try{const s=await Ue("local_offermanager_get_audiences",{offerid:0});return(t=s==null?void 0:s.data)!=null&&t.all_audiences?{items:s.data.all_audiences.filter(n=>n.name.toLowerCase().includes(e.toLowerCase())).map(n=>({id:n.id,name:n.name}))}:{items:[]}}catch(s){throw new Error(s)}}async function t0(e,t){try{return await Ue("local_offermanager_update_audiences",{offerid:e,audienceids:t})}catch(s){throw new Error(s)}}async function s0(e,t){try{return await Ue("local_offermanager_set_status",{id:e,status:!t})}catch(s){throw new Error(s.message||"Erro ao alterar status da oferta")}}async function Uo(e="",t=0){try{return await Ue("local_offermanager_get_categories",{search_string:e,offerid:t})}catch(s){throw new Error(s.message||"Erro ao buscar categorias")}}async function sp(e,t,s="",i=1,n=20){try{console.log(`getCoursesByCategory - Parâmetros: offerId=${e}, categoryId=${t}, search=${s}, page=${i}, perPage=${n}`);const a=parseInt(e,10),u=parseInt(t,10),c=parseInt(i,10),h=parseInt(n,10);(isNaN(a)||isNaN(u)||isNaN(c)||isNaN(h))&&console.error("Parâmetros inválidos para getCoursesByCategory");const m={offerid:a,categoryid:u,search_string:s||"",page:c,per_page:h,exclude_courseids:[]};console.log("Chamando endpoint com parâmetros:",m);const p=await Ue("local_offermanager_fetch_potential_courses",m);return console.log("Resposta bruta do endpoint:",p),p}catch(a){throw console.error("Erro em getCoursesByCategory:",a),new Error(a.message||"Erro ao buscar cursos")}}async function r0(e,t=""){try{return await Ue("local_offermanager_fetch_current_courses",{offerid:e,categoryid:0,search_string:t,exclude_courseids:[]})}catch(s){throw new Error(s.message||"Erro ao buscar cursos por nome")}}async function rp(e,t){try{return await Ue("local_offermanager_fetch_current_courses",{offerid:e,categoryid:t,search_string:"",exclude_courseids:[]})}catch(s){throw new Error(s.message||"Erro ao buscar cursos por categoria")}}async function n0(e,t){try{return await Ue("local_offermanager_add_courses",{offerid:e,courseids:t})}catch(s){throw new Error(s.message||"Erro ao adicionar cursos à oferta")}}async function fu(e,t={}){try{return t.sortBy==="name"&&(t.sortBy="fullname"),t.sortBy==="turmasCount"&&(t.sortBy="class_counter"),await Ue("local_offermanager_get_current_courses",{offerid:e,only_active:t.onlyActive||!1,courseids:t.courseIds||[],page:t.page||1,per_page:t.perPage||100,sort_by:t.sortBy||"id",sort_direction:t.sortDesc?"DESC":"ASC",course_search:t.courseSearch||"",category_search:t.categorySearch||""})}catch(s){throw new Error(s.message||"Erro ao buscar cursos da oferta")}}async function o0(e){try{const t=["enableenddate","enddate","enablepreenrolment","preenrolmentstartdate","preenrolmentenddate","description","enableenrolperiod","enrolperiod","minusers","maxusers","roleid","enablereenrol","reenrolmentsituations","enableextension","extensionperiod","extensiondaysavailable","extensionmaxrequests","extensionallowedsituations"],s={optional_fields:{}};e.offercourseid?s.offercourseid=parseInt(e.offercourseid):console.error("offercourseid não está definido nos parâmetros"),e.classname?s.classname=e.classname:console.error("classname não está definido nos parâmetros"),e.startdate?s.startdate=e.startdate:console.error("startdate não está definido nos parâmetros"),e.teachers&&Array.isArray(e.teachers)?s.teachers=[...e.teachers]:(console.warn("teachers não está definido nos parâmetros ou não é um array"),s.teachers=[]),e.enrol?s.enrol=e.enrol:console.error("enrol não está definido nos parâmetros"),e.optional_fields&&t.forEach(u=>{if(u in e.optional_fields){const c=e.optional_fields[u];["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].includes(u)?c!==0&&c!==null&&c!==void 0&&c!==""&&(s.optional_fields[u]=c):typeof c=="boolean"?s.optional_fields[u]=c:Array.isArray(c)?c.length>0&&(s.optional_fields[u]=c):c!=null&&c!==""&&(s.optional_fields[u]=c)}});const n=["offercourseid","classname","startdate","enrol"].filter(u=>!s[u]);if(n.length>0)throw console.error("Campos obrigatórios ausentes no serviço:",n),new Error(`Campos obrigatórios ausentes: ${n.join(", ")}`);return await Ue("local_offermanager_add_class",s)}catch(t){throw console.error("Erro ao criar turma:",t),new Error(t.message||"Erro ao criar turma")}}async function hu(e){try{return await Ue("local_offermanager_get_class",{offerclassid:e})}catch(t){throw new Error(t.message||"Erro ao buscar turma")}}async function i0(e){try{const t=await Ue("local_offermanager_get_course",{offercourseid:e});return t.error?[]:t}catch(t){throw new Error(t.message||"Erro ao buscar curso da oferta")}}async function a0(e){try{const t=await Ue("local_offermanager_get_classes",{offercourseid:e});return Array.isArray(t)&&t.length===0?(console.log(`Curso ${e} não tem turmas (array vazio)`),[]):Array.isArray(t)&&t.length>0&&t[0].error===!1&&Array.isArray(t[0].data)&&t[0].data.length===0?(console.log(`Curso ${e} não tem turmas (data vazio)`),[]):t}catch(t){throw console.error(`Erro ao buscar turmas do curso ${e}:`,t),new Error(t.message||"Erro ao buscar curso da oferta")}}async function l0(e){try{const t=["enableenddate","enddate","enablepreenrolment","preenrolmentstartdate","preenrolmentenddate","description","enableenrolperiod","enrolperiod","minusers","maxusers","roleid","enablereenrol","reenrolmentsituations","enableextension","extensionperiod","extensiondaysavailable","extensionmaxrequests","extensionallowedsituations"],s={offerclassid:e.offerclassid,classname:e.classname,startdate:e.startdate,teachers:e.teachers,optional_fields:{}};return e.optional_fields&&t.forEach(n=>{if(n in e.optional_fields){const a=e.optional_fields[n];["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].includes(n)?a!==0&&a!==null&&a!==void 0&&a!==""&&(s.optional_fields[n]=a):typeof a=="boolean"?s.optional_fields[n]=a:Array.isArray(a)?a.length>0&&(s.optional_fields[n]=a):a!=null&&a!==""&&(s.optional_fields[n]=a)}}),console.log("Campos enviados para a API de atualização:",Object.keys(s.optional_fields)),console.log("Objeto completo enviado para a API de atualização:",s),"enrol"in s&&delete s.enrol,await Ue("local_offermanager_update_class",s)}catch(t){throw new Error(t.message||"Erro ao atualizar turma")}}async function u0(e){try{return await Ue("local_offermanager_delete_class",{offerclassid:e})}catch(t){throw new Error(t.message||"Erro ao excluir turma")}}async function c0(e,t=0,s="",i=[]){try{return await Ue("local_offermanager_get_potential_teachers",{offercourseid:e,search_string:s,offerclassid:t,excluded_userids:i})}catch(n){throw new Error(n.message||"Erro ao buscar professores")}}async function d0(){try{const e=await Ue("local_offermanager_get_situation_list",{});if(e.error)throw new Error(e.exception.message||"Erro ao buscar situações de matrícula");return e}catch(e){throw new Error(e.message||"Erro ao buscar situações de matrícula")}}async function f0(e,t){try{if(!t)throw new Error("É necessário especificar um curso de destino para duplicar a turma");const s=parseInt(e,10),i=parseInt(t,10);if(isNaN(s)||isNaN(i))throw new Error("IDs inválidos para duplicação de turma");return await Ue("local_offermanager_duplicate_class",{offerclassid:s,targetoffercourseid:i})}catch(s){throw new Error(s.message||"Erro ao duplicar turma")}}async function h0(e){try{const t=parseInt(e,10);if(isNaN(t))throw new Error("ID da turma inválido");const s=await Ue("local_offermanager_get_duplication_courses",{offerclassid:t});let i;return s&&s.data&&Array.isArray(s.data)?i=s.data:i=s,Array.isArray(i)?i.map(a=>({id:a.id,name:a.name||a.fullname,courseid:a.courseid||null,offercourseid:a.offercourseid||a.id,categoryid:a.categoryid||null,category_name:a.category_name||""})):[]}catch(t){try{const s=await Ue("local_offermanager_get_class",{id:parseInt(e,10)});let i,n;if(s&&s.data)i=s.data.offerid,n=s.data.offercourseid;else if(s)i=s.offerid,n=s.offercourseid;else throw new Error("Não foi possível determinar a oferta da turma");const a=await Ue("local_offermanager_get_offer_courses",{offerid:parseInt(i,10)});let u=[];return a&&Array.isArray(a.data)?u=a.data:a&&a.data&&Array.isArray(a.data.courses)?u=a.data.courses:Array.isArray(a)&&(u=a),u.filter(m=>(m.id||m.offercourseid)!=n).map(m=>({id:m.id,name:m.fullname||m.name,courseid:m.courseid||null,offercourseid:m.id,categoryid:m.categoryid||null,category_name:m.category_name||""}))}catch{throw new Error(t.message||"Erro ao buscar cursos para duplicação")}}}async function pu(e){try{const t=await Ue("local_offermanager_get_course_roles",{offercourseid:e});if(t.error)throw new Error(error.message||"Erro ao buscar papéis do curso");return t}catch(t){throw new Error(t.message||"Erro ao buscar papéis do curso")}}async function np(e=!0){try{return await Ue("local_offermanager_get_class_methods",{enabled:e})}catch(t){throw new Error(t.message||"Erro ao buscar métodos de inscrição")}}async function p0(e,t){try{return await Ue("local_offermanager_set_class_status",{id:e,status:t?1:0})}catch(s){throw new Error(s.message||"Erro ao alterar status da turma")}}const gR="",m0={name:"CustomTable",props:{headers:{type:Array,required:!0},items:{type:Array,required:!0},sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1}},methods:{handleSort(e){this.$emit("sort",{sortBy:e,sortDesc:this.sortBy===e?!this.sortDesc:!1})}}},g0={class:"table-responsive"},v0={class:"table"},_0=["data-value"],y0=["onClick"],b0=["data-column"];function w0(e,t,s,i,n,a){return O(),S("div",g0,[f("table",v0,[f("thead",null,[f("tr",null,[(O(!0),S(Ie,null,at(s.headers,u=>(O(),S("th",{key:u.value,class:he({"text-right":u.align==="right"}),style:us(u.width?{width:u.width}:{}),"data-value":u.value},[u.value==="select"?Vt(e.$slots,"header-select",{key:0},()=>[Je(q(u.text),1)],!0):(O(),S(Ie,{key:1},[Je(q(u.text)+" ",1),u.sortable?(O(),S("span",{key:0,onClick:c=>u.sortable?a.handleSort(u.value):null,class:"sort-icon"},[f("i",{class:he(["fas",{"fa-sort":s.sortBy!==u.value,"fa-sort-up":s.sortBy===u.value&&!s.sortDesc,"fa-sort-down":s.sortBy===u.value&&s.sortDesc}])},null,2)],8,y0)):X("",!0)],64))],14,_0))),128))])]),f("tbody",null,[(O(!0),S(Ie,null,at(s.items,u=>(O(),S("tr",{key:u.id},[(O(!0),S(Ie,null,at(s.headers,c=>(O(),S("td",{key:c.value,class:he({"text-right":c.align==="right"}),"data-column":c.value},[Vt(e.$slots,"item-"+c.value,{item:u},()=>[Je(q(u[c.value]),1)],!0)],10,b0))),128))]))),128))])])])}const hn=He(m0,[["render",w0],["__scopeId","data-v-35ce6ca5"]]),vR="",E0={name:"CustomSelect",props:{modelValue:{type:[Number,String],default:""},options:{type:Array,required:!0},label:{type:String,default:""},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}}},methods:{handleChange(e){this.$emit("update:modelValue",e.target.value),this.hasError&&e.target.value&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate"]},C0={class:"select-wrapper"},D0=["value","disabled"],x0=["value"],O0={key:1,class:"error-message"};function S0(e,t,s,i,n,a){return O(),S("div",{ref:"selectContainer",class:"custom-select-container",style:us(a.customWidth)},[s.label?(O(),S("div",{key:0,class:he(["select-label",{disabled:s.disabled}])},q(s.label),3)):X("",!0),f("div",C0,[f("select",{value:s.modelValue,onChange:t[0]||(t[0]=(...u)=>a.handleChange&&a.handleChange(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),class:he(["custom-select",{error:s.hasError}]),disabled:s.disabled},[(O(!0),S(Ie,null,at(s.options,u=>(O(),S("option",{key:u.value,value:u.value},q(u.label),9,x0))),128))],42,D0),f("div",{class:he(["select-arrow",{disabled:s.disabled}])},null,2)]),s.hasError&&s.errorMessage?(O(),S("div",O0,q(s.errorMessage),1)):X("",!0)],4)}const mr=He(E0,[["render",S0],["__scopeId","data-v-c65f2fc1"]]),_R="",T0={name:"CustomInput",props:{modelValue:{type:[String,Number],default:""},label:{type:String,default:""},placeholder:{type:String,default:"Digite aqui..."},type:{type:String,default:"text"},hasSearchIcon:{type:Boolean,default:!1},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1},max:{type:[String,Number],default:null}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}},isDateType(){return this.type==="date"},isNumberType(){return this.type==="number"}},methods:{handleInput(e){let t=e.target.value;if(this.isNumberType&&(t.includes("-")&&(t=t.replace(/-/g,""),e.target.value=t),t!=="")){const s=parseFloat(t);s<0||isNaN(s)?(t="",e.target.value=t):this.max!==null&&s>parseFloat(this.max)&&(t=this.max.toString(),e.target.value=t,this.$emit("validate"))}this.$emit("update:modelValue",t),this.hasError&&t&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate"]},N0={key:0,class:"input-label"},I0=["type","placeholder","value","disabled","min","max"],A0={key:0,class:"search-icon"},M0={key:2,class:"error-message"};function P0(e,t,s,i,n,a){return O(),S("div",{class:"custom-input-container",style:us(a.customWidth)},[s.label?(O(),S("div",N0,q(s.label),1)):X("",!0),f("div",{class:he(["input-wrapper",{"with-icon":s.hasSearchIcon||a.isDateType}])},[f("input",{type:s.type,placeholder:s.placeholder,value:s.modelValue,onInput:t[0]||(t[0]=(...u)=>a.handleInput&&a.handleInput(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),disabled:s.disabled,class:he(["form-control custom-input",{error:s.hasError}]),min:a.isNumberType?0:null,max:s.max},null,42,I0),s.hasSearchIcon?(O(),S("div",A0,t[2]||(t[2]=[f("i",{class:"fas fa-search"},null,-1)]))):X("",!0),a.isDateType?(O(),S("div",{key:1,class:he(["calendar-icon",{disabled:s.disabled}])},t[3]||(t[3]=[f("i",{class:"fas fa-calendar-alt"},null,-1)]),2)):X("",!0),s.hasError&&s.errorMessage?(O(),S("div",M0,q(s.errorMessage),1)):X("",!0)],2)],4)}const Lo=He(T0,[["render",P0],["__scopeId","data-v-ee21b46c"]]),yR="",k0={name:"CustomCheckbox",props:{modelValue:{type:Boolean,default:!1},label:{type:String,default:""},id:{type:String,required:!0},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"]},V0=["id","checked","disabled"],R0=["for"];function U0(e,t,s,i,n,a){return O(),S("div",{class:he(["checkbox-container",{disabled:s.disabled}])},[f("input",{type:"checkbox",id:s.id,checked:s.modelValue,onChange:t[0]||(t[0]=u=>e.$emit("update:modelValue",u.target.checked)),class:"custom-checkbox",disabled:s.disabled},null,40,V0),f("label",{for:s.id,class:he(["checkbox-label",{disabled:s.disabled}])},[Vt(e.$slots,"default",{},()=>[Je(q(s.label),1)],!0)],10,R0)],2)}const Xi=He(k0,[["render",U0],["__scopeId","data-v-bb633156"]]),bR="",L0={name:"CustomButton",props:{variant:{type:String,default:"primary",validator:e=>["primary","secondary","success","danger","warning","info"].includes(e)},label:{type:String,default:""},icon:{type:String,default:""},disabled:{type:Boolean,default:!1}},emits:["click"]},F0=["disabled"],B0={key:1};function $0(e,t,s,i,n,a){return O(),S("button",{class:he(["custom-button",[`btn-${s.variant}`]]),disabled:s.disabled,onClick:t[0]||(t[0]=u=>e.$emit("click",u))},[s.icon?(O(),S("i",{key:0,class:he(s.icon)},null,2)):X("",!0),s.label?(O(),S("span",B0,q(s.label),1)):X("",!0),Vt(e.$slots,"default",{},void 0,!0)],10,F0)}const Fn=He(L0,[["render",$0],["__scopeId","data-v-9dc7585a"]]),wR="",j0={name:"FilterSection",props:{title:{type:String,default:"FILTRO"},hasActiveTags:{type:Boolean,default:!1}}},H0={class:"filter-section"},q0={key:0},z0={class:"filter-content"},W0={key:1,class:"filter-tags"};function G0(e,t,s,i,n,a){return O(),S("div",H0,[s.title?(O(),S("h2",q0,q(s.title),1)):X("",!0),f("div",z0,[Vt(e.$slots,"default",{},void 0,!0)]),s.hasActiveTags?(O(),S("div",W0,[Vt(e.$slots,"tags",{},void 0,!0)])):X("",!0)])}const op=He(j0,[["render",G0],["__scopeId","data-v-0a9c42cf"]]),ER="",K0={name:"FilterRow",props:{inline:{type:Boolean,default:!1}}};function Y0(e,t,s,i,n,a){return O(),S("div",{class:he(["filter-row",{"filter-row-inline":s.inline}])},[Vt(e.$slots,"default",{},void 0,!0)],2)}const ea=He(K0,[["render",Y0],["__scopeId","data-v-6725a4ba"]]),CR="",Q0={name:"FilterGroup",props:{label:{type:String,default:""},isCheckbox:{type:Boolean,default:!1}}},Z0={key:0,class:"filter-label"},J0={class:"filter-input"};function X0(e,t,s,i,n,a){return O(),S("div",{class:he(["filter-group",{"checkbox-group":s.isCheckbox}])},[s.label?(O(),S("div",Z0,q(s.label),1)):X("",!0),f("div",J0,[Vt(e.$slots,"default",{},void 0,!0)])],2)}const ta=He(Q0,[["render",X0],["__scopeId","data-v-f69fad7e"]]),DR="",e1={name:"FilterActions"},t1={class:"filter-actions"};function s1(e,t,s,i,n,a){return O(),S("div",t1,[Vt(e.$slots,"default",{},void 0,!0)])}const ip=He(e1,[["render",s1],["__scopeId","data-v-b9facd34"]]),xR="",r1={name:"LFLoading",props:{isLoading:{type:Boolean,default:!1}}},n1={key:0};function o1(e,t,s,i,n,a){return O(),Rt(Rf,null,{default:Ne(()=>[s.isLoading?(O(),S("div",n1,t[0]||(t[0]=[f("div",{class:"modal-overlay"},null,-1),f("div",{class:"loader-wrapper"},[f("span",{class:"loader",role:"status"},[f("span",{class:"sr-only"},"Carregando...")])],-1)]))):X("",!0)]),_:1})}const mu=He(r1,[["render",o1],["__scopeId","data-v-a4a23ca1"]]),OR="",i1={name:"Toast",props:{show:{type:Boolean,required:!0},message:{type:String,required:!0},type:{type:String,default:"success",validator:function(e){return["success","error","warning","info"].includes(e)}},duration:{type:Number,default:3e3}},computed:{icon(){return{success:"fas fa-check-circle",error:"fas fa-exclamation-circle",warning:"fas fa-exclamation-triangle",info:"fas fa-info-circle"}[this.type]},progressStyle(){return{animation:`progress ${this.duration}ms linear`}}}},a1={class:"toast-content"};function l1(e,t,s,i,n,a){return O(),Rt(Zv,{to:"body"},[k(Rf,{name:"toast"},{default:Ne(()=>[s.show?(O(),S("div",{key:0,class:he(["toast",s.type])},[f("div",a1,[f("i",{class:he(a.icon)},null,2),f("span",null,q(s.message),1)]),f("div",{class:"toast-progress",style:us(a.progressStyle)},null,4)],2)):X("",!0)]),_:1})])}const Fo=He(i1,[["render",l1],["__scopeId","data-v-4e0ca8ca"]]),SR="",u1={name:"Pagination",props:{currentPage:{type:Number,required:!0},perPage:{type:Number,required:!0},total:{type:Number,required:!0},perPageOptions:{type:Array,default:()=>[5,10,20,50]}},emits:["update:currentPage","update:perPage"],computed:{totalPages(){return Math.ceil(this.total/this.perPage)},visiblePages(){const t=Math.floor(2.5);let s=Math.max(1,this.currentPage-t),i=Math.min(this.totalPages,s+5-1);i-s+1<5&&(s=Math.max(1,i-5+1));const n=[];for(let a=s;a<=i;a++)n.push(a);return n},from(){return this.total===0?0:(this.currentPage-1)*this.perPage+1},to(){return Math.min(this.from+this.perPage-1,this.total)},perPageModel:{get(){return this.perPage},set(e){this.$emit("update:perPage",e)}}},methods:{handlePageChange(e){e>=1&&e<=this.totalPages&&this.$emit("update:currentPage",e)},handlePerPageChange(){this.$emit("update:currentPage",1)}}},c1={class:"pagination-container mt-3"},d1={class:"pagination-info"},f1=["value"],h1={class:"pagination-text"},p1={class:"pagination-controls"},m1=["disabled"],g1=["onClick"],v1=["disabled"];function _1(e,t,s,i,n,a){return O(),S("div",c1,[f("div",d1,[ut(f("select",{"onUpdate:modelValue":t[0]||(t[0]=u=>a.perPageModel=u),class:"per-page-select",onChange:t[1]||(t[1]=(...u)=>a.handlePerPageChange&&a.handlePerPageChange(...u))},[(O(!0),S(Ie,null,at(s.perPageOptions,u=>(O(),S("option",{key:u,value:u},q(u),9,f1))),128))],544),[[Kl,a.perPageModel]]),f("span",h1," Mostrando de "+q(a.from)+" até "+q(a.to)+" de "+q(s.total)+" resultados ",1)]),f("div",p1,[f("button",{class:"page-item",disabled:s.currentPage<=1,onClick:t[2]||(t[2]=u=>a.handlePageChange(s.currentPage-1))},t[4]||(t[4]=[f("i",{class:"fas fa-chevron-left"},null,-1)]),8,m1),(O(!0),S(Ie,null,at(a.visiblePages,u=>(O(),S("button",{key:u,class:he(["page-item",{active:u===s.currentPage}]),onClick:c=>a.handlePageChange(u)},q(u),11,g1))),128)),f("button",{class:"page-item",disabled:s.currentPage>=a.totalPages,onClick:t[3]||(t[3]=u=>a.handlePageChange(s.currentPage+1))},t[5]||(t[5]=[f("i",{class:"fas fa-chevron-right"},null,-1)]),8,v1)])])}const pn=He(u1,[["render",_1],["__scopeId","data-v-cd2746ef"]]),TR="",y1={name:"PageHeader",props:{title:{type:String,required:!0}}},b1={class:"page-header"},w1={class:"header-actions"};function E1(e,t,s,i,n,a){return O(),S("div",b1,[f("h2",null,q(s.title),1),f("div",w1,[Vt(e.$slots,"actions",{},void 0,!0)])])}const sa=He(y1,[["render",E1],["__scopeId","data-v-5266bf48"]]),NR="",C1={name:"Modal",components:{CustomButton:Fn},props:{show:{type:Boolean,default:!1},size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},showDefaultFooter:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Confirmar"},cancelButtonText:{type:String,default:"Cancelar"},confirmDisabled:{type:Boolean,default:!1}},emits:["close","confirm"],mounted(){document.addEventListener("keydown",this.handleKeyDown),this.show&&(document.body.style.overflow="hidden")},unmounted(){document.removeEventListener("keydown",this.handleKeyDown),document.body.style.overflow=""},watch:{show(e){document.body.style.overflow=e?"hidden":""}},methods:{handleKeyDown(e){this.show&&e.key==="Escape"&&this.$emit("close")}}},D1={class:"modal-body"},x1={key:0,class:"modal-footer"},O1={key:1,class:"modal-footer"};function S1(e,t,s,i,n,a){const u=te("custom-button");return s.show?(O(),S("div",{key:0,class:"modal-backdrop",onClick:t[3]||(t[3]=c=>s.closeOnBackdrop?e.$emit("close"):null)},[f("div",{class:he(["modal-container",[`modal-${s.size}`]]),onClick:t[2]||(t[2]=Ut(()=>{},["stop"]))},[f("div",D1,[Vt(e.$slots,"default",{},void 0,!0)]),e.$slots.footer?(O(),S("div",x1,[Vt(e.$slots,"footer",{},void 0,!0)])):s.showDefaultFooter?(O(),S("div",O1,[k(u,{variant:"secondary",label:s.cancelButtonText,onClick:t[0]||(t[0]=c=>e.$emit("close"))},null,8,["label"]),k(u,{variant:"primary",label:s.confirmButtonText,onClick:t[1]||(t[1]=c=>e.$emit("confirm")),disabled:s.confirmDisabled},null,8,["label","disabled"])])):X("",!0)],2)])):X("",!0)}const T1=He(C1,[["render",S1],["__scopeId","data-v-87998e77"]]),IR="",N1={name:"ConfirmationModal",components:{Modal:T1},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Confirmação"},message:{type:String,default:""},listTitle:{type:String,default:""},listItems:{type:Array,default:()=>[]},confirmButtonText:{type:String,default:"Confirmar"},cancelButtonText:{type:String,default:"Cancelar"},confirmDisabled:{type:Boolean,default:!1},icon:{type:String,default:"warning",validator:e=>["warning","info","error","success","question",""].includes(e)}},emits:["close","confirm"],computed:{iconClass(){return{warning:"fas fa-exclamation-triangle text-warning",info:"fas fa-info-circle text-info",error:"fas fa-times-circle text-danger",success:"fas fa-check-circle text-success",question:"fas fa-question-circle text-primary"}[this.icon]||""},hasListContent(){return this.listItems&&this.listItems.length>0}}},I1={key:0,class:"icon-container"},A1={class:"modal-custom-title"},M1={key:1,class:"message-list"},P1={key:0,class:"list-title"},k1={key:2,class:"message"},V1={class:"modal-custom-footer"},R1=["disabled"];function U1(e,t,s,i,n,a){const u=te("modal");return O(),Rt(u,{show:s.show,"confirm-button-text":s.confirmButtonText,"cancel-button-text":s.cancelButtonText,"confirm-disabled":s.confirmDisabled,size:"sm","show-default-footer":!1,onClose:t[2]||(t[2]=c=>e.$emit("close")),onConfirm:t[3]||(t[3]=c=>e.$emit("confirm"))},{default:Ne(()=>[f("div",{class:he(["confirmation-content",{"has-list":a.hasListContent}])},[s.icon?(O(),S("div",I1,[f("i",{class:he(a.iconClass)},null,2)])):X("",!0),f("h3",A1,q(s.title),1),a.hasListContent?(O(),S("div",M1,[s.listTitle?(O(),S("p",P1,q(s.listTitle),1)):X("",!0),f("ul",null,[(O(!0),S(Ie,null,at(s.listItems,(c,h)=>(O(),S("li",{key:h},q(c),1))),128))])])):(O(),S("div",k1,q(s.message),1)),f("div",V1,[f("button",{class:"btn-cancel",onClick:t[0]||(t[0]=c=>e.$emit("close"))},q(s.cancelButtonText),1),f("button",{class:"btn-danger",disabled:s.confirmDisabled,onClick:t[1]||(t[1]=c=>e.$emit("confirm"))},q(s.confirmButtonText),9,R1)])],2)]),_:1},8,["show","confirm-button-text","cancel-button-text","confirm-disabled"])}const gu=He(N1,[["render",U1],["__scopeId","data-v-3be135e0"]]),AR="",MR="",L1={name:"OfferManagerView",components:{CustomTable:hn,CustomSelect:mr,CustomInput:Lo,CustomCheckbox:Xi,CustomButton:Fn,FilterSection:op,FilterRow:ea,FilterGroup:ta,FilterActions:ip,Pagination:pn,PageHeader:sa,ConfirmationModal:gu,LFLoading:mu,Toast:Fo},setup(){return{router:Zi()}},mounted(){if(!document.querySelector('link[href*="font-awesome"]')){const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)}},data(){return{icons:{edit:Yw},inputFilters:{search:"",type:"",hideInactive:!1},appliedFilters:{search:"",type:"",hideInactive:!1},typeOptions:[],typeOptionsEnabled:!1,tableHeaders:[{text:"NOME DA OFERTA",value:"name",sortable:!0},{text:"DESCRIÇÃO",value:"description",sortable:!0},{text:"STATUS DA OFERTA",value:"status",sortable:!0},{text:"TIPO DA OFERTA",value:"type",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}],offers:[],totalOffers:0,loading:!1,error:null,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,currentPage:1,perPage:10,sortBy:"name",sortDesc:!1,showDeleteModal:!1,offerToDelete:null,showStatusModal:!1,selectedOffer:null}},computed:{typeSelectOptions(){return[{value:"",label:"Todos"},...this.typeOptions]},hasActiveFilters(){return this.appliedFilters.search||this.appliedFilters.hideInactive}},watch:{perPage(e,t){e!==t&&(this.currentPage=1,this.loadOffers())},currentPage(e,t){e!==t&&this.loadOffers()}},async created(){this.debouncedSearch=Jh.debounce(this.handleSearchInput,300),this.loadTypeOptions(),this.loadOffers()},methods:{getTypeLabel(e){if(!e)return"";const t=this.typeOptions.find(s=>s.value===e||s.label===e);return t?t.label:e},async loadTypeOptions(){const e=await du();e.data.types&&(this.typeOptionsEnabled=e.data.enabled,e.data.default&&(this.inputFilters.type=e.data.default),this.typeOptions=e.data.types.map(t=>({value:t,label:t})))},async loadOffers(){this.loading=!0,this.error=null;const e={search:this.appliedFilters.search||"",type:this.appliedFilters.type||null,onlyActive:this.appliedFilters.hideInactive===!0,page:this.currentPage,perPage:this.perPage,sortBy:this.sortBy,sortDesc:this.sortDesc},t=await Qw(e);if(t.error)throw new Error(t.message||"Erro ao carregar ofertas");this.offers=t.data.offers||[],this.totalOffers=t.data.total_items||0,this.loading=!1},async handlePageChange(e){e!==this.currentPage&&(this.currentPage=e,await this.loadOffers())},async handlePerPageChange(e){e!==this.perPage&&(this.perPage=e,this.currentPage=1,await this.loadOffers())},async clearFilters(){this.inputFilters.type,this.inputFilters={search:"",type:"",hideInactive:!1},this.appliedFilters={search:"",type:"",hideInactive:!1},this.currentPage=1,await this.loadOffers()},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,this.loadOffers()},createNewOffer(){this.router.push({name:"nova-oferta"})},editOffer(e){this.router.push({name:"editar-oferta",params:{id:e.id.toString()}})},deleteOffer(e){e.can_delete&&(this.offerToDelete=e,this.showDeleteModal=!0)},async confirmDeleteOffer(){if(this.offerToDelete)try{this.loading=!0,await Zw(this.offerToDelete.id),await this.loadOffers(),this.showSuccessMessage(`Oferta "${this.offerToDelete.name}" excluída com sucesso`),this.offerToDelete=null,this.showDeleteModal=!1}catch(e){this.error=e.message,this.showErrorMessage(`Erro ao excluir oferta "${this.offerToDelete.name}"`)}finally{this.loading=!1}},toggleOfferStatus(e){e.status===0&&!e.can_activate||(this.selectedOffer=e,this.showStatusModal=!0)},async confirmToggleStatus(){if(this.selectedOffer)try{this.loading=!0,await s0(this.selectedOffer.id,this.selectedOffer.status===1),await this.loadOffers(),this.showSuccessMessage(this.selectedOffer.status===1?`Oferta "${this.selectedOffer.name}" inativada com sucesso`:`Oferta "${this.selectedOffer.name}" inativada com sucesso`),this.selectedOffer=null,this.showStatusModal=!1}catch(e){this.error=e.message,this.showErrorMessage(this.selectedOffer.status===1?`Erro ao inativar oferta "${this.selectedOffer.name}"`:`Erro ao ativar oferta "${this.selectedOffer.name}"`)}finally{this.loading=!1}},getStatusButtonTitle(e){return e.status===1?"Desativar":e.can_activate?"Ativar":"Não é possível ativar esta oferta"},async handleTypeChange(e){this.appliedFilters.type=e,this.currentPage=1,await this.loadOffers()},async handleHideInactiveChange(e){const t=e===!0;this.inputFilters.hideInactive=t,this.appliedFilters.hideInactive=t,this.currentPage=1,await this.loadOffers()},async handleSearchInput(){(this.inputFilters.search.length>=3||this.inputFilters.search==="")&&(this.appliedFilters.search=this.inputFilters.search,this.currentPage=1,await this.loadOffers())},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},F1={id:"offer-manager-component",class:"offer-manager"},B1={class:"new-offer-container"},$1={key:0,class:"alert alert-danger"},j1={class:"table-container"},H1=["title"],q1={class:"action-buttons"},z1=["onClick"],W1=["onClick","disabled","title"],G1={key:0,class:"fas fa-eye"},K1={key:1,class:"fas fa-eye-slash"},Y1=["onClick","disabled","title"];function Q1(e,t,s,i,n,a){var ye,Z,fe,_e,Ae,ae;const u=te("CustomButton"),c=te("PageHeader"),h=te("CustomInput"),m=te("FilterGroup"),p=te("CustomSelect"),v=te("CustomCheckbox"),w=te("FilterActions"),D=te("FilterRow"),V=te("FilterSection"),F=te("CustomTable"),se=te("Pagination"),A=te("ConfirmationModal"),re=te("LFLoading"),Y=te("Toast");return O(),S("div",F1,[k(c,{title:"Gerenciar Ofertas"},{actions:Ne(()=>[f("div",B1,[k(u,{variant:"primary",label:"Nova Oferta",onClick:a.createNewOffer},null,8,["onClick"])])]),_:1}),k(V,{title:"FILTRO"},{default:Ne(()=>[k(D,{inline:!0},{default:Ne(()=>[k(m,{label:"Oferta"},{default:Ne(()=>[k(h,{modelValue:n.inputFilters.search,"onUpdate:modelValue":t[0]||(t[0]=I=>n.inputFilters.search=I),placeholder:"Buscar...",width:339,"has-search-icon":!0,onInput:e.debouncedSearch},null,8,["modelValue","onInput"])]),_:1}),n.typeOptionsEnabled?(O(),Rt(m,{key:0,label:"Tipo"},{default:Ne(()=>[k(p,{modelValue:n.inputFilters.type,"onUpdate:modelValue":[t[1]||(t[1]=I=>n.inputFilters.type=I),a.handleTypeChange],options:a.typeSelectOptions,width:144},null,8,["modelValue","options","onUpdate:modelValue"])]),_:1})):X("",!0),k(m,{"is-checkbox":!0},{default:Ne(()=>[k(v,{modelValue:n.inputFilters.hideInactive,"onUpdate:modelValue":[t[2]||(t[2]=I=>n.inputFilters.hideInactive=I),a.handleHideInactiveChange],id:"hideInactive",label:"Não exibir inativas"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),k(w,null,{default:Ne(()=>[k(u,{variant:"secondary",label:"Limpar",onClick:a.clearFilters},null,8,["onClick"])]),_:1})]),_:1})]),_:1}),n.error?(O(),S("div",$1,[t[7]||(t[7]=f("i",{class:"fas fa-exclamation-circle"},null,-1)),Je(" "+q(n.error),1)])):X("",!0),f("div",j1,[k(F,{headers:n.tableHeaders,items:n.offers,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-description":Ne(({item:I})=>[f("span",{title:I.description},q(I.description.length>50?I.description.slice(0,50)+"...":I.description),9,H1)]),"item-type":Ne(({item:I})=>[Je(q(I.type.charAt(0).toUpperCase()+I.type.slice(1)),1)]),"item-status":Ne(({item:I})=>[Je(q(I.status===1?"Ativa":"Inativa"),1)]),"item-actions":Ne(({item:I})=>[f("div",q1,[f("button",{class:"btn-action btn-edit",onClick:be=>a.editOffer(I),title:"Editar"},t[8]||(t[8]=[f("svg",{width:"16",height:"16",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[f("g",{"clip-path":"url(#clip0_9_197955)"},[f("path",{d:"M12.854 0.145905C12.7602 0.0521694 12.6331 -0.000488281 12.5005 -0.000488281C12.3679 -0.000488281 12.2408 0.0521694 12.147 0.145905L10.5 1.7929L14.207 5.49991L15.854 3.8539C15.9006 3.80746 15.9375 3.75228 15.9627 3.69154C15.9879 3.63079 16.0009 3.56567 16.0009 3.4999C16.0009 3.43414 15.9879 3.36902 15.9627 3.30827C15.9375 3.24753 15.9006 3.19235 15.854 3.1459L12.854 0.145905ZM13.5 6.2069L9.793 2.4999L3.293 8.9999H3.5C3.63261 8.9999 3.75978 9.05258 3.85355 9.14635C3.94732 9.24012 4 9.3673 4 9.4999V9.9999H4.5C4.63261 9.9999 4.75978 10.0526 4.85355 10.1464C4.94732 10.2401 5 10.3673 5 10.4999V10.9999H5.5C5.63261 10.9999 5.75978 11.0526 5.85355 11.1464C5.94732 11.2401 6 11.3673 6 11.4999V11.9999H6.5C6.63261 11.9999 6.75978 12.0526 6.85355 12.1464C6.94732 12.2401 7 12.3673 7 12.4999V12.7069L13.5 6.2069ZM6.032 13.6749C6.01095 13.619 6.00012 13.5597 6 13.4999V12.9999H5.5C5.36739 12.9999 5.24021 12.9472 5.14644 12.8535C5.05268 12.7597 5 12.6325 5 12.4999V11.9999H4.5C4.36739 11.9999 4.24021 11.9472 4.14644 11.8535C4.05268 11.7597 4 11.6325 4 11.4999V10.9999H3.5C3.36739 10.9999 3.24021 10.9472 3.14644 10.8535C3.05268 10.7597 3 10.6325 3 10.4999V9.9999H2.5C2.44022 9.99981 2.38094 9.98897 2.325 9.96791L2.146 10.1459C2.09835 10.1939 2.06093 10.251 2.036 10.3139L0.0359968 15.3139C-0.000373859 15.4048 -0.00927736 15.5043 0.0103901 15.6002C0.0300575 15.6961 0.077431 15.7841 0.146638 15.8533C0.215844 15.9225 0.30384 15.9698 0.399716 15.9895C0.495593 16.0092 0.595133 16.0003 0.685997 15.9639L5.686 13.9639C5.74886 13.939 5.80601 13.9016 5.854 13.8539L6.032 13.6759V13.6749Z",fill:"var(--primary)"})]),f("defs",null,[f("clipPath",{id:"clip0_9_197955"},[f("rect",{width:"16",height:"16",fill:"white"})])])],-1)]),8,z1),f("button",{class:he(["btn-action",I.status===1?"btn-deactivate":"btn-activate"]),onClick:be=>a.toggleOfferStatus(I),disabled:I.status===0&&!I.can_activate,title:a.getStatusButtonTitle(I)},[I.status===1?(O(),S("i",G1)):(O(),S("i",K1))],10,W1),f("button",{class:"btn-action btn-delete",onClick:be=>a.deleteOffer(I),disabled:!I.can_delete,title:I.can_delete?"Excluir":"Não é possível excluir esta oferta"},t[9]||(t[9]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,Y1)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"])]),k(se,{"current-page":n.currentPage,"onUpdate:currentPage":t[3]||(t[3]=I=>n.currentPage=I),"per-page":n.perPage,"onUpdate:perPage":t[4]||(t[4]=I=>n.perPage=I),total:n.totalOffers,loading:n.loading},null,8,["current-page","per-page","total","loading"]),k(A,{show:n.showDeleteModal,title:"A exclusão desta instância de oferta é uma ação irreversível.",message:"Todos os cursos vinculados serão desassociados e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir Oferta","cancel-button-text":"Cancelar",icon:"warning",onClose:t[5]||(t[5]=I=>n.showDeleteModal=!1),onConfirm:a.confirmDeleteOffer},null,8,["show","onConfirm"]),k(A,{show:n.showStatusModal,title:((ye=n.selectedOffer)==null?void 0:ye.status)===1?"Ao inativar esta oferta, os cursos e as turmas associadas serão tratados da seguinte forma:":"Confirmar Ativação",message:((Z=n.selectedOffer)==null?void 0:Z.status)===1?"":"Tem certeza que deseja ativar esta oferta?","list-title":((fe=n.selectedOffer)==null?void 0:fe.status)===1?"Comportamento para os cursos, turmas e matrículas:":"","list-items":((_e=n.selectedOffer)==null?void 0:_e.status)===1?["Todos os cursos contidos na oferta não serão mais disponibilizados para os usuários da oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."]:[],"confirm-button-text":((Ae=n.selectedOffer)==null?void 0:Ae.status)===1?"Inativar oferta":"Ativar","cancel-button-text":"Cancelar",icon:((ae=n.selectedOffer)==null?void 0:ae.status)===1?"warning":"question",onClose:t[6]||(t[6]=I=>n.showStatusModal=!1),onConfirm:a.confirmToggleStatus},null,8,["show","title","message","list-title","list-items","confirm-button-text","icon","onConfirm"]),k(re,{"is-loading":n.loading},null,8,["is-loading"]),k(Y,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])])}const Z1=He(L1,[["render",Q1],["__scopeId","data-v-78bb8b76"]]);async function J1(e={}){try{const t=await Ue("local_offermanager_fetch_enrolments",{offerclassid:e.offerclassid,userids:e.userids||[],page:e.page||1,perpage:e.perpage||20,orderby:e.orderby||"fullname",direction:e.direction||"ASC"});if(t.error)throw new Error(error.message||"Erro ao buscar matrículas");return t}catch(t){throw new Error(t.message||"Erro ao buscar matrículas")}}async function vu(e={}){try{const t=await Ue("local_offermanager_get_enroled_users",{offerclassid:e.offerclassid,searchstring:e.searchstring||"",fieldstring:e.fieldstring||"name",excludeduserids:e.excludeduserids||[]});if(t.error)throw new Error(t.message||"Erro ao buscar opções de filtro");return t}catch{throw new Error(response.message||"Erro ao buscar opções de filtro")}}async function X1(e={}){try{const t=await Ue("local_offermanager_enrol_users",{offerclassid:e.offerclassid,userids:e.userids||[],roleid:e.roleid||5});if(t.error)throw new Error(t.message||"Erro ao matricular usuários");return t}catch{throw new Error(response.message||"Erro ao matricular usuários")}}async function eE(e,t="",s){const i=await Ue("local_offermanager_get_potential_users_to_enrol",{offerclassid:e,search_string:t,excluded_userids:s});return i.error?(console.error("Erro na resposta de getPotentialUsersToEnrol:",i.error),[]):i}async function tE(e={}){try{const t=await Ue("local_offermanager_edit_offer_user_enrol",{offeruserenrolid:e.offeruserenrolid,status:e.status,timestart:e.timestart,timeend:e.timeend,roleid:e.roleid});return t==null?(console.error("Resposta vazia de editEnrolment"),!1):typeof t=="boolean"?t:t&&typeof t.success=="boolean"?t.success:t&&t.error===!1?t.data===!0:(console.warn("Formato de resposta não reconhecido:",t),!1)}catch(t){return console.error("Erro ao editar matrícula:",t),!1}}async function sE(e={}){try{const t=await Ue("local_offermanager_edit_offer_user_enrol_bulk",{offeruserenrolids:e.offeruserenrolids||[],status:e.status,timestart:e.timestart,timeend:e.timeend});return t&&t.error===!1&&t.data===!0?e.offeruserenrolids.map(i=>({id:i,operation_status:!0})):Array.isArray(t)?t:t&&Array.isArray(t.data)?t.data:(console.error("Formato de resposta não reconhecido:",t),[])}catch(t){throw console.error("Erro ao editar matrículas em lote:",t),t}}async function rE(e){try{const t=await Ue("local_offermanager_delete_offer_user_enrol_bulk",{offeruserenrolids:e});return t&&t.error===!1&&t.data===!0?e.map(i=>({id:i,operation_status:!0})):Array.isArray(t)?t:t&&Array.isArray(t.data)?t.data:(console.warn("Formato de resposta não reconhecido:",t),[])}catch(t){return console.error("Erro ao excluir matrículas em lote:",t),[]}}async function nE(e){try{const t=await Ue("local_offermanager_get_roles",{offeruserenrolid:e});return t?t.error?(console.error("Erro na resposta de getUserRoles:",t.error),[]):Array.isArray(t)?t:t&&Array.isArray(t.data)?t.data:[]:(console.error("Resposta vazia de getUserRoles"),[])}catch(t){return console.error("Erro ao buscar papéis do usuário:",t),[]}}async function oE(e,t){try{const s=await Ue("local_offermanager_update_roles",{offeruserenrolid:e,roleids:Array.isArray(t)?t:[t]});return s==null?(console.error("Resposta vazia de updateUserRoles"),!1):typeof s=="boolean"?s:s&&typeof s.success=="boolean"?s.success:s&&s.error===!1?s.data===!0:(console.warn("Formato de resposta não reconhecido:",s),!1)}catch(s){return console.error("Erro ao atualizar papéis do usuário:",s),!1}}const PR="",iE={name:"HierarchicalSelect",props:{modelValue:{type:String,default:""},options:{type:Array,required:!0},label:{type:String,default:""},width:{type:[String,Number],default:null},disabled:{type:Boolean,default:!1},hasError:{type:Boolean,default:!1},errorMessage:{type:String,default:""},required:{type:Boolean,default:!1}},computed:{customWidth(){return this.width?{width:typeof this.width=="number"?`${this.width}px`:this.width}:{}}},methods:{handleChange(e){this.$emit("update:modelValue",e.target.value),this.$emit("navigate",e.target.value),this.hasError&&e.target.value&&this.$emit("validate")},handleBlur(e){this.required&&this.$emit("validate")}},emits:["update:modelValue","validate","navigate"]},aE={class:"select-wrapper"},lE=["value","disabled"],uE=["label"],cE=["value"],dE={key:1,class:"error-message"};function fE(e,t,s,i,n,a){return O(),S("div",{ref:"selectContainer",class:"hierarchical-select-container",style:us(a.customWidth)},[s.label?(O(),S("div",{key:0,class:he(["select-label",{disabled:s.disabled}])},q(s.label),3)):X("",!0),f("div",aE,[f("select",{value:s.modelValue,onChange:t[0]||(t[0]=(...u)=>a.handleChange&&a.handleChange(...u)),onBlur:t[1]||(t[1]=(...u)=>a.handleBlur&&a.handleBlur(...u)),class:he(["hierarchical-select",{error:s.hasError}]),disabled:s.disabled},[(O(!0),S(Ie,null,at(s.options,u=>(O(),S("optgroup",{key:u.value,label:u.label},[(O(!0),S(Ie,null,at(u.children,c=>(O(),S("option",{key:c.value,value:c.value,class:"child-option"},q(c.label),9,cE))),128))],8,uE))),128))],42,lE),f("div",{class:he(["select-arrow",{disabled:s.disabled}])},null,2)]),s.hasError&&s.errorMessage?(O(),S("div",dE,q(s.errorMessage),1)):X("",!0)],4)}const hE=He(iE,[["render",fE],["__scopeId","data-v-ca8af705"]]),kR="",pE={name:"FilterTag",emits:["remove"]};function mE(e,t,s,i,n,a){return O(),S("div",{class:"tag badge badge-primary",onClick:t[0]||(t[0]=u=>e.$emit("remove"))},[t[1]||(t[1]=f("i",{class:"fas fa-times"},null,-1)),Vt(e.$slots,"default",{},void 0,!0)])}const Bo=He(pE,[["render",mE],["__scopeId","data-v-cf6f2168"]]),VR="",gE={name:"FilterTags"},vE={class:"filter-tags"};function _E(e,t,s,i,n,a){return O(),S("div",vE,[Vt(e.$slots,"default",{},void 0,!0)])}const ra=He(gE,[["render",_E],["__scopeId","data-v-746bf68d"]]),RR="",yE={name:"Autocomplete",components:{FilterTag:Bo,FilterTags:ra},props:{modelValue:{type:[Array,String,Number],default:()=>[]},items:{type:Array,default:()=>[]},placeholder:{type:String,default:""},label:{type:String,default:""},width:{type:[Number,String],default:"auto"},required:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},minChars:{type:Number,default:3},showAllOption:{type:Boolean,default:!1},inputMaxWidth:{type:[String,Number],default:null},autoOpen:{type:Boolean,default:!0},noResultsText:{type:String,default:"Nenhum item disponível"},hasSearchIcon:{type:Boolean,default:!1},showFilterTags:{type:Boolean,default:!0},showSelectedInInput:{type:Boolean,default:!1},maxLabelLength:{type:Number,default:30},loading:{type:Boolean,default:!1},keepOpenOnSelect:{type:Boolean,default:!1}},emits:["update:modelValue","select","select-all","load-more","search"],data(){return{searchQuery:"",isOpen:!1,selectedIndex:-1,internalItems:[],uniqueId:`autocomplete-${Math.random().toString(36).substring(2,9)}`,focusedOptionIndex:-1,blurTimeout:null,debouncedSearch:null}},computed:{displayItems(){let e=this.internalItems;if(this.searchQuery){const t=this.searchQuery.toLowerCase();e=this.internalItems.filter(s=>s.label.toLowerCase().includes(t))}return this.showAllOption&&Array.isArray(this.modelValue)?[{label:"Todos",value:"__ALL__"},...e]:e},inputMaxWidthStyle(){return this.inputMaxWidth?typeof this.inputMaxWidth=="number"?`${this.inputMaxWidth}px`:this.inputMaxWidth:null},getSelectedItemLabel(){if(!this.modelValue)return"";const e=this.internalItems.find(t=>t.value===this.modelValue);return e?e.label:""}},created(){this.debouncedSearch=Jh.debounce(e=>{this.$emit("search",e)},300)},watch:{items:{handler(e){this.internalItems=Array.isArray(e)?[...e]:[],this.autoOpen&&this.keepOpenOnSelect&&!this.disabled&&this.internalItems.length>0&&this.$refs.inputElement===document.activeElement&&(this.isOpen=!0)},immediate:!0,deep:!0},searchQuery(e){this.isOpen=!0,this.selectedIndex=-1,(e.length===0||e.length>=this.minChars)&&this.debouncedSearch(e)}},methods:{handleFocus(){this.autoOpen&&!this.disabled&&(this.isOpen=!0,this.selectedIndex=-1,this.searchQuery&&(this.searchQuery="",this.$emit("search",""))),this.blurTimeout&&(clearTimeout(this.blurTimeout),this.blurTimeout=null)},openDropdown(){this.disabled||(this.isOpen=!0)},handleBlur(){this.blurTimeout=setTimeout(()=>{this.$el.contains(document.activeElement)||(this.isOpen=!1,this.selectedIndex=-1)},150)},handleInput(){this.disabled||(this.isOpen=!0)},selectItem(e){if(e.value==="__ALL__"){Array.isArray(this.modelValue)&&this.$emit("select-all"),this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1,this.$nextTick(()=>{this.focusInput()});return}if(Array.isArray(this.modelValue)){const t=[...this.modelValue],s=t.findIndex(i=>i.value===e.value);s===-1?t.push(e):t.splice(s,1),this.$emit("update:modelValue",t)}else this.$emit("update:modelValue",e.value),this.$emit("select",e);this.searchQuery="",this.isOpen=!!this.keepOpenOnSelect,this.selectedIndex=-1,this.$nextTick(()=>{this.autoOpen&&this.focusInput()})},removeItem(e){if(Array.isArray(this.modelValue)){const t=this.modelValue.filter(s=>s.value!==e.value);this.$emit("update:modelValue",t)}else this.$emit("update:modelValue","");Array.isArray(this.modelValue)?(this.searchQuery="",this.isOpen=!1,this.selectedIndex=-1):(this.selectedIndex=-1,this.$nextTick(()=>{this.isOpen=!1})),this.$nextTick(()=>{this.focusInput()})},removeSelectedItem(){this.$emit("update:modelValue",""),this.searchQuery="",this.selectedIndex=-1,this.$nextTick(()=>{this.focusInput()})},handleKeydown(e){if(!this.isOpen&&e.key!=="Tab"){this.isOpen=!0;return}switch(e.key){case"ArrowDown":e.preventDefault(),e.stopPropagation(),this.selectedIndex=Math.min(this.selectedIndex+1,this.displayItems.length-1),this.focusOption(this.selectedIndex);break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),this.selectedIndex=Math.max(this.selectedIndex-1,-1),this.selectedIndex===-1?this.focusInput():this.focusOption(this.selectedIndex);break;case"Enter":e.preventDefault(),this.selectedIndex>=0?this.selectItem(this.displayItems[this.selectedIndex]):this.searchQuery&&this.searchQuery.length>=this.minChars&&this.$emit("search",this.searchQuery);break;case"Escape":e.preventDefault(),this.isOpen=!1,this.selectedIndex=-1;break;case"Tab":this.isOpen&&!e.shiftKey&&this.displayItems.length>0&&(e.preventDefault(),e.stopPropagation(),this.selectedIndex=0,this.focusOption(0));break}},handleOptionKeydown(e,t,s){switch(e.key){case"ArrowDown":e.preventDefault(),e.stopPropagation(),s<this.displayItems.length-1&&(this.selectedIndex=s+1,this.focusOption(this.selectedIndex));break;case"ArrowUp":e.preventDefault(),e.stopPropagation(),s>0?(this.selectedIndex=s-1,this.focusOption(this.selectedIndex)):(this.selectedIndex=-1,this.focusInput());break;case"Enter":case" ":e.preventDefault(),this.selectItem(t);break;case"Escape":e.preventDefault(),this.isOpen=!1,this.selectedIndex=-1,this.focusInput();break;case"Tab":e.shiftKey?(e.preventDefault(),e.stopPropagation(),s>0?(this.selectedIndex=s-1,this.focusOption(this.selectedIndex)):(this.selectedIndex=-1,this.focusInput())):(e.preventDefault(),e.stopPropagation(),s<this.displayItems.length-1?(this.selectedIndex=s+1,this.focusOption(this.selectedIndex)):(this.selectedIndex=0,this.focusOption(0)));break}},focusInput(){this.$refs.inputElement&&this.$refs.inputElement.focus()},focusOption(e){requestAnimationFrame(()=>{var s;const t=(s=this.$refs.optionElements)==null?void 0:s[e];t&&t.focus()})},handleClickOutside(e){this.$el.contains(e.target)||(this.isOpen=!1,this.selectedIndex=-1)},truncateLabel(e){return e?e.length<=this.maxLabelLength?e:e.substring(0,this.maxLabelLength)+"...":""},handleScroll(e){if(!e||!e.target)return;const t=e.target;t.scrollHeight&&t.scrollTop!==void 0&&t.clientHeight&&t.scrollHeight-t.scrollTop-t.clientHeight<50&&this.$emit("load-more")}},mounted(){document.addEventListener("click",this.handleClickOutside),this.autoOpen&&!this.disabled&&this.internalItems.length>0&&this.$nextTick(()=>{this.isOpen=!0})},beforeUnmount(){document.removeEventListener("click",this.handleClickOutside)}},bE={class:"autocomplete-container"},wE=["id"],EE={class:"autocomplete-wrapper"},CE=["placeholder","disabled","aria-expanded","aria-owns","aria-labelledby","aria-controls"],DE={key:0,class:"selected-item"},xE=["title"],OE=["id"],SE=["id","data-index","aria-selected","tabindex","onClick","onKeydown","title"],TE={class:"item-label"},NE={key:0,class:"fas fa-check"},IE={key:0,class:"dropdown-item loading-item"},AE={key:1,class:"dropdown-item no-results"},ME={key:0,class:"tags-container"};function PE(e,t,s,i,n,a){const u=te("FilterTag"),c=te("FilterTags");return O(),S("div",bE,[s.label?(O(),S("label",{key:0,class:he(["filter-label",{required:s.required}]),id:`${n.uniqueId}-label`},q(s.label),11,wE)):X("",!0),f("div",EE,[f("div",{class:"input-container",style:us({maxWidth:a.inputMaxWidthStyle})},[f("div",{class:he(["input-wrapper",{"has-search-icon":s.hasSearchIcon,"has-selected-item":s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery}])},[ut(f("input",{type:"text",class:"form-control",placeholder:s.placeholder,"onUpdate:modelValue":t[0]||(t[0]=h=>n.searchQuery=h),disabled:s.disabled,"aria-expanded":n.isOpen,"aria-owns":`${n.uniqueId}-listbox`,"aria-labelledby":s.label?`${n.uniqueId}-label`:void 0,"aria-autocomplete":"list","aria-controls":`${n.uniqueId}-listbox`,role:"combobox",tabindex:"0",onKeydown:t[1]||(t[1]=(...h)=>a.handleKeydown&&a.handleKeydown(...h)),onFocus:t[2]||(t[2]=h=>!s.disabled&&a.handleFocus),onInput:t[3]||(t[3]=(...h)=>a.handleInput&&a.handleInput(...h)),onClick:t[4]||(t[4]=h=>!s.disabled&&a.openDropdown()),onBlur:t[5]||(t[5]=(...h)=>a.handleBlur&&a.handleBlur(...h)),ref:"inputElement"},null,40,CE),[[Xt,n.searchQuery]]),s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery?(O(),S("div",DE,[f("span",{class:"selected-text",title:a.getSelectedItemLabel},q(a.truncateLabel(a.getSelectedItemLabel)),9,xE),f("i",{class:"fas fa-times remove-selected",onClick:t[6]||(t[6]=Ut((...h)=>a.removeSelectedItem&&a.removeSelectedItem(...h),["stop"]))})])):X("",!0),s.hasSearchIcon&&!(s.showSelectedInInput&&!Array.isArray(s.modelValue)&&s.modelValue&&!n.isOpen&&!n.searchQuery)?(O(),S("i",{key:1,class:he(["search-icon",{"fas fa-search":!s.loading,"spinner-border spinner-border-sm":s.loading}])},null,2)):X("",!0)],2),n.isOpen?(O(),S("div",{key:0,class:"dropdown-menu show",id:`${n.uniqueId}-listbox`,role:"listbox",tabindex:"-1",ref:"dropdownMenu",onScroll:t[7]||(t[7]=(...h)=>a.handleScroll&&a.handleScroll(...h))},[a.displayItems.length>0?(O(),S(Ie,{key:0},[(O(!0),S(Ie,null,at(a.displayItems,(h,m)=>(O(),S("div",{key:h.value==="__ALL__"?"__ALL__":h.value,class:he(["dropdown-item",{active:n.selectedIndex===m,selected:h.value!=="__ALL__"&&(Array.isArray(s.modelValue)?s.modelValue.some(p=>p.value===h.value):s.modelValue===h.value)}]),id:`${n.uniqueId}-option-${m}`,role:"option","data-index":m,"aria-selected":n.selectedIndex===m,tabindex:n.selectedIndex===m?0:-1,onClick:p=>a.selectItem(h),onKeydown:p=>a.handleOptionKeydown(p,h,m),ref_for:!0,ref:"optionElements",title:h.label},[f("span",TE,q(a.truncateLabel(h.label)),1),h.value!=="__ALL__"&&Array.isArray(s.modelValue)&&s.modelValue.some(p=>p.value===h.value)?(O(),S("i",NE)):X("",!0)],42,SE))),128)),s.loading?(O(),S("div",IE,t[8]||(t[8]=[f("span",null,"Carregando mais itens...",-1)]))):X("",!0)],64)):(O(),S("div",AE,q(s.noResultsText||"Nenhum item disponível"),1))],40,OE)):X("",!0)],4),s.showFilterTags&&Array.isArray(s.modelValue)&&s.modelValue.length>0?(O(),S("div",ME,[k(c,null,{default:Ne(()=>[(O(!0),S(Ie,null,at(s.modelValue,h=>(O(),Rt(u,{key:h.value,onRemove:m=>a.removeItem(h)},{default:Ne(()=>[Je(q(h.label),1)]),_:2},1032,["onRemove"]))),128))]),_:1})])):X("",!0)])])}const $o=He(yE,[["render",PE],["__scopeId","data-v-a4618aad"]]),UR="",kE={name:"EnrolmentModalNew",components:{Toast:Fo,CustomSelect:mr},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Matricular usuários na turma"},size:{type:String,default:"lg",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Salvar"},cancelButtonText:{type:String,default:"Cancelar"},offerclassid:{type:Number,required:!0},roles:{type:Array,required:!0}},emits:["close","success"],data(){return{enrolmentMethod:"manual",enrolmentMethodOptions:[{value:"manual",label:"Manual"},{value:"batch",label:"Em lote"}],selectedRoleId:"",searchQuery:"",isOpen:!1,userOptions:[],selectedUsers:[],debounceTimer:null,selectedFile:null,csvUsers:[],isDragging:!1,csvDelimiter:",",csvEncoding:"UTF-8",delimiterOptions:[{value:",",label:","},{value:";",label:";"},{value:":",label:":"},{value:"	",label:"\\t"},{value:" ",label:"Espaço"}],encodingOptions:[{value:"UTF-8",label:"UTF-8"},{value:"WINDOWS-1252",label:"WINDOWS-1252"},{value:"ISO-8859-1",label:"ISO-8859-1"},{value:"ASCII",label:"ASCII"},{value:"ISO-8859-2",label:"ISO-8859-2"},{value:"ISO-8859-3",label:"ISO-8859-3"},{value:"ISO-8859-4",label:"ISO-8859-4"},{value:"ISO-8859-5",label:"ISO-8859-5"},{value:"ISO-8859-6",label:"ISO-8859-6"},{value:"ISO-8859-7",label:"ISO-8859-7"},{value:"ISO-8859-8",label:"ISO-8859-8"},{value:"ISO-8859-9",label:"ISO-8859-9"},{value:"ISO-8859-10",label:"ISO-8859-10"},{value:"ISO-8859-13",label:"ISO-8859-13"},{value:"ISO-8859-14",label:"ISO-8859-14"},{value:"ISO-8859-15",label:"ISO-8859-15"},{value:"ISO-8859-16",label:"ISO-8859-16"},{value:"WINDOWS-874",label:"WINDOWS-874"},{value:"WINDOWS-1250",label:"WINDOWS-1250"},{value:"WINDOWS-1251",label:"WINDOWS-1251"},{value:"WINDOWS-1253",label:"WINDOWS-1253"},{value:"WINDOWS-1254",label:"WINDOWS-1254"},{value:"WINDOWS-1255",label:"WINDOWS-1255"},{value:"WINDOWS-1256",label:"WINDOWS-1256"},{value:"WINDOWS-1257",label:"WINDOWS-1257"},{value:"WINDOWS-1258",label:"WINDOWS-1258"},{value:"KOI8-R",label:"KOI8-R"},{value:"MACINTOSH",label:"MACINTOSH"},{value:"IBM866",label:"IBM866"},{value:"BIG5",label:"BIG5"},{value:"EUC-JP",label:"EUC-JP"},{value:"SHIFT_JIS",label:"SHIFT_JIS"},{value:"EUC-KR",label:"EUC-KR"},{value:"UTF-7",label:"UTF-7"},{value:"UTF-16",label:"UTF-16"},{value:"UTF-32",label:"UTF-32"},{value:"UCS-2",label:"UCS-2"},{value:"UCS-4",label:"UCS-4"}],loadingUsers:!1,isSubmitting:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,showResultAlerts:!1,batchMessage:"",batchMessageType:"success",failedMessages:[],reenrolMessages:[]}},computed:{isFormValid(){return this.roles.length===0?!1:this.enrolmentMethod==="manual"?this.selectedUsers.length>0&&this.selectedRoleId:this.enrolmentMethod==="batch"?this.csvUsers.length>0&&this.selectedRoleId:!1}},watch:{show(e){document.body.style.overflow=e?"hidden":"",e&&this.initializeForm()}},mounted(){document.addEventListener("keydown",this.handleKeyDown),document.addEventListener("click",this.handleClickOutside),this.show&&(document.body.style.overflow="hidden",this.initializeForm())},unmounted(){document.removeEventListener("keydown",this.handleKeyDown),document.removeEventListener("click",this.handleClickOutside),document.body.style.overflow=""},methods:{handleKeyDown(e){this.show&&e.key==="Escape"&&this.$emit("close")},handleClickOutside(e){if(this.show===!1)return;const t=document.querySelector(".custom-autocomplete-wrapper");t&&!t.contains(e.target)&&(this.isOpen=!1)},async initializeForm(){this.resetForm()},resetForm(){let e=this.roles.find(t=>t.value==5);this.enrolmentMethod="manual",this.selectedRoleId=e.value,this.searchQuery="",this.selectedUsers=[],this.selectedFile=null,this.csvUsers=[],this.csvDelimiter=",",this.csvEncoding="UTF-8",this.showResultAlerts=!1,this.batchMessage="",this.batchMessageType="success",this.failedMessages=[],this.reenrolMessages=[]},async fetchPotentialUsersToEnrol(e){this.loadingUsers=!0;let t=this.selectedUsers.map(i=>i.value);const s=await eE(this.offerclassid,e,t);this.userOptions=s.data.map(i=>({value:i.id,label:i.fullname})),this.loadingUsers=!1},handleInput(){const e=this.searchQuery.trim();this.debounceTimer&&clearTimeout(this.debounceTimer),e.length>=3?this.debounceTimer=setTimeout(async()=>{await this.fetchPotentialUsersToEnrol(e),this.userOptions&&(this.isOpen=!0)},500):(this.isOpen=!1,this.userOptions=[])},selectUser(e){const t=this.selectedUsers.findIndex(s=>s.value===e.value);t===-1?this.selectedUsers.push(e):this.selectedUsers.splice(t,1),this.searchQuery="",this.isOpen=!1},removeUser(e){this.selectedUsers=this.selectedUsers.filter(t=>t.value!==e.value)},onDragOver(){this.isDragging=!0},onDragLeave(){this.isDragging=!1},onDrop(e){this.isDragging=!1;const t=e.dataTransfer.files;t.length>0&&this.processFile(t[0])},handleFileSelect(e){const t=e.target.files;t.length>0&&this.processFile(t[0])},removeFile(){this.selectedFile=null,this.csvUsers=[],this.$refs.fileInput&&(this.$refs.fileInput.value="")},processFile(e){if(e.type!=="text/csv"&&!e.name.endsWith(".csv")){this.showErrorMessage("Por favor, selecione um arquivo CSV válido.");return}this.selectedFile=e,this.readCSVFile(e)},readCSVFile(e){const t=new FileReader;t.onload=s=>{const i=s.target.result;this.parseCSV(i)},t.onerror=s=>{if(console.error("Erro ao ler o arquivo:",s),this.csvEncoding!=="UTF-8"){console.log("Tentando ler com UTF-8 como fallback...");const i=new FileReader;i.onload=n=>{const a=n.target.result;this.parseCSV(a)},i.onerror=()=>{this.showErrorMessage("Não foi possível ler o arquivo. Verifique se o formato e a codificação estão corretos.")},i.readAsText(e,"UTF-8")}else this.showErrorMessage("Não foi possível ler o arquivo. Verifique se o formato está correto.")};try{t.readAsText(e,this.csvEncoding)}catch(s){console.error("Erro ao tentar ler o arquivo com a codificação selecionada:",s),this.showErrorMessage(`Erro ao ler o arquivo com a codificação ${this.csvEncoding}. Tente selecionar outra codificação.`)}},parseCSV(e){try{const t=this.csvDelimiter,s=/�/.test(e);s&&console.warn("O arquivo contém caracteres inválidos. Pode haver um problema com a codificação selecionada.");const i=e.split(/\r?\n/),n=[];if(i.length<2){console.log("EnrolmentModalNew - Linhas do CSV:",i),this.showErrorMessage("O arquivo CSV deve conter pelo menos uma linha de cabeçalho e uma linha de dados.");return}const a=(m,p)=>{if(p==="\\t")return m.split("	");if(p===" ")return m.split(/\s+/);{const v=p.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");return m.split(new RegExp(v))}},u=a(i[0].toLowerCase(),t);if(u.length<2||!u.some(m=>m.includes("userid"))||!u.some(m=>m.includes("firstname"))){this.showErrorMessage("O arquivo CSV deve conter colunas para UserID e firstname do usuário.");return}const c=u.findIndex(m=>m.includes("userid")),h=u.findIndex(m=>m.includes("firstname"));for(let m=1;m<i.length;m++){const p=i[m].trim();if(!p)continue;const v=a(p,t);if(v.length>Math.max(c,h)){const w=v[c].trim(),D=v[h].trim();if(w&&D){if(!/^\d+$/.test(w)){console.warn(`Linha ${m+1}: ID inválido '${w}'. Deve ser um número.`);continue}n.push({id:w,name:D})}}}if(n.length===0){s?this.showErrorMessage("Nenhum usuário válido encontrado no arquivo CSV. Pode haver um problema com a codificação selecionada. Tente selecionar outra codificação."):this.showErrorMessage("Nenhum usuário válido encontrado no arquivo CSV. Verifique o formato do arquivo.");return}this.csvUsers=n}catch(t){console.error("Erro ao processar arquivo CSV:",t),this.showErrorMessage("Erro ao processar o arquivo CSV. Verifique o formato e a codificação e tente novamente.")}},formatFileSize(e){if(e===0)return"0 Bytes";const t=1024,s=["Bytes","KB","MB","GB","TB"],i=Math.floor(Math.log(e)/Math.log(t));return parseFloat((e/Math.pow(t,i)).toFixed(2))+" "+s[i]},async handleSubmit(){if(this.isFormValid)try{this.isSubmitting=!0;let e=[];this.enrolmentMethod==="manual"?e=this.selectedUsers.map(s=>s.value):this.enrolmentMethod==="batch"&&(e=this.csvUsers.map(s=>parseInt(s.id))),e||this.showErrorMessage("Nenhum usuário selecionado para efetuar a matrícula");const t=await X1({offerclassid:this.offerclassid,userids:e,roleid:parseInt(this.selectedRoleId)});if(t.data){this.showResultAlerts=!0;const s=t.data.filter(u=>u.success),i=s.length,n=i>0?s.filter(u=>u.reenrol):[],a=t.data.filter(u=>u.success==!1);this.batchMessage=i>0?`${i} de ${e.length} usuário(s) matriculado(s) com sucesso.`:"Nenhuma inscrição foi realizada",this.batchMessageType=i>0?"success":"danger",this.reenrolMessages=n.length>0?n.map(u=>u.message):[],this.failedMessages=a.length>0?a.map(u=>u.message):[],i>0&&this.$emit("success",{count:i,total:e.length})}}catch(e){this.showErrorMessage(e.message||"Erro ao matricular usuários. Tente novamente.")}finally{this.isSubmitting=!1}},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},VE={class:"modal-header"},RE={class:"modal-title"},UE={class:"modal-body"},LE={key:0,class:"loading-overlay"},FE={key:1,class:"result-alerts"},BE={key:1,class:"failed-messages"},$E={key:2,class:"reenrol-messages"},jE={key:2,class:"enrolment-modal"},HE={class:"form-row"},qE={class:"form-group"},zE={class:"limited-width-input"},WE={class:"form-group"},GE={class:"limited-width-input"},KE={key:0,class:"error-message"},YE={key:0,class:"form-group"},QE={class:"user-select-container"},ZE={class:"custom-autocomplete-wrapper"},JE={key:0,class:"dropdown-menu show"},XE=["onClick"],eC={key:0,class:"fas fa-check"},tC={key:0,class:"selected-users-container"},sC={class:"filter-tags"},rC=["onClick"],nC={key:1,class:"form-group"},oC={class:"file-name"},iC={class:"file-size"},aC={key:0,class:"csv-users-preview"},lC={class:"preview-header"},uC={class:"selected-users-container"},cC={class:"filter-tags"},dC={key:0,class:"more-users"},fC={class:"csv-info"},hC={class:"csv-example"},pC=["href"],mC={class:"csv-options-row"},gC={class:"csv-option"},vC={class:"csv-option"},_C={key:0,class:"modal-footer"},yC=["disabled"];function bC(e,t,s,i,n,a){const u=te("CustomSelect"),c=te("Toast");return O(),S(Ie,null,[s.show?(O(),S("div",{key:0,class:"modal-backdrop",onClick:t[15]||(t[15]=h=>s.closeOnBackdrop?e.$emit("close"):null)},[f("div",{class:he(["modal-container",[`modal-${s.size}`]]),onClick:t[14]||(t[14]=Ut(()=>{},["stop"]))},[f("div",VE,[f("h3",RE,q(s.title),1),f("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[16]||(t[16]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",UE,[n.isSubmitting?(O(),S("div",LE,t[17]||(t[17]=[f("div",{class:"loading-content"},[f("div",{class:"spinner-border text-primary",role:"status"},[f("span",{class:"sr-only"},"Carregando...")]),f("p",{class:"loading-text mt-3"},"Processando matrículas...")],-1)]))):X("",!0),n.showResultAlerts?(O(),S("div",FE,[n.batchMessage?(O(),S("div",{key:0,class:he(["alert",n.batchMessageType==="success"?"alert-success":"alert-danger"])},[f("i",{class:he(n.batchMessageType==="success"?"fas fa-check-circle":"fas fa-exclamation-triangle")},null,2),Je(" "+q(n.batchMessage),1)],2)):X("",!0),n.failedMessages.length>0?(O(),S("div",BE,[(O(!0),S(Ie,null,at(n.failedMessages,(h,m)=>(O(),S("div",{key:m,class:"alert alert-warning"},[t[18]||(t[18]=f("i",{class:"fas fa-exclamation-triangle"},null,-1)),Je(" "+q(h),1)]))),128))])):X("",!0),n.reenrolMessages.length>0?(O(),S("div",$E,[(O(!0),S(Ie,null,at(n.reenrolMessages,(h,m)=>(O(),S("div",{key:m,class:"alert alert-info"},[t[19]||(t[19]=f("i",{class:"fas fa-exclamation-triangle"},null,-1)),Je(" "+q(h),1)]))),128))])):X("",!0)])):(O(),S("div",jE,[t[34]||(t[34]=f("h3",{class:"section-title"},"OPÇÕES DE MATRÍCULA",-1)),f("div",HE,[f("div",qE,[t[20]||(t[20]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Forma de matrícula"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",zE,[k(u,{modelValue:n.enrolmentMethod,"onUpdate:modelValue":t[1]||(t[1]=h=>n.enrolmentMethod=h),options:n.enrolmentMethodOptions,style:{width:"100%"},required:""},null,8,["modelValue","options"])])]),f("div",WE,[t[21]||(t[21]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Papel para atribuir"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",GE,[k(u,{modelValue:n.selectedRoleId,"onUpdate:modelValue":t[2]||(t[2]=h=>n.selectedRoleId=h),options:s.roles,class:"w-100",required:""},null,8,["modelValue","options"]),s.roles.length===0?(O(),S("div",KE," Não foi possível carregar os papéis disponíveis para esta turma. ")):X("",!0)])])]),n.enrolmentMethod==="manual"?(O(),S("div",YE,[t[24]||(t[24]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Selecionar usuários"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",QE,[f("div",ZE,[ut(f("input",{type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[3]||(t[3]=h=>n.searchQuery=h),onInput:t[4]||(t[4]=(...h)=>a.handleInput&&a.handleInput(...h))},null,544),[[Xt,n.searchQuery]]),t[22]||(t[22]=f("div",{class:"select-arrow"},null,-1)),n.isOpen?(O(),S("div",JE,[(O(!0),S(Ie,null,at(n.userOptions,(h,m)=>(O(),S("div",{key:h.value,class:"dropdown-item",onClick:p=>a.selectUser(h)},[Je(q(h.label)+" ",1),n.selectedUsers.some(p=>p.value===h.value)?(O(),S("i",eC)):X("",!0)],8,XE))),128))])):X("",!0)])]),n.selectedUsers.length>0?(O(),S("div",tC,[f("div",sC,[(O(!0),S(Ie,null,at(n.selectedUsers,h=>(O(),S("div",{key:h.value,class:"tag badge badge-primary",onClick:m=>a.removeUser(h)},[t[23]||(t[23]=f("i",{class:"fas fa-times"},null,-1)),Je(" "+q(h.label),1)],8,rC))),128))])])):X("",!0)])):X("",!0),n.enrolmentMethod==="batch"?(O(),S("div",nC,[t[33]||(t[33]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Matricular usuários a partir de um arquivo CSV"),f("i",{class:"fas fa-exclamation-circle required-icon",title:"Campo obrigatório"})],-1)),f("div",{class:he(["csv-upload-area",{"drag-over":n.isDragging}]),onDragover:t[6]||(t[6]=Ut((...h)=>a.onDragOver&&a.onDragOver(...h),["prevent"])),onDragleave:t[7]||(t[7]=Ut((...h)=>a.onDragLeave&&a.onDragLeave(...h),["prevent"])),onDrop:t[8]||(t[8]=Ut((...h)=>a.onDrop&&a.onDrop(...h),["prevent"])),onClick:t[9]||(t[9]=h=>e.$refs.fileInput.click())},[f("input",{type:"file",ref:"fileInput",accept:".csv",style:{display:"none"},onChange:t[5]||(t[5]=(...h)=>a.handleFileSelect&&a.handleFileSelect(...h))},null,544),n.selectedFile?(O(),S(Ie,{key:1},[t[27]||(t[27]=f("div",{class:"file-icon"},[f("i",{class:"fas fa-file-alt"})],-1)),f("p",oC,q(n.selectedFile.name),1),f("p",iC," ("+q(a.formatFileSize(n.selectedFile.size))+") ",1),t[28]||(t[28]=f("p",{class:"file-replace-text"}," Clique ou arraste outro arquivo para substituir ",-1))],64)):(O(),S(Ie,{key:0},[t[25]||(t[25]=f("div",{class:"upload-icon"},[f("i",{class:"fas fa-arrow-down"})],-1)),t[26]||(t[26]=f("p",{class:"upload-text"}," Você pode arrastar e soltar arquivos aqui para adicioná-los. ",-1))],64))],34),n.csvUsers.length>0?(O(),S("div",aC,[f("div",lC,[f("span",null,"Usuários encontrados no arquivo ("+q(n.csvUsers.length)+"):",1)]),f("div",uC,[f("div",cC,[(O(!0),S(Ie,null,at(n.csvUsers.slice(0,5),h=>(O(),S("div",{key:h.id,class:"tag badge badge-primary"},q(h.name),1))),128)),n.csvUsers.length>5?(O(),S("span",dC,"+"+q(n.csvUsers.length-5)+" mais",1)):X("",!0)])])])):X("",!0),f("div",fC,[t[32]||(t[32]=f("p",{class:"csv-format-text"},"Formatos aceitos: CSV",-1)),f("div",hC,[t[29]||(t[29]=f("span",{class:"example-label"},"Exemplo CSV",-1)),f("a",{href:`/local/offermanager/export_potential_users.php?offerclassid=${s.offerclassid}`,class:"example-csv"},"example.csv",8,pC)]),f("div",mC,[f("div",gC,[t[30]||(t[30]=f("label",null,"Delimitador do CSV",-1)),k(u,{modelValue:n.csvDelimiter,"onUpdate:modelValue":t[10]||(t[10]=h=>n.csvDelimiter=h),options:n.delimiterOptions,width:160},null,8,["modelValue","options"])]),f("div",vC,[t[31]||(t[31]=f("label",null,"Codificação",-1)),k(u,{modelValue:n.csvEncoding,"onUpdate:modelValue":t[11]||(t[11]=h=>n.csvEncoding=h),options:n.encodingOptions,width:160},null,8,["modelValue","options"])])])])])):X("",!0),t[35]||(t[35]=f("div",{class:"form-info"},[f("span",{style:{color:"#f8f9fa","font-size":"15px"}},"Este formulário contém campos obrigatórios marcados com"),f("i",{class:"fas fa-exclamation-circle",style:{color:"#dc3545","font-size":"0.85rem","vertical-align":"middle"}})],-1))]))]),n.showResultAlerts?X("",!0):(O(),S("div",_C,[f("button",{class:"btn btn-primary",onClick:t[12]||(t[12]=(...h)=>a.handleSubmit&&a.handleSubmit(...h)),disabled:n.isSubmitting||!a.isFormValid},q(s.confirmButtonText),9,yC),f("button",{class:"btn btn-secondary",onClick:t[13]||(t[13]=h=>e.$emit("close"))},q(s.cancelButtonText),1)]))],2)])):X("",!0),k(c,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])],64)}const wC=He(kE,[["render",bC],["__scopeId","data-v-a334bdb3"]]),LR="",EC={name:"EnrollmentDetailsModal",props:{show:{type:Boolean,default:!1},user:{type:Object,default:null},courseName:{type:String,default:""}},emits:["close"],methods:{getEnrolmentMethod(e){if(console.log("EnrollmentDetailsModal - Método de inscrição recebido:",e),!e)return"Não disponível";switch(e){case"offer_manual":return"Inscrição manual";case"offer_self":return"Autoinscrição";default:return e}}}},CC={class:"modal-header"},DC={key:0,class:"modal-body"},xC={class:"details-container"},OC={class:"detail-row"},SC={class:"detail-value"},TC={class:"detail-row"},NC={class:"detail-value"},IC={class:"detail-row"},AC={class:"detail-value"},MC={class:"detail-row"},PC={class:"detail-value"},kC={class:"detail-row"},VC={class:"detail-value"},RC={key:1,class:"modal-body no-data"},UC={class:"modal-footer"};function LC(e,t,s,i,n,a){return s.show?(O(),S("div",{key:0,class:"modal-backdrop",onClick:t[3]||(t[3]=u=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[2]||(t[2]=Ut(()=>{},["stop"]))},[f("div",CC,[t[5]||(t[5]=f("h3",{class:"modal-title"},"Informações da matrícula",-1)),f("button",{class:"modal-close",onClick:t[0]||(t[0]=u=>e.$emit("close"))},t[4]||(t[4]=[f("i",{class:"fas fa-times"},null,-1)]))]),s.user?(O(),S("div",DC,[f("div",xC,[f("div",OC,[t[6]||(t[6]=f("div",{class:"detail-label"},"Nome completo",-1)),f("div",SC,q(s.user.fullName),1)]),f("div",TC,[t[7]||(t[7]=f("div",{class:"detail-label"},"Curso",-1)),f("div",NC,q(s.courseName),1)]),f("div",IC,[t[8]||(t[8]=f("div",{class:"detail-label"},"Método de inscrição",-1)),f("div",AC,q(a.getEnrolmentMethod(s.user.enrol)),1)]),f("div",MC,[t[9]||(t[9]=f("div",{class:"detail-label"},"Estado",-1)),f("div",PC,[f("span",{class:he(["status-tag",s.user.status===0?"status-ativo":"status-inativo"])},q(s.user.statusName),3)])]),f("div",kC,[t[10]||(t[10]=f("div",{class:"detail-label"},"Matrícula criada",-1)),f("div",VC,q(s.user.createdDate),1)])])])):(O(),S("div",RC,"Nenhum dado disponível")),f("div",UC,[f("button",{class:"btn btn-secondary",onClick:t[1]||(t[1]=u=>e.$emit("close"))}," Cancelar ")])])])):X("",!0)}const FC=He(EC,[["render",LC],["__scopeId","data-v-ffabdbe2"]]),FR="",BC={name:"EditEnrollmentModal",components:{CustomSelect:mr},props:{show:{type:Boolean,default:!1},user:{type:Object,default:null},offerclassid:{type:[Number,String],required:!0}},emits:["close","success","error"],data(){return{isSubmitting:!1,formData:{status:"1",enableStartDate:!0,startDateStr:"",startTimeStr:"00:00",enableEndDate:!1,endDateStr:"",endTimeStr:"00:00",validityPeriod:"unlimited"},statusOptions:[{value:0,label:"Ativo"},{value:1,label:"Suspenso"}],validityPeriodOptions:[{value:"unlimited",label:"Ilimitado"},...Array.from({length:365},(e,t)=>{const s=t+1;return{value:s.toString(),label:s===1?"1 dia":`${s} dias`}})]}},watch:{show(e){e&&this.user&&this.initializeForm()},user(e){e&&this.show&&this.initializeForm()}},methods:{getEnrolmentMethod(e){if(!e)return"Não disponível";switch(e){case"offer_manual":return"Inscrição manual";case"offer_self":return"Autoinscrição";default:return e}},initializeForm(){if(!this.user)return;this.formData.status=this.user.status;const e=this.user.timestart,t=e?new Date(e*1e3):new Date;this.formData.startDateStr=this.formatDateForInput(t),this.formData.startTimeStr=this.formatTimeForInput(t),this.formData.enableStartDate=!0;const s=this.validityPeriodOptions.filter(i=>i.value!=="unlimited");if(this.user.timeend){const i=new Date(this.user.timeend*1e3);this.formData.endDateStr=this.formatDateForInput(i),this.formData.endTimeStr=this.formatTimeForInput(i),this.formData.enableEndDate=this.user.timeend>0;const a=i-t,u=Math.ceil(a/(1e3*60*60*24)),c=s.find(h=>parseInt(h.value)===u);this.formData.validityPeriod=c?c.value:"unlimited"}else{const i=new Date;i.setMonth(i.getMonth()+3),this.formData.endDateStr=this.formatDateForInput(i),this.formData.endTimeStr=this.formatTimeForInput(i),this.formData.validityPeriod="unlimited",this.formData.enableEndDate=!1}},handleValidityPeriodChange(){if(this.formData.validityPeriod!=="unlimited"){this.formData.enableEndDate=!1;const e=this.formData.enableStartDate&&this.formData.startDateStr?new Date(this.formData.startDateStr):new Date,t=parseInt(this.formData.validityPeriod),s=new Date(e);s.setDate(s.getDate()+t),this.formData.endDateStr=this.formatDateForInput(s),this.formData.endTimeStr=this.formData.startTimeStr}},handleEnableEndDateChange(){this.formData.enableEndDate&&(this.formData.validityPeriod="unlimited")},formatDateForInput(e){const t=e.getFullYear(),s=String(e.getMonth()+1).padStart(2,"0"),i=String(e.getDate()).padStart(2,"0");return`${t}-${s}-${i}`},formatTimeForInput(e){return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},async saveChanges(){var e;if((e=this.user)!=null&&e.offeruserenrolid)try{this.isSubmitting=!0;const t=Number(this.formData.status)||0,s=this.getStartTimestamp(),i=this.getEndTimestamp(s);if(s>i&&i!==0){this.$emit("error","A data de início da matrícula deve ser menor que a data de fim da matrícula.");return}await tE({offeruserenrolid:this.user.offeruserenrolid,status:t,timestart:s,timeend:i})?(this.$emit("success",{userId:this.user.id,offeruserenrolid:this.user.offeruserenrolid,status:t,timestart:s,timeend:i}),this.$emit("close")):this.$emit("error","Não foi possível editar a matrícula. Por favor, tente novamente.")}catch{this.$emit("error","Ocorreu um erro ao editar a matrícula. Por favor, tente novamente.")}finally{this.isSubmitting=!1}},getStartTimestamp(){if(this.formData.enableStartDate&&this.formData.startDateStr){const e=this.parseDateTime(this.formData.startDateStr,this.formData.startTimeStr);return Math.floor(e.getTime()/1e3)}return 0},getEndTimestamp(e){if(this.formData.enableEndDate&&this.formData.endDateStr){const t=this.parseDateTime(this.formData.endDateStr,this.formData.endTimeStr);return Math.floor(t.getTime()/1e3)}if(this.formData.validityPeriod!=="unlimited"){const t=parseInt(this.formData.validityPeriod);if(this.formData.enableStartDate&&this.formData.startDateStr){const s=this.parseDateTime(this.formData.startDateStr,this.formData.startTimeStr),i=new Date(s);return i.setDate(i.getDate()+t),Math.floor(i.getTime()/1e3)}}return 0},parseDateTime(e,t){const[s,i,n]=e.split("-").map(Number),[a,u]=t.split(":").map(Number);return new Date(s,i-1,n,a,u,0,0)}}},$C={class:"modal-header"},jC={class:"modal-title"},HC={class:"modal-body"},qC={class:"enrollment-form"},zC={class:"form-row"},WC={class:"form-value"},GC={class:"form-row"},KC={class:"form-field"},YC={class:"select-wrapper"},QC={class:"form-row"},ZC={class:"form-field date-time-field"},JC={class:"date-field"},XC={class:"time-field"},eD={class:"enable-checkbox"},tD={class:"form-row"},sD={class:"form-field"},rD={class:"select-wrapper"},nD={class:"form-row"},oD={class:"date-field"},iD=["disabled"],aD={class:"time-field"},lD=["disabled"],uD={class:"enable-checkbox"},cD={class:"form-row"},dD={class:"form-value"},fD={class:"modal-footer"},hD={class:"footer-buttons"},pD=["disabled"];function mD(e,t,s,i,n,a){const u=te("CustomSelect");return s.show?(O(),S("div",{key:0,class:"modal-backdrop",onClick:t[15]||(t[15]=c=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[14]||(t[14]=Ut(()=>{},["stop"]))},[f("div",$C,[f("h3",jC," Editar matrícula de "+q(s.user?s.user.fullName:""),1),f("button",{class:"modal-close",onClick:t[0]||(t[0]=c=>e.$emit("close"))},t[16]||(t[16]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",HC,[f("div",qC,[f("div",zC,[t[17]||(t[17]=f("div",{class:"form-label"},"Método de inscrição",-1)),f("div",WC,q(a.getEnrolmentMethod(s.user&&s.user.enrol?s.user.enrol:"")),1)]),f("div",GC,[t[18]||(t[18]=f("div",{class:"form-label"},"Estado",-1)),f("div",KC,[f("div",YC,[k(u,{modelValue:n.formData.status,"onUpdate:modelValue":t[1]||(t[1]=c=>n.formData.status=c),options:n.statusOptions,width:120,class:"smaller-select"},null,8,["modelValue","options"])])])]),f("div",QC,[t[20]||(t[20]=f("div",{class:"form-label"},"Matrícula começa",-1)),f("div",ZC,[f("div",JC,[ut(f("input",{type:"date","onUpdate:modelValue":t[2]||(t[2]=c=>n.formData.startDateStr=c),class:"form-control",onChange:t[3]||(t[3]=(...c)=>e.handleStartDateChange&&e.handleStartDateChange(...c))},null,544),[[Xt,n.formData.startDateStr]])]),f("div",XC,[ut(f("input",{type:"time","onUpdate:modelValue":t[4]||(t[4]=c=>n.formData.startTimeStr=c),class:"form-control",onChange:t[5]||(t[5]=(...c)=>e.handleStartTimeChange&&e.handleStartTimeChange(...c))},null,544),[[Xt,n.formData.startTimeStr]])]),f("div",eD,[ut(f("input",{type:"checkbox",id:"enable-start-date","onUpdate:modelValue":t[6]||(t[6]=c=>n.formData.enableStartDate=c),class:"custom-checkbox"},null,512),[[$i,n.formData.enableStartDate]]),t[19]||(t[19]=f("label",{for:"enable-start-date"},"Habilitar",-1))])])]),f("div",tD,[t[21]||(t[21]=f("div",{class:"form-label"},"Período de validade da matrícula",-1)),f("div",sD,[f("div",rD,[k(u,{modelValue:n.formData.validityPeriod,"onUpdate:modelValue":t[7]||(t[7]=c=>n.formData.validityPeriod=c),options:n.validityPeriodOptions,width:120,class:"smaller-select",onChange:a.handleValidityPeriodChange,disabled:n.formData.enableEndDate},null,8,["modelValue","options","onChange","disabled"])])])]),f("div",nD,[t[23]||(t[23]=f("div",{class:"form-label"},"Matrícula termina",-1)),f("div",{class:he(["form-field date-time-field",{"disabled-inputs-only":!n.formData.enableEndDate}])},[f("div",oD,[ut(f("input",{type:"date","onUpdate:modelValue":t[8]||(t[8]=c=>n.formData.endDateStr=c),class:"form-control",disabled:!n.formData.enableEndDate},null,8,iD),[[Xt,n.formData.endDateStr]])]),f("div",aD,[ut(f("input",{type:"time","onUpdate:modelValue":t[9]||(t[9]=c=>n.formData.endTimeStr=c),class:"form-control",disabled:!n.formData.enableEndDate},null,8,lD),[[Xt,n.formData.endTimeStr]])]),f("div",uD,[ut(f("input",{type:"checkbox",id:"enable-enddate","onUpdate:modelValue":t[10]||(t[10]=c=>n.formData.enableEndDate=c),class:"custom-checkbox",onChange:t[11]||(t[11]=(...c)=>a.handleEnableEndDateChange&&a.handleEnableEndDateChange(...c))},null,544),[[$i,n.formData.enableEndDate]]),t[22]||(t[22]=f("label",{for:"enable-enddate"},"Habilitar",-1))])],2)]),f("div",cD,[t[24]||(t[24]=f("div",{class:"form-label"},"Matrícula criada",-1)),f("div",dD,q(s.user&&s.user.createdDate?s.user.createdDate:"Não disponível"),1)])])]),f("div",fD,[t[25]||(t[25]=f("div",{class:"footer-spacer"},null,-1)),f("div",hD,[f("button",{class:"btn btn-primary",onClick:t[12]||(t[12]=(...c)=>a.saveChanges&&a.saveChanges(...c)),disabled:n.isSubmitting},q(n.isSubmitting?"Salvando...":"Salvar mudanças"),9,pD),f("button",{class:"btn btn-secondary",onClick:t[13]||(t[13]=c=>e.$emit("close"))}," Cancelar ")])])])])):X("",!0)}const gD=He(BC,[["render",mD],["__scopeId","data-v-f9509e2b"]]),BR="",$R="",vD={name:"BulkEditEnrollmentModal",components:{Pagination:pn,CustomTable:hn,CustomSelect:mr},props:{show:{type:Boolean,default:!1},users:{type:Array,default:()=>[]},offerclassid:{type:[Number,String],required:!0}},emits:["close","success","error"],data(){return{isSubmitting:!1,formData:{status:"1",startDateStr:"",startTimeStr:"00:00",enableStartDate:!1,endDateStr:"",endTimeStr:"23:59",enableEndDate:!1},statusOptions:[{value:1,label:"Ativo"},{value:0,label:"Suspenso"}],currentPage:1,perPage:5,sortBy:"fullName",sortDesc:!1,tableHeaders:[{text:"NOME/SOBRENOME",value:"fullName",sortable:!1},{text:"ESTADO ",value:"statusName",sortable:!1},{text:"INÍCIO DA MATRÍCULA",value:"startDate",sortable:!1},{text:"FIM DA MATRÍCULA",value:"endDate",sortable:!1}]}},computed:{filteredUsers(){const e=[...this.users].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)}},watch:{show(e){e&&this.initializeForm()}},methods:{initializeForm(){const e=new Date;this.formData={status:"1",startDateStr:this.formatDateForInput(e),startTimeStr:"00:00",enableStartDate:!1,endDateStr:this.formatDateForInput(e),endTimeStr:"23:59",enableEndDate:!1}},formatDateForInput(e){return e.toISOString().split("T")[0]},formatTimeForInput(e){return`${String(e.getHours()).padStart(2,"0")}:${String(e.getMinutes()).padStart(2,"0")}`},handleStartDateChange(){},handleStartTimeChange(){},handleEndDateChange(){},handleEndTimeChange(){},async saveChanges(){if(!this.users||this.users.length===0){console.error("Nenhum usuário selecionado"),this.$emit("error","Nenhum usuário selecionado para edição em lote.");return}try{this.isSubmitting=!0;const e=parseInt(this.formData.status);let t=0;if(this.formData.enableStartDate&&this.formData.startDateStr){const[a,u,c]=this.formData.startDateStr.split("-").map(Number),[h,m]=this.formData.startTimeStr.split(":").map(Number),p=new Date(a,u-1,c,h,m,0,0);t=Math.floor(p.getTime()/1e3);const v=p.getTimezoneOffset()*60;t+=v}let s=0;if(this.formData.enableEndDate&&this.formData.endDateStr){const[a,u,c]=this.formData.endDateStr.split("-").map(Number),[h,m]=this.formData.endTimeStr.split(":").map(Number),p=new Date(a,u-1,c,h,m,0,0);s=Math.floor(p.getTime()/1e3);const v=p.getTimezoneOffset()*60;s+=v}const i=this.users.filter(a=>a.offeruserenrolid).map(a=>a.offeruserenrolid);if(i.length===0){console.error("Nenhum ID de matrícula encontrado"),this.$emit("error","Não foi possível encontrar os IDs das matrículas dos usuários selecionados.");return}const n=await sE({offeruserenrolids:i,status:e,timestart:t,timeend:s});if(Array.isArray(n)&&n.length>0){const a=n.filter(h=>h.operation_status).length,u=n.length-a;let c="";if(a===n.length)c=`${a} matrícula(s) editada(s) com sucesso.`;else if(a>0)c=`${a} de ${n.length} matrícula(s) editada(s) com sucesso. ${u} matrícula(s) não puderam ser editadas.`;else{c="Nenhuma matrícula pôde ser editada.",this.$emit("error",c);return}this.$emit("success",{message:c,count:a,total:n.length}),this.$emit("close")}else console.error("Resposta inválida da API:",n),this.$emit("error","Não foi possível editar as matrículas. Por favor, tente novamente.")}catch(e){console.error("Erro ao salvar alterações:",e),this.$emit("error","Ocorreu um erro ao editar as matrículas. Por favor, tente novamente.")}finally{this.isSubmitting=!1}}}},_D={class:"modal-header"},yD={class:"modal-body"},bD={class:"enrollment-form"},wD={class:"table-container"},ED={class:"form-row"},CD={class:"form-field"},DD={class:"select-wrapper"},xD={class:"form-row"},OD={class:"form-field date-time-field"},SD={class:"date-field"},TD=["disabled"],ND={class:"time-field"},ID=["disabled"],AD={class:"enable-checkbox"},MD={class:"form-row"},PD={class:"form-field date-time-field"},kD={class:"date-field"},VD=["disabled"],RD={class:"time-field"},UD=["disabled"],LD={class:"enable-checkbox"},FD={class:"modal-footer"},BD={class:"footer-buttons"},$D=["disabled"];function jD(e,t,s,i,n,a){const u=te("CustomTable"),c=te("Pagination"),h=te("CustomSelect");return s.show?(O(),S("div",{key:0,class:"modal-backdrop",onClick:t[17]||(t[17]=m=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[16]||(t[16]=Ut(()=>{},["stop"]))},[f("div",_D,[t[19]||(t[19]=f("h3",{class:"modal-title"},"Edição de Matrículas em Lote",-1)),f("button",{class:"modal-close",onClick:t[0]||(t[0]=m=>e.$emit("close"))},t[18]||(t[18]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",yD,[f("div",bD,[f("div",null,[f("div",wD,[k(u,{headers:n.tableHeaders,items:a.filteredUsers},null,8,["headers","items"])]),s.users.length>0?ut((O(),Rt(c,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":t[1]||(t[1]=m=>n.currentPage=m),"per-page":n.perPage,"onUpdate:perPage":t[2]||(t[2]=m=>n.perPage=m),total:s.users.length},null,8,["current-page","per-page","total"])),[[zl,s.users.length>n.perPage]]):X("",!0),t[20]||(t[20]=f("span",{class:"d-block w-100 border-bottom mt-4"},null,-1))]),f("div",ED,[t[21]||(t[21]=f("div",{class:"form-label"},"Alterar o status",-1)),f("div",CD,[f("div",DD,[k(h,{modelValue:n.formData.status,"onUpdate:modelValue":t[3]||(t[3]=m=>n.formData.status=m),options:n.statusOptions,width:235,class:"smaller-select"},null,8,["modelValue","options"])])])]),f("div",xD,[t[23]||(t[23]=f("div",{class:"form-label"},"Alterar data de início",-1)),f("div",OD,[f("div",SD,[ut(f("input",{type:"date","onUpdate:modelValue":t[4]||(t[4]=m=>n.formData.startDateStr=m),class:"form-control",onChange:t[5]||(t[5]=(...m)=>a.handleStartDateChange&&a.handleStartDateChange(...m)),disabled:!n.formData.enableStartDate},null,40,TD),[[Xt,n.formData.startDateStr]])]),f("div",ND,[ut(f("input",{type:"time","onUpdate:modelValue":t[6]||(t[6]=m=>n.formData.startTimeStr=m),class:"form-control",onChange:t[7]||(t[7]=(...m)=>a.handleStartTimeChange&&a.handleStartTimeChange(...m)),disabled:!n.formData.enableStartDate},null,40,ID),[[Xt,n.formData.startTimeStr]])]),f("div",AD,[ut(f("input",{type:"checkbox",id:"enable-start-date","onUpdate:modelValue":t[8]||(t[8]=m=>n.formData.enableStartDate=m),class:"custom-checkbox"},null,512),[[$i,n.formData.enableStartDate]]),t[22]||(t[22]=f("label",{for:"enable-start-date"},"Habilitar",-1))])])]),f("div",MD,[t[25]||(t[25]=f("div",{class:"form-label"},"Alterar data de fim",-1)),f("div",PD,[f("div",kD,[ut(f("input",{type:"date","onUpdate:modelValue":t[9]||(t[9]=m=>n.formData.endDateStr=m),class:"form-control",onChange:t[10]||(t[10]=(...m)=>a.handleEndDateChange&&a.handleEndDateChange(...m)),disabled:!n.formData.enableEndDate},null,40,VD),[[Xt,n.formData.endDateStr]])]),f("div",RD,[ut(f("input",{type:"time","onUpdate:modelValue":t[11]||(t[11]=m=>n.formData.endTimeStr=m),class:"form-control",onChange:t[12]||(t[12]=(...m)=>a.handleEndTimeChange&&a.handleEndTimeChange(...m)),disabled:!n.formData.enableEndDate},null,40,UD),[[Xt,n.formData.endTimeStr]])]),f("div",LD,[ut(f("input",{type:"checkbox",id:"enable-end-date","onUpdate:modelValue":t[13]||(t[13]=m=>n.formData.enableEndDate=m),class:"custom-checkbox"},null,512),[[$i,n.formData.enableEndDate]]),t[24]||(t[24]=f("label",{for:"enable-end-date"},"Habilitar",-1))])])])])]),f("div",FD,[t[26]||(t[26]=f("div",{class:"footer-spacer"},null,-1)),f("div",BD,[f("button",{class:"btn btn-primary",onClick:t[14]||(t[14]=(...m)=>a.saveChanges&&a.saveChanges(...m)),disabled:n.isSubmitting},q(n.isSubmitting?"Salvando...":"Salvar mudanças"),9,$D),f("button",{class:"btn btn-secondary",onClick:t[15]||(t[15]=m=>e.$emit("close"))}," Cancelar ")])])])])):X("",!0)}const HD=He(vD,[["render",jD],["__scopeId","data-v-1ade848f"]]),jR="",qD={name:"BulkDeleteEnrollmentModal",components:{Pagination:pn,CustomSelect:mr,CustomTable:hn},props:{show:{type:Boolean,default:!1},users:{type:Array,default:()=>[]},offerclassid:{type:[Number,String],required:!0}},emits:["close","confirm","error"],data(){return{isSubmitting:!1,currentPage:1,perPage:5,sortBy:"fullName",sortDesc:!1,tableHeaders:[{text:"NOME/SOBRENOME",value:"fullName",sortable:!1},{text:"ESTADO ",value:"statusName",sortable:!1},{text:"INÍCIO DA MATRÍCULA",value:"startDate",sortable:!1},{text:"FIM DA MATRÍCULA",value:"endDate",sortable:!1}]}},computed:{filteredUsers(){const e=[...this.users].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)}}},zD={class:"modal-header"},WD={class:"modal-body"},GD={class:"enrollment-form"},KD={class:"table-container"},YD={class:"modal-footer"},QD={class:"footer-buttons"},ZD=["disabled"];function JD(e,t,s,i,n,a){const u=te("CustomTable"),c=te("Pagination");return s.show?(O(),S("div",{key:0,class:"modal-backdrop",onClick:t[6]||(t[6]=h=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[5]||(t[5]=Ut(()=>{},["stop"]))},[f("div",zD,[t[8]||(t[8]=f("h3",{class:"modal-title"},"Remoção de Matrículas",-1)),f("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[7]||(t[7]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",WD,[f("div",GD,[f("div",KD,[k(u,{headers:n.tableHeaders,items:a.filteredUsers},null,8,["headers","items"])]),s.users.length>0?ut((O(),Rt(c,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":t[1]||(t[1]=h=>n.currentPage=h),"per-page":n.perPage,"onUpdate:perPage":t[2]||(t[2]=h=>n.perPage=h),total:s.users.length},null,8,["current-page","per-page","total"])),[[zl,s.users.length>n.perPage]]):X("",!0)]),t[9]||(t[9]=f("div",{class:"text-center mt-5"},[f("h5",{class:"mt-1"}," Tem certeza de que deseja excluir essas inscrições de usuário? ")],-1))]),f("div",YD,[f("div",QD,[f("button",{class:"btn btn-primary",onClick:t[3]||(t[3]=h=>e.$emit("confirm")),disabled:n.isSubmitting},q(n.isSubmitting?"Removendo...":"Remover matrículas"),9,ZD),f("button",{class:"btn btn-secondary",onClick:t[4]||(t[4]=h=>e.$emit("close"))}," Cancelar ")])])])])):X("",!0)}const XD=He(qD,[["render",JD],["__scopeId","data-v-cd4191df"]]),HR="",ex={name:"BackButton",props:{label:{type:String,default:"Voltar"},route:{type:String,default:"/local/offermanager/"}},methods:{goBack(){this.$emit("click")}}};function tx(e,t,s,i,n,a){return O(),S("button",{class:"btn-back",onClick:t[0]||(t[0]=(...u)=>a.goBack&&a.goBack(...u))},[t[1]||(t[1]=f("i",{class:"fas fa-angle-left"},null,-1)),Je(" "+q(s.label),1)])}const _u=He(ex,[["render",tx],["__scopeId","data-v-eb293b5c"]]),qR="",sx={name:"UserAvatar",props:{imageUrl:{type:String,default:""},fullName:{type:String,required:!0},size:{type:Number,default:32}},computed:{hasImage(){return!!this.imageUrl},initials(){if(!this.fullName)return"";const e=this.fullName.split(" ").filter(i=>i.length>0);if(e.length===0)return"";if(e.length===1)return e[0].substring(0,2).toUpperCase();const t=e[0].charAt(0),s=e[e.length-1].charAt(0);return(t+s).toUpperCase()},backgroundColor(){const e=["#1976D2","#388E3C","#D32F2F","#7B1FA2","#FFA000","#0097A7","#E64A19","#5D4037","#455A64","#616161"];let t=0;for(let i=0;i<this.fullName.length;i++)t=this.fullName.charCodeAt(i)+((t<<5)-t);const s=Math.abs(t)%e.length;return e[s]},avatarStyle(){return{width:`${this.size}px`,height:`${this.size}px`,minWidth:`${this.size}px`,minHeight:`${this.size}px`}}}},rx=["src"];function nx(e,t,s,i,n,a){return O(),S("div",{class:"user-avatar",style:us(a.avatarStyle)},[a.hasImage?(O(),S("img",{key:0,src:s.imageUrl,alt:"Foto de perfil",class:"avatar-image"},null,8,rx)):(O(),S("div",{key:1,class:"avatar-initials",style:us({backgroundColor:a.backgroundColor})},q(a.initials),5))],4)}const ox=He(sx,[["render",nx],["__scopeId","data-v-0a49f249"]]),zR="",ix={name:"RoleSelector",props:{userId:{type:[Number,String],required:!0},offeruserenrolid:{type:[Number,String],required:!0},currentRole:{type:[String,Array],required:!0},offerclassid:{type:[Number,String],required:!0}},data(){return{isEditing:!1,selectedRoles:[],roles:[],loading:!1,initialLoading:!0}},computed:{displayRoleNames(){return Array.isArray(this.currentRole)?this.currentRole.join(", "):String(this.currentRole||"")}},mounted(){this.loadRoles()},methods:{async loadRoles(){var e;this.initialLoading||(this.loading=!0);try{const t=await hu(parseInt(this.offerclassid)),s=((e=t==null?void 0:t.data)==null?void 0:e.offercourseid)||t.offercourseid;if(!s)throw new Error("offercourseid não encontrado");const i=await pu(s);this.roles=Array.isArray(i==null?void 0:i.data)?i.data:Array.isArray(i)?i:[];const n=await nE(this.offeruserenrolid);if(Array.isArray(n)&&n.length)this.selectedRoles=n.map(a=>a.id);else if(Array.isArray(this.currentRole))this.selectedRoles=this.roles.filter(a=>this.currentRole.includes(a.name)).map(a=>a.id);else if(this.currentRole){const a=this.roles.find(u=>u.name.toLowerCase()===String(this.currentRole).toLowerCase());a&&(this.selectedRoles=[a.id])}}catch{this.$emit("error","Não foi possível carregar papéis.")}finally{this.loading=!1,this.initialLoading=!1}},startEditing(){this.isEditing=!0,this.$nextTick(()=>{var e;return(e=this.$refs.roleSelect)==null?void 0:e.focus()})},cancelEdit(){this.isEditing=!1},close(){this.isEditing&&(this.isEditing=!1)},async saveRoles(){if(!this.selectedRoles.length){this.$emit("error","Selecione ao menos um papel.");return}this.loading=!0;try{const e=await oE(this.offeruserenrolid,this.selectedRoles.map(t=>parseInt(t)));if(e===!0||e&&e.error===!1||e&&e.success===!0){const t=this.roles.filter(s=>this.selectedRoles.includes(s.id)).map(s=>s.name);this.$emit("success",{userId:this.userId,offeruserenrolid:this.offeruserenrolid,roleids:this.selectedRoles,roleNames:t}),this.isEditing=!1,this.$emit("reload-table")}else throw new Error("Resposta inesperada do servidor: "+JSON.stringify(e))}catch(e){console.error("Erro ao salvar papéis:",e),this.$emit("error","Não foi possível salvar papéis.")}finally{this.loading=!1}}}},ax={class:"role-selector"},lx={key:1,class:"role-edit-wrapper"},ux={class:"role-edit-container"},cx={class:"select-wrapper"},dx=["value"],fx={class:"role-actions"},hx={key:2,class:"loading-overlay"};function px(e,t,s,i,n,a){return O(),S("div",ax,[n.isEditing?(O(),S("div",lx,[f("div",ux,[f("div",cx,[ut(f("select",{"onUpdate:modelValue":t[1]||(t[1]=u=>n.selectedRoles=u),class:"role-select",ref:"roleSelect",multiple:"",onClick:t[2]||(t[2]=Ut(()=>{},["stop"])),style:us({height:Math.max(4,n.roles.length)*25+"px"})},[(O(!0),S(Ie,null,at(n.roles,u=>(O(),S("option",{key:u.id,value:u.id},q(u.name),9,dx))),128))],4),[[Kl,n.selectedRoles]])]),f("div",fx,[f("button",{class:"btn-save",onClick:t[3]||(t[3]=Ut((...u)=>a.saveRoles&&a.saveRoles(...u),["stop"])),title:"Salvar"},t[6]||(t[6]=[f("i",{class:"fas fa-check"},null,-1)])),f("button",{class:"btn-cancel",onClick:t[4]||(t[4]=Ut((...u)=>a.cancelEdit&&a.cancelEdit(...u),["stop"])),title:"Cancelar"},t[7]||(t[7]=[f("i",{class:"fas fa-times"},null,-1)]))])])])):(O(),S("div",{key:0,class:"role-display",onClick:t[0]||(t[0]=Ut((...u)=>a.startEditing&&a.startEditing(...u),["stop"]))},[f("span",null,q(a.displayRoleNames),1),t[5]||(t[5]=f("i",{class:"fas fa-pencil-alt edit-icon","aria-hidden":"true"},null,-1))])),n.loading&&n.isEditing?(O(),S("div",hx,t[8]||(t[8]=[f("div",{class:"spinner"},null,-1)]))):X("",!0)])}const mx=He(ix,[["render",px],["__scopeId","data-v-21b55063"]]),WR="",gx={name:"RegisteredUsers",components:{CustomTable:hn,CustomSelect:mr,HierarchicalSelect:hE,CustomInput:Lo,CustomCheckbox:Xi,CustomButton:Fn,FilterSection:op,FilterRow:ea,FilterGroup:ta,FilterActions:ip,FilterTag:Bo,FilterTags:ra,Pagination:pn,PageHeader:sa,ConfirmationModal:gu,Autocomplete:$o,EnrolmentModalNew:wC,EnrollmentDetailsModal:FC,Toast:Fo,EditEnrollmentModal:gD,BulkEditEnrollmentModal:HD,BulkDeleteEnrollmentModal:XD,BackButton:_u,UserAvatar:ox,RoleSelector:mx,LFLoading:mu},data(){return{offerid:null,offerclassid:null,offercourseid:null,courseid:null,courseContextId:null,filteredUsers:[],nameOptions:[],cpfOptions:[],emailOptions:[],nameSearchInput:"",cpfSearchInput:"",emailSearchInput:"",showNameDropdown:!1,showCpfDropdown:!1,showEmailDropdown:!1,nameDebounceTimer:null,cpfDebounceTimer:null,emailDebounceTimer:null,tableHeaders:[{text:"",value:"select",sortable:!1,width:"50px"},{text:"NOME/SOBRENOME",value:"fullName",sortable:!0,width:"220px"},{text:"E-MAIL",value:"email",sortable:!0},{text:"CPF",value:"cpf",sortable:!0},{text:"PAPÉIS",value:"roles",sortable:!1},{text:"GRUPOS",value:"groups",sortable:!1},{text:"DATA INÍCIO DA MATRÍCULA",value:"startDate",sortable:!0},{text:"DATA FIM DA MATRÍCULA",value:"endDate",sortable:!0},{text:"PRAZO DE CONCLUSÃO",value:"deadline",sortable:!0},{text:"PROGRESSO",value:"progress",sortable:!1},{text:"SITUAÇÃO DE MATRÍCULA",value:"situation",sortable:!0},{text:"NOTA",value:"grade",sortable:!1},{text:"ESTADO",value:"status",sortable:!0}],enrolments:[],totalEnrolments:0,loading:!1,error:null,currentPage:1,perPage:10,sortBy:"fullName",sortDesc:!1,showBulkDeleteEnrollmentModal:!1,showEnrollmentModal:!1,selectedUser:null,showEnrolmentModal:!1,roleOptions:[],showEditEnrollmentModal:!1,showBulkEditEnrollmentModal:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,classDetails:{},selectedUsers:[],selectedBulkAction:"",selectedPageView:"usuarios_matriculados",pageViewOptions:[{value:"matriculas",label:"Matrículas",children:[{value:"usuarios_matriculados",label:"Usuários matriculados"}]},{value:"grupos",label:"Grupos",children:[{value:"grupos",label:"Grupos"},{value:"agrupamentos",label:"Agrupamentos"},{value:"visao_geral",label:"Visão geral"}]},{value:"permissoes",label:"Permissões",children:[{value:"permissoes",label:"Permissões"},{value:"outros_usuarios",label:"Outros usuários"},{value:"verificar_permissoes",label:"Verificar permissões"}]}]}},setup(){return{router:Zi()}},async created(){var t,s,i,n;if(this.offerclassid=this.offerclassid??this.$route.params.offerclassid,!this.offerclassid)throw new Error("ID da turma não foi definido.");this.offerclassid=parseInt(this.offerclassid);const e=await hu(this.offerclassid);if(e.error)throw new Error("Erro ao requisitar informações da turma");this.classDetails=e.data,this.offerid=parseInt((t=this.classDetails)==null?void 0:t.offerid),this.offercourseid=parseInt((s=this.classDetails)==null?void 0:s.offercourseid),this.corseid=(i=this.classDetails)==null?void 0:i.courseid,this.courseContextId=(n=this.classDetails)==null?void 0:n.course_context_id,await this.loadRoles(),await this.loadRegisteredUsers()},beforeUnmount(){this.nameDebounceTimer&&clearTimeout(this.nameDebounceTimer),this.cpfDebounceTimer&&clearTimeout(this.cpfDebounceTimer),this.emailDebounceTimer&&clearTimeout(this.emailDebounceTimer)},computed:{allSelected(){return this.enrolments.length>0&&this.selectedUsers.length===this.enrolments.length},someSelected(){return this.selectedUsers.length>0&&!this.allSelected},excludedUserIds(){return this.filteredUsers.map(e=>e.id||e.value)}},watch:{perPage(e,t){e!==t&&(this.currentPage=1,this.selectedUsers=[],this.loadRegisteredUsers())},currentPage(e,t){e!==t&&this.loadRegisteredUsers()}},methods:{async loadRegisteredUsers(){this.loading=!0,this.error=null;let e=[];this.filteredUsers.length>0&&(e=this.excludedUserIds);const t={offerclassid:this.offerclassid,userids:e,page:this.currentPage,perpage:this.perPage,orderby:this.mapSortFieldToBackend(this.sortBy||"fullName"),direction:this.sortDesc?"DESC":"ASC"},s=await J1(t);if(s.data){const i=s.data;Array.isArray(i.enrolments)&&(this.enrolments=i.enrolments.map(n=>({id:n.userid,offeruserenrolid:n.offeruserenrolid,fullName:n.fullname,email:n.email,cpf:n.cpf,enrol:n.enrol,roles:this.formatRoles(n.roles),groups:n.groups,timecreated:n.timecreated,createdDate:this.formatDateTime(n.timecreated),timestart:n.timestart,timeend:n.timeend,startDate:this.formatDate(n.timestart),endDate:this.formatDate(n.timeend),deadline:n.enrolperiod,progress:this.formatProgress(n.progress),situation:n.situation,situationName:n.situation_name,grade:n.grade||"-",status:n.status,statusName:n.status!==void 0?n.status===0?"Ativo":"Suspenso":"-",canCancel:n.can_cancel})),this.totalEnrolments=i.total||this.enrolments.length)}else this.enrolments=[],this.totalEnrolments=0;this.loading=!1},formatDate(e){return!e||e===0?"-":new Date(e*1e3).toLocaleDateString("pt-BR")},formatDateTime(e,t={}){return!e||e===0?"-":(Object.keys(t).length===0&&(t={day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"}),new Date(e*1e3).toLocaleString("pt-BR",t))},formatProgress(e){return e==null?"-":Math.round(e)+"%"},formatRoles(e){return!e||e==="-"?"-":typeof e=="string"?e.split(",").join(", "):Array.isArray(e)&&e.length>0&&typeof e[0]=="object"&&e[0].name?e.map(t=>t.name).join(", "):Array.isArray(e)?e.join(", "):"-"},async loadNameOptions(e){if(!e||e.length<3){this.nameOptions=[],this.showNameDropdown=!1;return}try{const t=await vu({offerclassid:this.offerclassid,fieldstring:"name",searchstring:e,excludeduserids:this.excludedUserIds});t.data?(this.nameOptions=t.data.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showNameDropdown=this.nameOptions.length>0):(this.nameOptions=[],this.showNameDropdown=!1)}catch{this.nameOptions=[],this.showNameDropdown=!1}},async loadCpfOptions(e){if(!e||e.length<3){this.cpfOptions=[],this.showCpfDropdown=!1;return}try{const t=await vu({offerclassid:this.offerclassid,fieldstring:"username",searchstring:e,excludeduserids:this.excludedUserIds});t.data?(this.cpfOptions=t.data.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showCpfDropdown=this.cpfOptions.length>0):(this.cpfOptions=[],this.showCpfDropdown=!1)}catch{this.cpfOptions=[],this.showCpfDropdown=!1}},async loadEmailOptions(e){if(!e||e.length<3){this.emailOptions=[],this.showEmailDropdown=!1;return}try{const t=await vu({offerclassid:this.offerclassid,fieldstring:"email",searchstring:e,excludeduserids:this.excludedUserIds});!t.error&&t.data?(this.emailOptions=t.data.map(s=>({id:s.id,value:s.id,label:s.fullname})),this.showEmailDropdown=this.emailOptions.length>0):(this.emailOptions=[],this.showEmailDropdown=!1)}catch{this.emailOptions=[],this.showEmailDropdown=!1}},handleNameInput(){this.nameDebounceTimer&&clearTimeout(this.nameDebounceTimer),this.nameSearchInput.length>=3?this.nameDebounceTimer=setTimeout(()=>{this.loadNameOptions(this.nameSearchInput)},500):this.showNameDropdown=!1},handleCpfInput(){this.cpfDebounceTimer&&clearTimeout(this.cpfDebounceTimer),this.cpfSearchInput.length>=3?this.cpfDebounceTimer=setTimeout(()=>{this.loadCpfOptions(this.cpfSearchInput)},500):this.showCpfDropdown=!1},handleEmailInput(){this.emailDebounceTimer&&clearTimeout(this.emailDebounceTimer),this.emailSearchInput.length>=3?this.emailDebounceTimer=setTimeout(()=>{this.loadEmailOptions(this.emailSearchInput)},500):this.showEmailDropdown=!1},selectNameOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"name"}),this.nameSearchInput="",this.showNameDropdown=!1,this.clearOptions(),this.loadRegisteredUsers()},selectCpfOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"cpf"}),this.cpfSearchInput="",this.showCpfDropdown=!1,this.clearOptions(),this.loadRegisteredUsers()},selectEmailOption(e){this.filteredUsers.push({id:e.id,value:e.value,label:e.label,type:"email"}),this.emailSearchInput="",this.showEmailDropdown=!1,this.clearOptions(),this.loadRegisteredUsers()},clearOptions(e){setTimeout(()=>{switch(e){case"name":this.nameOptions=[];break;case"cpf":this.cpfOptions=[];break;case"email":this.emailOptions=[];break;default:this.nameOptions=[],this.cpfOptions=[],this.emailOptions=[];break}},500)},removeFilter(e){const t=this.filteredUsers.findIndex(s=>s.id===e||s.value===e);t!==-1&&this.filteredUsers.splice(t,1),this.loadRegisteredUsers()},clearFilteredUsers(){this.filteredUsers=[],this.loadRegisteredUsers()},toggleSelectAll(){this.allSelected?this.selectedUsers=[]:this.selectedUsers=this.enrolments.map(e=>e.id)},toggleSelectUser(e){const t=this.selectedUsers.indexOf(e);t===-1?this.selectedUsers.push(e):this.selectedUsers.splice(t,1)},isSelected(e){return this.selectedUsers.includes(e)},async handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,await this.loadRegisteredUsers()},mapSortFieldToBackend(e){return{fullName:"fullname",email:"email",cpf:"cpf",startDate:"startdate",endDate:"enddate",deadline:"enrolperiod",situation:"situation",status:"status"}[e]||"fullname"},addNewUser(){var e;if(this.classDetails&&((e=this.classDetails)==null?void 0:e.operational_cycle)===2){this.error="Não é possível matricular usuários em uma turma com ciclo operacional encerrado.";return}this.showEnrolmentModal=!0},closeEnrolmentModal(){this.showEnrolmentModal=!1},async goBack(){this.router.push({name:"editar-oferta",params:{id:this.offerid}})},viewUserProfile(e){if(!e)return;const t=`/user/view.php?id=${e}&course=${this.courseid}`;window.location.href=t},async handlePageViewChange(e){let t=this.offerclassid,s=this.courseid,i=this.courseContextId;const n={usuarios_matriculados:`/local/offermanager/new-subscribed-users/${t}`,grupos:`/group/index.php?id=${s}`,agrupamentos:`/group/groupings.php?id=${s}`,visao_geral:`/user/index.php?id=${s}`,permissoes:`/admin/roles/permissions.php?contextid=${i}`,outros_usuarios:`/enrol/otherusers.php?id=${s}`,verificar_permissoes:`/admin/roles/check.php?contextid=${i}`};n[e]&&(window.location.href=n[e])},async handleEnrolmentSuccess(){await this.loadRegisteredUsers()},async loadRoles(){const e=await pu(this.offercourseid);if(e.error)throw new Error("Erro ao requisitar papéis do curso");this.roleOptions=e.data.map(t=>({value:t.id,label:t.name}))},showEnrollmentDetails(e){this.selectedUser={fullName:e.fullName,enrol:e.enrol||"Inscrições manuais",status:e.status||0,statusName:e.statusName||"Ativo",startDate:e.startDate||"Não disponível",createdDate:e.createdDate||e.startDate||"Não disponível"},this.showEnrollmentModal=!0},closeEnrollmentModal(){this.showEnrollmentModal=!1,this.selectedUser=null},closeEditEnrollmentModal(){this.showEditEnrollmentModal=!1,this.selectedUser=null},async handleEditEnrollmentSuccess(e){if(this.showSuccessMessage("Matrícula editada com sucesso."),e.roleid){let t=null;if(this.roleOptions&&this.roleOptions.length>0){const i=this.roleOptions.find(n=>n.value===String(e.roleid));i&&(t=i.name)}if(!t){await this.loadRegisteredUsers(),this.showEditEnrollmentModal=!1,this.selectedUser=null;return}const s=this.enrolments.findIndex(i=>i.id===e.userId);if(s!==-1){if(t&&(this.enrolments[s].roles=t),e.status!==void 0&&(this.enrolments[s].status=e.status,e.status===1?this.enrolments[s].statusName="Ativo":e.status===0&&(this.enrolments[s].statusName="Suspenso")),e.timestart){const i=new Date(e.timestart*1e3);this.enrolments[s].startDate=i.toLocaleDateString("pt-BR")}if(e.timeend){const i=new Date(e.timeend*1e3);this.enrolments[s].endDate=i.toLocaleDateString("pt-BR")}}else await this.loadRegisteredUsers()}else await this.loadRegisteredUsers();this.showEditEnrollmentModal=!1,this.selectedUser=null},handleEditEnrollmentError(e){this.showErrorMessage(e||"Não foi possível editar a matrícula. Por favor, tente novamente.")},handleRoleUpdateSuccess(e){this.showSuccessMessage("Papel atualizado com sucesso.");const t=this.enrolments.findIndex(s=>s.id===e.userId);t!==-1?this.enrolments[t].roles=e.roleName:this.reloadTable()},handleRoleUpdateError(e){this.showErrorMessage(e||"Ocorreu um erro ao atualizar o papel do usuário.")},reloadTable(){this.loadRegisteredUsers()},editUser(e){let t=null;e.roles&&e.roleid&&(t=e.roleid),this.selectedUser={id:e.id,offeruserenrolid:e.offeruserenrolid,fullName:e.fullName,enrol:e.enrol,status:e.status,statusName:e.statusName,roles:e.roles,roleid:t,startDate:e.startDate,timestart:e.timestart,timeend:e.timeend,createdDate:e.createdDate||"-"},this.showEditEnrollmentModal=!0},async confirmeBulkDeleteEnrollment(){this.loading=!0;const e=[];for(const i of this.selectedUsers){const n=this.enrolments.find(a=>a.id===i);n&&n.offeruserenrolid&&e.push(n.offeruserenrolid)}if(e.length===0){this.showErrorMessage("Não foi possível encontrar os IDs das matrículas. Por favor, tente novamente."),this.loading=!1;return}const t=`Processando exclusão de ${e.length} matrícula(s)...`;this.showSuccessMessage(t);const s=await rE(e);if(s&&s.length>0){const i=s.filter(a=>a.operation_status).length,n=s.length-i;i>0?(this.showSuccessMessage(`${i} matrícula(s) cancelada(s) com sucesso.${n>0?` ${n} matrícula(s) não puderam ser canceladas.`:""}`),await this.loadRegisteredUsers(),this.selectedUsers=[]):this.showErrorMessage("Não foi possível cancelar as matrículas. Por favor, tente novamente.")}else this.showSuccessMessage(`${e.length} matrícula(s) cancelada(s) com sucesso.`),await this.loadRegisteredUsers(),this.selectedUsers=[];this.showBulkDeleteEnrollmentModal=!1,this.loading=!1},handleBulkAction(){if(this.selectedBulkAction){if(this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para realizar esta ação."),this.selectedBulkAction="";return}switch(this.selectedBulkAction){case"message":this.sendMessage();break;case"note":this.writeNote();break;case"download_csv":this.downloadData("csv");break;case"download_xlsx":this.downloadData("xlsx");break;case"download_html":this.downloadData("html");break;case"download_json":this.downloadData("json");break;case"download_ods":this.downloadData("ods");break;case"download_pdf":this.downloadData("pdf");break;case"edit_enrolment":this.editEnrolments();break;case"delete_enrolment":this.bulkDeleteEnrollment();break}this.selectedBulkAction=""}},sendMessage(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showErrorMessage("Por favor, selecione pelo menos um usuário para enviar mensagem.");return}this.showSendMessageModal(this.selectedUsers)},showSendMessageModal(e){if(typeof window.require!="function"){this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.");return}window.require(["core_message/message_send_bulk"],t=>{if(typeof t.showModal!="function"){this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.");return}t.showModal(e,()=>{this.selectedBulkAction=""})},t=>{this.showErrorMessage("Não foi possível abrir o modal de mensagens. Por favor, tente novamente mais tarde.")})},writeNote(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para escrever anotação.");return}this.showAddNoteModal(this.courseid,this.selectedUsers)},showAddNoteModal(e,t){if(typeof window.require!="function"){this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.");return}window.require(["core_user/local/participants/bulkactions"],s=>{if(typeof s.showAddNote!="function"){this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.");return}const i={personal:"Pessoal",course:"Curso",site:"Site"};s.showAddNote(e,t,i,"").then(n=>(n.getRoot().on("hidden.bs.modal",()=>{this.selectedBulkAction=""}),n)).catch(n=>{this.showErrorMessage("Ocorreu um erro ao abrir o modal de anotações. Por favor, tente novamente mais tarde.")})},s=>{this.showErrorMessage("Não foi possível abrir o modal de anotações. Por favor, tente novamente mais tarde.")})},downloadData(e){this.selectedUsers.length!==0&&this.prepareLocalDownload(e)},prepareLocalDownload(e){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Nenhum usuário selecionado para download.");return}const t=[];if(Array.isArray(this.selectedUsers)&&this.selectedUsers.every(s=>typeof s=="number"))for(const s of this.selectedUsers){const i=this.enrolments.find(n=>n.id===s);if(i){const n={ID:i.id||"",Nome:i.fullName||i.name||"",Email:i.email||"",CPF:i.cpf||"",Papéis:i.roles||"",Grupos:i.groups||"","Data de Início":i.startDate||"","Data de Término":i.endDate||"",Prazo:i.deadline||"",Progresso:i.progress||"",Situação:i.situationName||i.situation||"",Nota:i.grade||"",Estado:i.statusName||""};t.push(n)}}else for(const s of this.selectedUsers)if(typeof s=="number"){const i=this.enrolments.find(n=>n.id===s);if(i){const n={ID:i.id||"",Nome:i.fullName||i.name||"",Email:i.email||"",CPF:i.cpf||"",Papéis:i.roles||"",Grupos:i.groups||"","Data de Início":i.startDate||"","Data de Término":i.endDate||"",Prazo:i.deadline||"",Progresso:i.progress||"",Situação:i.situationName||i.situation||"",Nota:i.grade||"",Estado:i.statusName||""};t.push(n)}}else if(typeof s=="object"&&s!==null){const i={ID:s.id||"",Nome:s.fullName||s.name||"",Email:s.email||"",CPF:s.cpf||"",Papéis:s.roles||"",Grupos:s.groups||"","Data de Início":s.startDate||"","Data de Término":s.endDate||"",Prazo:s.deadline||"",Progresso:s.progress||"",Situação:s.situationName||s.situation||"",Nota:s.grade||"",Estado:s.statusName||""};t.push(i)}if(t.length===0){this.showErrorMessage("Nenhum dado disponível para download.");return}switch(e){case"csv":this.downloadCSV(t);break;case"xlsx":this.downloadXLSX(t);break;case"html":this.downloadHTML(t);break;case"json":this.downloadJSON(t);break;case"ods":this.downloadODS(t);break;case"pdf":this.downloadPDF(t);break;default:this.showErrorMessage("Formato de download não suportado.");break}},downloadCSV(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]),i=s.map(h=>h.replace(/([A-Z])/g," $1").replace(/^./,m=>m.toUpperCase()).trim()),n=t+[i.join(","),...e.map(h=>s.map(m=>{const p=h[m]||"";return`"${String(p).replace(/"/g,'""')}"`}).join(","))].join(`
`),a=new Blob([n],{type:"text/csv;charset=utf-8;"}),u=URL.createObjectURL(a),c=document.createElement("a");c.setAttribute("href",u),c.setAttribute("download","usuarios_matriculados.csv"),c.style.visibility="hidden",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(u)},downloadXLSX(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]),i=t+[s.join(","),...e.map(c=>s.map(h=>{const m=c[h]||"";return`"${String(m).replace(/"/g,'""')}"`}).join(","))].join(`
`),n=new Blob([i],{type:"text/csv;charset=utf-8;"}),a=URL.createObjectURL(n),u=document.createElement("a");u.setAttribute("href",a),u.setAttribute("download","usuarios_matriculados.csv"),u.style.visibility="hidden",document.body.appendChild(u),u.click(),document.body.removeChild(u),URL.revokeObjectURL(a),this.showSuccessMessage("Download concluído. O arquivo CSV pode ser aberto no Excel.")},downloadHTML(e){if(e.length===0)return;const t=Object.keys(e[0]),s=[];for(let A=0;A<t.length;A++){const re=t[A].replace(/([A-Z])/g," $1").replace(/^./,Y=>Y.toUpperCase()).trim();s.push(re)}let i="";for(let A=0;A<s.length;A++)i+="<th>"+s[A]+"</th>";let n="";for(let A=0;A<e.length;A++){let re="<tr>";for(let Y=0;Y<t.length;Y++)re+="<td>"+(e[A][t[Y]]||"")+"</td>";re+="</tr>",n+=re}const a='<!DOCTYPE html><html><head><meta charset="utf-8"><title>Usuários Matriculados</title>',u="<style>body{font-family:Arial,sans-serif;margin:20px;color:#333}h1{color:#2c3e50;text-align:center;margin-bottom:20px}table{border-collapse:collapse;width:100%;margin-bottom:20px;box-shadow:0 0 20px rgba(0,0,0,.1)}th,td{border:1px solid #ddd;padding:12px;text-align:left}th{background-color:#3498db;color:white;font-weight:bold;text-transform:uppercase;font-size:14px}tr:nth-child(even){background-color:#f2f2f2}tr:hover{background-color:#e9f7fe}.footer{text-align:center;margin-top:20px;font-size:12px;color:#7f8c8d}</style>",c="</head><body><h1>Usuários Matriculados</h1>",h="<table><thead><tr>",m="</tr></thead><tbody>",p="</tbody></table>",v='<div class="footer">Gerado em '+new Date().toLocaleString()+"</div>",w="</body></html>",D=a+u+c+h+i+m+n+p+v+w,V=new Blob([D],{type:"text/html;charset=utf-8;"}),F=URL.createObjectURL(V),se=document.createElement("a");se.setAttribute("href",F),se.setAttribute("download","usuarios_matriculados.html"),se.style.visibility="hidden",document.body.appendChild(se),se.click(),document.body.removeChild(se),URL.revokeObjectURL(F),this.showSuccessMessage("Download concluído. O arquivo HTML foi salvo com sucesso.")},downloadJSON(e){if(e.length===0)return;const t=JSON.stringify(e,null,2),s=new Blob([t],{type:"application/json;charset=utf-8;"}),i=URL.createObjectURL(s),n=document.createElement("a");n.setAttribute("href",i),n.setAttribute("download","usuarios_matriculados.json"),n.style.visibility="hidden",document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(i)},downloadODS(e){if(e.length===0)return;const t="\uFEFF",s=Object.keys(e[0]);let i=[];i.push(s.join(",")),e.forEach(h=>{const m=s.map(p=>{const v=h[p]||"";return'"'+String(v).replace(/"/g,'""')+'"'});i.push(m.join(","))});const n=t+i.join(`
`),a=new Blob([n],{type:"text/csv;charset=utf-8;"}),u=URL.createObjectURL(a),c=document.createElement("a");c.setAttribute("href",u),c.setAttribute("download","usuarios_matriculados.csv"),c.style.visibility="hidden",document.body.appendChild(c),c.click(),document.body.removeChild(c),URL.revokeObjectURL(u),this.showSuccessMessage("Download concluído. O arquivo CSV pode ser importado no LibreOffice Calc para salvar como ODS.")},downloadPDF(e){e.length!==0&&(this.downloadHTML(e),this.showSuccessMessage("Página HTML aberta. Use a função de impressão do navegador (Ctrl+P) para salvar como PDF."))},editEnrolments(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showErrorMessage("Por favor, selecione pelo menos um usuário para editar matrícula.");return}if(this.selectedUsers.length===1){const e=this.selectedUsers[0],t=this.enrolments.find(s=>s.id===e);t?this.editUser(t):this.showErrorMessage("Usuário não encontrado. Por favor, tente novamente.")}else this.showBulkEditEnrollmentModal=!0},async handleBulkEditEnrollmentSuccess(e){this.showSuccessMessage(e.message||"Matrículas editadas com sucesso."),await this.loadRegisteredUsers(),this.selectedUsers=[],this.showBulkEditEnrollmentModal=!1},handleBulkEditEnrollmentError(e){const t="Não foi possível editar as matrículas. Por favor, tente novamente.";this.showErrorMessage(e||t)},bulkDeleteEnrollment(){if(!this.selectedUsers||this.selectedUsers.length===0){this.showWarningMessage("Por favor, selecione pelo menos um usuário para excluir matrícula.");return}this.showBulkDeleteEnrollmentModal=!0},handleBulkDeleteEnrollmentError(e){const t="Não foi possível excluir as matrículas. Por favor, tente novamente.";this.showErrorMessage(e||t)},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showWarningMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="warning",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}}},vx={id:"offer-manager-component",class:"offer-manager"},_x={style:{display:"flex","align-items":"center","margin-bottom":"20px",gap:"10px"}},yx={style:{width:"240px"}},bx={class:"filters-section mb-3"},wx={class:"row"},Ex={class:"col-md-3"},Cx={class:"filter-input-container position-relative"},Dx={key:0,class:"dropdown-menu show position-absolute w-100",style:{top:"100%","z-index":"1000","max-height":"200px","overflow-y":"auto"}},xx=["onClick"],Ox={class:"col-md-3"},Sx={class:"filter-input-container position-relative"},Tx={key:0,class:"dropdown-menu show position-absolute w-100",style:{top:"100%","z-index":"1000","max-height":"200px","overflow-y":"auto"}},Nx=["onClick"],Ix={class:"col-md-3"},Ax={class:"filter-input-container position-relative"},Mx={key:0,class:"dropdown-menu show position-absolute w-100",style:{top:"100%","z-index":"1000","max-height":"200px","overflow-y":"auto"}},Px=["onClick"],kx={key:0,class:"my-4"},Vx={key:1,class:"alert alert-danger"},Rx={class:"table-container"},Ux={class:"checkbox-container"},Lx=["checked","indeterminate"],Fx={class:"checkbox-container"},Bx=["checked","onChange"],$x=["href","title"],jx={class:"user-name-link"},Hx={class:"progress-container"},qx={class:"progress-text"},zx={class:"status-container"},Wx={class:"status-actions"},Gx=["onClick"],Kx=["onClick"],Yx=["disabled","onClick"],Qx={class:"selected-users-actions"},Zx={class:"bulk-actions-container"},Jx={key:2,class:"bottom-enroll-button"};function Xx(e,t,s,i,n,a){var _e,Ae,ae;const u=te("BackButton"),c=te("PageHeader"),h=te("HierarchicalSelect"),m=te("CustomButton"),p=te("FilterTag"),v=te("FilterTags"),w=te("UserAvatar"),D=te("RoleSelector"),V=te("CustomTable"),F=te("Pagination"),se=te("EnrollmentDetailsModal"),A=te("EnrolmentModalNew"),re=te("EditEnrollmentModal"),Y=te("BulkEditEnrollmentModal"),ye=te("BulkDeleteEnrollmentModal"),Z=te("LFLoading"),fe=te("Toast");return O(),S("div",vx,[k(c,{title:"Usuários matriculados"},{actions:Ne(()=>[k(u,{onClick:a.goBack},null,8,["onClick"])]),_:1}),f("div",_x,[f("div",yx,[k(h,{modelValue:n.selectedPageView,"onUpdate:modelValue":t[0]||(t[0]=I=>n.selectedPageView=I),options:n.pageViewOptions,onNavigate:a.handlePageViewChange},null,8,["modelValue","options","onNavigate"])]),!n.classDetails||((_e=n.classDetails)==null?void 0:_e.operational_cycle)!==2?(O(),Rt(m,{key:0,variant:"primary",label:"Matricular usuários",onClick:a.addNewUser},null,8,["onClick"])):X("",!0)]),f("div",bx,[f("div",wx,[f("div",Ex,[f("div",Cx,[t[21]||(t[21]=f("label",{for:"name-filter",class:"form-label text-muted small"},"Filtrar por nome",-1)),ut(f("input",{id:"name-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[1]||(t[1]=I=>n.nameSearchInput=I),onInput:t[2]||(t[2]=(...I)=>a.handleNameInput&&a.handleNameInput(...I)),onFocus:t[3]||(t[3]=I=>n.showNameDropdown=n.nameOptions.length>0),onBlur:t[4]||(t[4]=I=>a.clearOptions("name"))},null,544),[[Xt,n.nameSearchInput]]),n.showNameDropdown&&n.nameOptions.length>0?(O(),S("div",Dx,[(O(!0),S(Ie,null,at(n.nameOptions,I=>(O(),S("button",{key:I.id,type:"button",class:"dropdown-item",onClick:be=>a.selectNameOption(I)},q(I.label),9,xx))),128))])):X("",!0)])]),f("div",Ox,[f("div",Sx,[t[22]||(t[22]=f("label",{for:"cpf-filter",class:"form-label text-muted small"},"Filtrar por CPF",-1)),ut(f("input",{id:"cpf-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[5]||(t[5]=I=>n.cpfSearchInput=I),onInput:t[6]||(t[6]=(...I)=>a.handleCpfInput&&a.handleCpfInput(...I)),onFocus:t[7]||(t[7]=I=>n.showCpfDropdown=n.cpfOptions.length>0),onBlur:t[8]||(t[8]=I=>a.clearOptions("cpf"))},null,544),[[Xt,n.cpfSearchInput]]),n.showCpfDropdown&&n.cpfOptions.length>0?(O(),S("div",Tx,[(O(!0),S(Ie,null,at(n.cpfOptions,I=>(O(),S("button",{key:I.id,type:"button",class:"dropdown-item",onClick:be=>a.selectCpfOption(I)},q(I.label),9,Nx))),128))])):X("",!0)])]),f("div",Ix,[f("div",Ax,[t[23]||(t[23]=f("label",{for:"email-filter",class:"form-label text-muted small"},"Filtrar por E-mail",-1)),ut(f("input",{id:"email-filter",type:"text",class:"form-control",placeholder:"Buscar...","onUpdate:modelValue":t[9]||(t[9]=I=>n.emailSearchInput=I),onInput:t[10]||(t[10]=(...I)=>a.handleEmailInput&&a.handleEmailInput(...I)),onFocus:t[11]||(t[11]=I=>n.showEmailDropdown=n.emailOptions.length>0),onBlur:t[12]||(t[12]=I=>a.clearOptions("email"))},null,544),[[Xt,n.emailSearchInput]]),n.showEmailDropdown&&n.emailOptions.length>0?(O(),S("div",Mx,[(O(!0),S(Ie,null,at(n.emailOptions,I=>(O(),S("button",{key:I.id,type:"button",class:"dropdown-item",onClick:be=>a.selectEmailOption(I)},q(I.label),9,Px))),128))])):X("",!0)])])])]),k(v,null,{default:Ne(()=>[(O(!0),S(Ie,null,at(n.filteredUsers,I=>(O(),Rt(p,{key:I.id,onRemove:be=>a.removeFilter(I.id||I.value)},{default:Ne(()=>[Je(q(I.label),1)]),_:2},1032,["onRemove"]))),128))]),_:1}),n.filteredUsers.length>0?(O(),S("div",kx,[f("button",{type:"button",class:"btn btn-secondary",onClick:t[13]||(t[13]=(...I)=>a.clearFilteredUsers&&a.clearFilteredUsers(...I))}," Limpar ")])):X("",!0),n.error?(O(),S("div",Vx,[t[24]||(t[24]=f("i",{class:"fas fa-exclamation-circle"},null,-1)),Je(" "+q(n.error),1)])):X("",!0),f("div",Rx,[k(V,{headers:n.tableHeaders,items:n.enrolments,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"header-select":Ne(()=>[f("div",Ux,[f("input",{type:"checkbox",checked:a.allSelected,indeterminate:a.someSelected&&!a.allSelected,onChange:t[14]||(t[14]=(...I)=>a.toggleSelectAll&&a.toggleSelectAll(...I)),class:"custom-checkbox"},null,40,Lx)])]),"item-select":Ne(({item:I})=>[f("div",Fx,[f("input",{type:"checkbox",checked:a.isSelected(I.id),onChange:be=>a.toggleSelectUser(I.id),class:"custom-checkbox"},null,40,Bx)])]),"item-fullName":Ne(({item:I})=>[f("a",{class:"user-name-container",href:`/user/view.php?id=${I.id}`,title:"Ver perfil de "+I.fullName},[k(w,{"full-name":I.fullName,size:36},null,8,["full-name"]),f("span",jx,q(I.fullName),1)],8,$x)]),"item-email":Ne(({item:I})=>[Je(q(I.email),1)]),"item-cpf":Ne(({item:I})=>[Je(q(I.cpf),1)]),"item-roles":Ne(({item:I})=>[k(D,{userId:I.id,offeruserenrolid:I.offeruserenrolid,currentRole:I.roles,offerclassid:n.offerclassid,onSuccess:a.handleRoleUpdateSuccess,onError:a.handleRoleUpdateError,onReloadTable:a.reloadTable},null,8,["userId","offeruserenrolid","currentRole","offerclassid","onSuccess","onError","onReloadTable"])]),"item-groups":Ne(({item:I})=>[Je(q(I.groups),1)]),"item-startDate":Ne(({item:I})=>[Je(q(I.startDate),1)]),"item-endDate":Ne(({item:I})=>[Je(q(I.endDate),1)]),"item-deadline":Ne(({item:I})=>[Je(q(I.deadline),1)]),"item-progress":Ne(({item:I})=>[f("div",Hx,[f("div",{class:"progress-bar",style:us({width:I.progress})},null,4),f("span",qx,q(I.progress),1)])]),"item-situation":Ne(({item:I})=>[Je(q(I.situationName),1)]),"item-grade":Ne(({item:I})=>[Je(q(I.grade),1)]),"item-status":Ne(({item:I})=>[f("div",zx,[f("span",{class:he(["status-tag badge",I.status===0?"badge-success":"badge-danger"])},q(I.statusName),3),f("div",Wx,[f("button",{class:"btn-information",onClick:be=>a.showEnrollmentDetails(I),title:"Informações da matrícula"},t[25]||(t[25]=[f("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round",class:"custom-icon"},[f("circle",{cx:"12",cy:"12",r:"10"}),f("line",{x1:"12",y1:"16",x2:"12",y2:"12"}),f("line",{x1:"12",y1:"8",x2:"12.01",y2:"8"})],-1)]),8,Gx),f("button",{class:"btn-settings",onClick:be=>a.editUser(I),title:"Editar matrícula"},t[26]||(t[26]=[f("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round",class:"custom-icon"},[f("circle",{cx:"12",cy:"12",r:"3"}),f("path",{d:"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"})],-1)]),8,Kx),f("a",{role:"button",class:"btn-link p-0",disabled:!I.canCancel,onClick:be=>e.cancelUser(I),title:"Cancelar matrícula"},t[27]||(t[27]=[f("i",{class:"fa fa-times-circle-o text-danger"},null,-1)]),8,Yx)])])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"])]),k(F,{"current-page":n.currentPage,"onUpdate:currentPage":t[15]||(t[15]=I=>n.currentPage=I),"per-page":n.perPage,"onUpdate:perPage":t[16]||(t[16]=I=>n.perPage=I),total:n.totalEnrolments,loading:n.loading},null,8,["current-page","per-page","total","loading"]),f("div",Qx,[f("div",Zx,[t[29]||(t[29]=f("label",{for:"bulk-actions"},"Com usuários selecionados...",-1)),ut(f("select",{id:"bulk-actions",class:"form-control bulk-select","onUpdate:modelValue":t[17]||(t[17]=I=>n.selectedBulkAction=I),onChange:t[18]||(t[18]=(...I)=>a.handleBulkAction&&a.handleBulkAction(...I))},t[28]||(t[28]=[ny('<option value="" data-v-965c2e5e>Escolher...</option><optgroup label="Comunicação" data-v-965c2e5e><option value="message" data-v-965c2e5e>Enviar uma mensagem</option><option value="note" data-v-965c2e5e>Escrever uma nova anotação</option></optgroup><optgroup label="Baixar dados da tabela como:" data-v-965c2e5e><option value="download_csv" data-v-965c2e5e> Valores separados por vírgula (.csv) </option><option value="download_xlsx" data-v-965c2e5e>Microsoft excel (.xlsx)</option><option value="download_html" data-v-965c2e5e>Tabela HTML</option><option value="download_json" data-v-965c2e5e> JavaScript Object Notation (.json) </option><option value="download_ods" data-v-965c2e5e>OpenDocument (.ods)</option><option value="download_pdf" data-v-965c2e5e> Formato de documento portável (.pdf) </option></optgroup><optgroup label="Inscrições" data-v-965c2e5e><option value="edit_enrolment" data-v-965c2e5e> Editar matrículas de usuários selecionados </option><option value="delete_enrolment" data-v-965c2e5e> Excluir matrículas de usuários selecionados </option></optgroup>',4)]),544),[[Kl,n.selectedBulkAction]])])]),!n.classDetails||((Ae=n.classDetails)==null?void 0:Ae.operational_cycle)!==2?(O(),S("div",Jx,[k(m,{variant:"primary",label:"Matricular usuários",onClick:a.addNewUser},null,8,["onClick"])])):X("",!0),k(se,{show:n.showEnrollmentModal,user:n.selectedUser,"course-name":((ae=n.classDetails)==null?void 0:ae.course_fullname)||"",onClose:a.closeEnrollmentModal},null,8,["show","user","course-name","onClose"]),k(A,{show:n.showEnrolmentModal,offerclassid:n.offerclassid,roles:n.roleOptions,onClose:a.closeEnrolmentModal,onSuccess:a.handleEnrolmentSuccess},null,8,["show","offerclassid","roles","onClose","onSuccess"]),k(re,{show:n.showEditEnrollmentModal,user:n.selectedUser,offerclassid:n.offerclassid,onClose:a.closeEditEnrollmentModal,onSuccess:a.handleEditEnrollmentSuccess,onError:a.handleEditEnrollmentError},null,8,["show","user","offerclassid","onClose","onSuccess","onError"]),k(Y,{show:n.showBulkEditEnrollmentModal,users:n.selectedUsers.map(I=>n.enrolments.find(be=>be.id===I)).filter(Boolean),offerclassid:n.offerclassid,onClose:t[19]||(t[19]=I=>this.showBulkEditEnrollmentModal=!1),onSuccess:a.handleBulkEditEnrollmentSuccess,onError:a.handleBulkEditEnrollmentError},null,8,["show","users","offerclassid","onSuccess","onError"]),k(ye,{show:n.showBulkDeleteEnrollmentModal,users:n.selectedUsers.map(I=>n.enrolments.find(be=>be.id===I)).filter(Boolean),offerclassid:n.offerclassid,onClose:t[20]||(t[20]=I=>n.showBulkDeleteEnrollmentModal=!1),onConfirm:a.confirmeBulkDeleteEnrollment,onError:a.handleBulkDeleteEnrollmentError},null,8,["show","users","offerclassid","onConfirm","onError"]),k(Z,{"is-loading":n.loading},null,8,["is-loading"]),k(fe,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])])}const eO=He(gx,[["render",Xx],["__scopeId","data-v-965c2e5e"]]),GR="",tO={name:"CollapsibleTable",props:{headers:{type:Array,required:!0},items:{type:Array,required:!0},sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1},expandable:{type:Boolean,default:!1}},data(){return{expandedRows:[]}},methods:{handleSort(e){this.$emit("sort",{sortBy:e,sortDesc:this.sortBy===e?!this.sortDesc:!1})},toggleExpand(e){const t=this.expandedRows.indexOf(e);t===-1?this.expandedRows.push(e):this.expandedRows.splice(t,1)}}},sO={class:"table-responsive"},rO={class:"table"},nO={key:0,class:"expand-column"},oO=["onClick","data-value"],iO={key:0,class:"sort-icon"},aO={key:0},lO={key:0,class:"expand-column"},uO=["onClick","title"],cO=["colspan"],dO={class:"expanded-content"},fO={key:1},hO=["colspan"];function pO(e,t,s,i,n,a){return O(),S("div",sO,[f("table",rO,[f("thead",null,[f("tr",null,[s.expandable?(O(),S("th",nO)):X("",!0),(O(!0),S(Ie,null,at(s.headers,u=>(O(),S("th",{key:u.value,onClick:c=>u.sortable?a.handleSort(u.value):null,class:he({sortable:u.sortable}),"data-value":u.value},[Je(q(u.text)+" ",1),u.sortable?(O(),S("span",iO,[f("i",{class:he(["fas",{"fa-sort":s.sortBy!==u.value,"fa-sort-up":s.sortBy===u.value&&!s.sortDesc,"fa-sort-down":s.sortBy===u.value&&s.sortDesc}])},null,2)])):X("",!0)],10,oO))),128))])]),s.items.length>0?(O(),S("tbody",aO,[(O(!0),S(Ie,null,at(s.items,(u,c)=>(O(),S(Ie,{key:u.id},[f("tr",{class:he({expanded:n.expandedRows.includes(u.id)})},[s.expandable?(O(),S("td",lO,[f("button",{class:"btn-expand",onClick:h=>a.toggleExpand(u.id),title:n.expandedRows.includes(u.id)?"Recolher":"Expandir"},[f("div",{class:he(["icon-container",{"is-expanded":n.expandedRows.includes(u.id)}])},t[0]||(t[0]=[f("svg",{width:"16",height:"16",viewBox:"0 0 24 24",class:"expand-icon"},[f("rect",{x:"5",y:"11",width:"14",height:"2",fill:"var(--primary)"}),f("rect",{x:"11",y:"5",width:"2",height:"14",fill:"var(--primary)",class:"vertical-line"})],-1)]),2)],8,uO)])):X("",!0),(O(!0),S(Ie,null,at(s.headers,h=>(O(),S("td",{key:`${u.id}-${h.value}`},[Vt(e.$slots,"item-"+h.value,{item:u},()=>[Je(q(u[h.value]),1)],!0)]))),128))],2),s.expandable?(O(),S("tr",{key:0,class:he(["expanded-row",{"is-visible":n.expandedRows.includes(u.id)}])},[f("td",{colspan:s.headers.length+1},[f("div",dO,[Vt(e.$slots,"expanded-content",{item:u},void 0,!0)])],8,cO)],2)):X("",!0)],64))),128))])):(O(),S("tbody",fO,[f("tr",null,[f("td",{colspan:s.headers.length+(s.expandable?1:0)},[Vt(e.$slots,"empty-state",{},()=>[t[1]||(t[1]=f("div",{class:"empty-state"},[f("span",null,"Não existem registros")],-1))],!0)],8,hO)])]))])])}const mO=He(tO,[["render",pO],["__scopeId","data-v-049f598f"]]),KR="",gO={name:"TextEditor",props:{modelValue:{type:String,default:""},label:{type:String,default:""},placeholder:{type:String,default:"Digite o conteúdo aqui..."},rows:{type:Number,default:5},disabled:{type:Boolean,default:!1}},emits:["update:modelValue"],data(){return{showHtmlSource:!1,htmlContent:this.modelValue}},mounted(){this.$refs.editableContent&&(this.$refs.editableContent.textContent=this.modelValue)},watch:{modelValue:{handler(e){this.showHtmlSource?this.htmlContent=e:this.$refs.editableContent&&this.$refs.editableContent.textContent!==e&&(this.$refs.editableContent.textContent=e)},immediate:!0}},methods:{applyFormat(e,t=null){this.showHtmlSource||(document.execCommand(e,!1,t),this.updateContent())},insertLink(){if(this.showHtmlSource)return;const e=prompt("Digite a URL do link:","http://");e&&this.applyFormat("createLink",e)},insertImage(){if(this.showHtmlSource)return;const e=prompt("Digite a URL da imagem:","http://");e&&this.applyFormat("insertImage",e)},toggleHtmlView(){this.showHtmlSource?this.$nextTick(()=>{this.$refs.editableContent&&(this.$refs.editableContent.textContent=this.htmlContent)}):this.htmlContent=this.$refs.editableContent.textContent,this.showHtmlSource=!this.showHtmlSource},updateContent(){if(!this.showHtmlSource&&this.$refs.editableContent){const e=this.$refs.editableContent.textContent;this.$emit("update:modelValue",e)}},updateHtmlContent(){this.showHtmlSource&&this.$emit("update:modelValue",this.htmlContent)}}},vO={class:"text-editor-container"},_O={class:"editor-toolbar"},yO={class:"toolbar-group"},bO=["disabled"],wO=["disabled"],EO=["disabled"],CO=["disabled"],DO={class:"toolbar-group"},xO=["disabled"],OO=["disabled"],SO=["contenteditable"],TO=["rows","placeholder","disabled"];function NO(e,t,s,i,n,a){return O(),S("div",vO,[s.label?(O(),S("label",{key:0,class:he(["filter-label",{disabled:s.disabled}])},q(s.label),3)):X("",!0),f("div",{class:he(["editor-container",{disabled:s.disabled}])},[f("div",_O,[f("div",yO,[f("button",{class:"btn-editor",onClick:t[0]||(t[0]=u=>!s.disabled&&a.applyFormat("bold")),title:"Negrito",disabled:s.disabled},t[10]||(t[10]=[f("i",{class:"fas fa-bold"},null,-1)]),8,bO),f("button",{class:"btn-editor",onClick:t[1]||(t[1]=u=>!s.disabled&&a.applyFormat("italic")),title:"Itálico",disabled:s.disabled},t[11]||(t[11]=[f("i",{class:"fas fa-italic"},null,-1)]),8,wO),f("button",{class:"btn-editor",onClick:t[2]||(t[2]=u=>!s.disabled&&a.applyFormat("underline")),title:"Sublinhado",disabled:s.disabled},t[12]||(t[12]=[f("i",{class:"fas fa-underline"},null,-1)]),8,EO),f("button",{class:"btn-editor",onClick:t[3]||(t[3]=u=>!s.disabled&&a.applyFormat("strikethrough")),title:"Tachado",disabled:s.disabled},t[13]||(t[13]=[f("i",{class:"fas fa-strikethrough"},null,-1)]),8,CO)]),t[16]||(t[16]=f("div",{class:"toolbar-divider"},null,-1)),f("div",DO,[f("button",{class:"btn-editor",onClick:t[4]||(t[4]=u=>!s.disabled&&a.applyFormat("insertUnorderedList")),title:"Lista não ordenada",disabled:s.disabled},t[14]||(t[14]=[f("i",{class:"fas fa-list-ul"},null,-1)]),8,xO),f("button",{class:"btn-editor",onClick:t[5]||(t[5]=u=>!s.disabled&&a.applyFormat("insertOrderedList")),title:"Lista ordenada",disabled:s.disabled},t[15]||(t[15]=[f("i",{class:"fas fa-list-ol"},null,-1)]),8,OO)])]),n.showHtmlSource?ut((O(),S("textarea",{key:1,"onUpdate:modelValue":t[8]||(t[8]=u=>n.htmlContent=u),class:"editor-textarea",rows:s.rows,placeholder:s.placeholder,onInput:t[9]||(t[9]=(...u)=>a.updateHtmlContent&&a.updateHtmlContent(...u)),disabled:s.disabled},null,40,TO)),[[Xt,n.htmlContent]]):(O(),S("div",{key:0,class:"editor-content",contenteditable:!s.disabled,onInput:t[6]||(t[6]=(...u)=>a.updateContent&&a.updateContent(...u)),onKeyup:t[7]||(t[7]=(...u)=>a.updateContent&&a.updateContent(...u)),ref:"editableContent"},null,40,SO))],2)])}const ap=He(gO,[["render",NO],["__scopeId","data-v-672cb06c"]]),YR="",QR="",IO={name:"AddCourseModal",components:{CustomInput:Lo,CustomButton:Fn,CustomTable:hn,Pagination:pn,Autocomplete:$o,FilterTag:Bo},props:{modelValue:{type:Boolean,required:!0},offerId:{type:Number,required:!0}},emits:["update:modelValue","confirm"],data(){return{selectedCategory:null,selectedCategoryObject:null,selectedCourse:null,categoryOptions:[],courseOptions:[],currentPage:1,perPage:5,sortBy:"name",sortDesc:!1,loadingCategories:!1,loadingCourses:!1,loadingCurrentCourses:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,loadingMoreCourses:!1,tableHeaders:[{text:"CURSO",value:"name",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1,align:"right"}],selectedCoursesPreview:[],existingCourses:[]}},computed:{filteredCourses(){const e=[...this.selectedCoursesPreview].sort((i,n)=>{const a=this.sortDesc?-1:1;return i[this.sortBy]<n[this.sortBy]?-1*a:i[this.sortBy]>n[this.sortBy]?1*a:0}),t=(this.currentPage-1)*this.perPage,s=t+this.perPage;return e.slice(t,s)},totalPages(){return Math.ceil(this.selectedCoursesPreview.length/this.perPage)},courseNoResultsText(){return this.loadingCourses?"Buscando cursos...":this.loadingMoreCourses?"Carregando mais cursos...":this.selectedCategory?this.courseOptions.length===0&&this.selectedCategory?"Todos os cursos já foram adicionados":"Nenhum curso encontrado":"Selecione uma categoria primeiro"}},watch:{modelValue(e){e?(this.loadCurrentCourses(),this.loadAllCategories()):(this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.categoryOptions=[],this.courseOptions=[],this.selectedCoursesPreview=[])},selectedCategory(e){e||(this.courseOptions=[],this.selectedCourse=null,this.selectedCategoryObject=null,this.loadCurrentCourses())},courseOptions(e){e.length<10&&this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses&&this.$nextTick(()=>{this.loadMoreCourses(),console.log("Carregando mais cursos... via watch")})}},methods:{closeModal(){this.$emit("update:modelValue",!1),this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.selectedCoursesPreview=[]},async confirm(){try{if(this.selectedCoursesPreview.length===0){console.warn("Nenhum curso selecionado para adicionar"),this.closeModal();return}const e=this.selectedCoursesPreview.map(t=>t.id);await n0(this.offerId,e),this.$emit("confirm",this.selectedCoursesPreview),this.closeModal()}catch(e){console.error("Erro ao salvar cursos:",e)}},async loadCurrentCourses(){console.log("loadCurrentCourses this.offerId:",this.offerId);try{this.loadingCurrentCourses=!0;const e=await fu(this.offerId);console.log("loadCurrentCourses response:",e),e&&e.data&&(Array.isArray(e.data)?this.existingCourses=e.data.map(t=>({id:t.courseid,name:t.fullname,offerCourseId:t.id})):this.existingCourses=[])}catch(e){console.error("Erro ao carregar cursos da oferta:",e),this.existingCourses=[]}finally{this.loadingCurrentCourses=!1}},async loadAllCategories(){try{this.loadingCategories=!0,this.categoryOptions=[];const e=await Uo("");e&&e.data&&(this.categoryOptions=e.data.map(t=>({value:t.id,label:t.name})))}catch(e){console.error("Erro ao carregar todas as categorias:",e),this.categoryOptions=[]}finally{this.loadingCategories=!1}},handleCategorySelect(e){if(!e){this.removeCategory();return}this.selectedCategoryObject=e,this.selectedCategory=e.value,this.courseOptions=[],this.selectedCourse=null,this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadCoursesForCategory(e.value)},async loadCoursesForCategory(e,t=1,s=!1,i=""){if(!e)return;const n=typeof e=="object"?e.value:e;try{t===1?(this.loadingCourses=!0,s||(this.courseOptions=[])):this.loadingMoreCourses=!0;const a=await sp(this.offerId,n,i,t,this.coursesPerPage);let u=null,c=[];try{if(Array.isArray(a)&&a.length>0?a[0].error===!1&&a[0].data?a[0].data.courses?(u=a[0].data,c=u.courses||[]):Array.isArray(a[0].data)?(c=a[0].data,u={page:1,total_pages:1}):a[0].data.data&&Array.isArray(a[0].data.data)&&(c=a[0].data.data,u={page:a[0].data.page||1,total_pages:a[0].data.total_pages||1}):(c=a,u={page:1,total_pages:1}):a&&typeof a=="object"&&(a.data&&a.data.courses?(c=a.data.courses,u={page:a.data.page||1,total_pages:a.data.total_pages||1}):a.courses?(c=a.courses,u={page:a.page||1,total_pages:a.total_pages||1}):a.data&&Array.isArray(a.data)&&(c=a.data,u={page:1,total_pages:1})),c.length===0&&a&&typeof a=="object"&&!Array.isArray(a)){for(const h in a)if(Array.isArray(a[h])){c=a[h],u={page:1,total_pages:1};break}}}catch(h){console.error("Erro ao processar resposta:",h)}if(u){if(this.coursesPage=u.page||1,this.coursesTotalPages=u.total_pages||1,this.hasMoreCourses=(u.page||1)<(u.total_pages||1),c&&c.length>0){const m=c.filter(p=>!this.existingCourses.some(v=>v.id===p.id)&&!this.selectedCoursesPreview.some(v=>v.id===p.id)).map(p=>({value:p.id,label:p.fullname}));s?this.courseOptions=[...this.courseOptions,...m]:this.courseOptions=m}}else console.warn("Formato de resposta inesperado")}catch(a){console.error("Erro ao carregar cursos da categoria:",a),s||(this.courseOptions=[])}finally{t===1?this.loadingCourses=!1:this.loadingMoreCourses=!1}},async loadMoreCourses(){if(this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses){const e=this.coursesPage+1;await this.loadCoursesForCategory(this.selectedCategory,e,!0)}},async handleCourseSearch(e){this.selectedCategory&&(console.log("Buscando cursos com termo:",e),this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,await this.loadCoursesForCategory(this.selectedCategory,1,!1,e||""))},handleCourseSelect(e){e&&!this.selectedCoursesPreview.some(t=>t.id===e.value)&&(this.selectedCoursesPreview.push({id:e.value,name:e.label}),this.courseOptions=this.courseOptions.filter(t=>t.value!==e.value),this.currentPage=1),this.selectedCourse=null},removeCourse(e){const t=this.selectedCoursesPreview.findIndex(s=>s.id===e.id);if(t!==-1){const s=this.selectedCoursesPreview.splice(t,1)[0];this.currentPage>1&&this.currentPage>Math.ceil(this.selectedCoursesPreview.length/this.perPage)&&(this.currentPage=Math.max(1,this.currentPage-1)),this.selectedCategory?sp(this.offerId,this.selectedCategory,"",1,this.coursesPerPage).then(i=>{let n=[];try{if(Array.isArray(i)&&i.length>0?i[0].error===!1&&i[0].data?i[0].data.courses?n=i[0].data.courses||[]:Array.isArray(i[0].data)?n=i[0].data:i[0].data.data&&Array.isArray(i[0].data.data)&&(n=i[0].data.data):n=i:i&&typeof i=="object"&&(i.data&&i.data.courses?n=i.data.courses:i.courses?n=i.courses:i.data&&Array.isArray(i.data)&&(n=i.data)),n.length===0&&i&&typeof i=="object"&&!Array.isArray(i)){for(const a in i)if(Array.isArray(i[a])){n=i[a];break}}}catch(a){console.error("Erro ao processar resposta em removeCourse:",a)}n&&n.length>0&&n.find(u=>u.id===s.id)&&this.courseOptions.push({value:s.id,label:s.name})}).catch(i=>{console.error("Erro ao verificar categoria do curso:",i)}):this.courseOptions.push({value:s.id,label:s.name})}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t},handlePageChange(e){this.currentPage=e},handlePerPageChange(e){this.perPage=e,this.currentPage=1},removeCategory(){this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.courseOptions=[],this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadCurrentCourses()}}},AO={class:"modal-header"},MO={class:"modal-body"},PO={class:"search-section"},kO={class:"search-group"},VO={class:"search-group"},RO={class:"table-container"},UO={key:0,class:"empty-preview-message"},LO={class:"action-buttons"},FO=["onClick"],BO={class:"modal-footer"};function $O(e,t,s,i,n,a){const u=te("Autocomplete"),c=te("CustomTable"),h=te("Pagination"),m=te("CustomButton");return s.modelValue?(O(),S("div",{key:0,class:"modal-overlay",onClick:t[6]||(t[6]=(...p)=>a.closeModal&&a.closeModal(...p))},[f("div",{class:"modal-content",onClick:t[5]||(t[5]=Ut(()=>{},["stop"]))},[f("div",AO,[t[8]||(t[8]=f("h2",null,"Adicionar curso",-1)),f("button",{class:"close-button",onClick:t[0]||(t[0]=(...p)=>a.closeModal&&a.closeModal(...p))},t[7]||(t[7]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",MO,[t[11]||(t[11]=f("h3",{class:"section-title"},"SELECIONAR CURSO",-1)),f("div",PO,[f("div",kO,[k(u,{modelValue:n.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=p=>n.selectedCategory=p),items:n.categoryOptions,label:"Categoria",placeholder:"Pesquisar...","input-max-width":250,loading:n.loadingCategories,"show-filter-tags":!1,"show-selected-in-input":!0,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada",onSelect:a.handleCategorySelect},null,8,["modelValue","items","loading","no-results-text","onSelect"])]),f("div",VO,[k(u,{modelValue:n.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=p=>n.selectedCourse=p),items:n.courseOptions,label:"Curso",placeholder:"Pesquisar...","input-max-width":250,disabled:!n.selectedCategory,loading:n.loadingCourses||n.loadingMoreCourses,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"keep-open-on-select":!0,"no-results-text":a.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","disabled","loading","no-results-text","onSelect","onLoadMore","onSearch"])])]),f("div",RO,[n.selectedCoursesPreview.length===0?(O(),S("div",UO,t[9]||(t[9]=[f("p",null,"Selecione cursos acima para adicioná-los à oferta",-1)]))):(O(),Rt(c,{key:1,headers:n.tableHeaders,items:a.filteredCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-actions":Ne(({item:p})=>[f("div",LO,[f("button",{class:"btn-action btn-delete",onClick:v=>a.removeCourse(p),title:"Remover da lista"},t[10]||(t[10]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,FO)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]))]),n.selectedCoursesPreview.length>0?(O(),Rt(h,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":[t[3]||(t[3]=p=>n.currentPage=p),a.handlePageChange],"per-page":n.perPage,"onUpdate:perPage":[t[4]||(t[4]=p=>n.perPage=p),a.handlePerPageChange],total:n.selectedCoursesPreview.length},null,8,["current-page","per-page","total","onUpdate:currentPage","onUpdate:perPage"])):X("",!0)]),f("div",BO,[k(m,{variant:"primary",label:"Confirmar",disabled:n.selectedCoursesPreview.length===0,onClick:a.confirm},null,8,["disabled","onClick"]),k(m,{variant:"secondary",label:"Cancelar",onClick:a.closeModal},null,8,["onClick"])])])])):X("",!0)}const jO=He(IO,[["render",$O],["__scopeId","data-v-0a88ee2a"]]),ZR="",JR="",HO={name:"DuplicateClassModal",components:{Autocomplete:$o,CustomTable:hn,Pagination:pn},props:{show:{type:Boolean,default:!1},turma:{type:Object,default:null},parentCourse:{type:Object,default:null},offerId:{type:[Number,String],default:null}},emits:["close","success","loading","error"],data(){return{selectedCategory:null,selectedCategoryObject:null,categoryOptions:[],loadingCategories:!1,selectedCourse:null,targetCourseOptions:[],selectedCoursesPreview:[],loadingCourses:!1,loadingMoreCourses:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,currentPage:1,perPage:5,sortBy:"label",sortDesc:!1,tableHeaders:[{text:"CURSO",value:"label",sortable:!0},{text:"CATEGORIA",value:"category_name",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1,align:"right"}],duplicatingCourses:!1,duplicatedCount:0,totalToDuplicate:0,existingCourses:[]}},computed:{courseNoResultsText(){return this.selectedCategory?this.loadingCourses||this.loadingMoreCourses?"Carregando cursos...":this.targetCourseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado":"Selecione uma categoria primeiro"},filteredCourses(){const e=(this.currentPage-1)*this.perPage,t=e+this.perPage;return[...this.selectedCoursesPreview].sort((i,n)=>{const a=i[this.sortBy],u=n[this.sortBy];return a<u?this.sortDesc?1:-1:a>u?this.sortDesc?-1:1:0}).slice(e,t)}},watch:{show(e){e&&this.turma&&this.parentCourse?(this.resetForm(),this.$nextTick(()=>{this.loadAllCategories()})):this.resetForm()},turma(){this.show&&(this.resetForm(),this.loadAllCategories())},parentCourse(){this.show&&(this.resetForm(),this.loadAllCategories())},selectedCategory(e){e?this.loadCoursesForCategory(e):(this.targetCourseOptions=[],this.selectedCourse=null,this.selectedCategoryObject=null)}},methods:{resetForm(){this.selectedCategory=null,this.selectedCategoryObject=null,this.categoryOptions=[],this.loadingCategories=!1,this.selectedCourse=null,this.targetCourseOptions=[],this.selectedCoursesPreview=[],this.loadingCourses=!1,this.loadingMoreCourses=!1,this.coursesPage=1,this.coursesPerPage=20,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.currentPage=1,this.duplicatingCourses=!1,this.duplicatedCount=0,this.totalToDuplicate=0,this.existingCourses=[]},async loadAllCategories(){try{this.loadingCategories=!0,this.categoryOptions=[];const e=await Uo("",this.offerId);e&&e.data&&(this.categoryOptions=e.data.map(t=>({value:t.id,label:t.name})))}catch(e){console.error("Erro ao carregar categorias:",e),this.$emit("error","Erro ao carregar categorias. Por favor, tente novamente."),this.categoryOptions=[]}finally{this.loadingCategories=!1}},handleCategorySelect(e){if(!e){this.removeCategory();return}this.selectedCategoryObject=e,this.selectedCategory=e.value,this.targetCourseOptions=[],this.selectedCourse=null,this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1},removeCategory(){this.selectedCategory=null,this.selectedCategoryObject=null,this.selectedCourse=null,this.targetCourseOptions=[],this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1},async loadCoursesForCategory(e,t=1,s=!1,i=""){if(!e||!this.turma)return;const n=typeof e=="object"?e.value:e;try{t===1?(this.loadingCourses=!0,s||(this.targetCourseOptions=[])):this.loadingMoreCourses=!0;const a=await h0(this.turma.id);let u=[];a&&Array.isArray(a)&&(u=a);const c=this.parentCourse.offerCourseId||this.parentCourse.id;u=u.filter(m=>{const p=m.categoryid,v=String(p)===String(n),w=m.offercourseid||m.id,D=String(w)!==String(c),V=!i||m.name&&m.name.toLowerCase().includes(i.toLowerCase())||m.fullname&&m.fullname.toLowerCase().includes(i.toLowerCase());return v&&D&&V}),u=u.filter(m=>{const p=m.offercourseid||m.id;return!this.selectedCoursesPreview.some(v=>String(v.value)===String(p))});const h=u.map(m=>{let p=m.offercourseid||m.id;return p==null?null:{value:p,label:m.name||m.fullname||m.coursename||`Curso ${p}`,categoryid:m.categoryid,category_name:m.category_name}}).filter(m=>m!==null);s?this.targetCourseOptions=[...this.targetCourseOptions,...h]:this.targetCourseOptions=h,this.hasMoreCourses=h.length>=this.coursesPerPage,t>this.coursesPage&&(this.coursesPage=t)}catch(a){console.error("Erro ao carregar cursos da categoria:",a),this.$emit("error","Erro ao carregar cursos. Por favor, tente novamente."),s||(this.targetCourseOptions=[])}finally{t===1?this.loadingCourses=!1:this.loadingMoreCourses=!1}},async loadMoreCourses(){if(this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses){const e=this.coursesPage+1;await this.loadCoursesForCategory(this.selectedCategory,e,!0)}},async handleCourseSearch(e){this.selectedCategory&&(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,await this.loadCoursesForCategory(this.selectedCategory,1,!1,e||""))},handleCourseSelect(e){e&&!this.selectedCoursesPreview.some(t=>t.value===e.value)&&(this.selectedCoursesPreview.push({value:e.value,label:e.label,categoryid:e.categoryid,category_name:e.category_name}),this.targetCourseOptions=this.targetCourseOptions.filter(t=>t.value!==e.value)),this.selectedCourse=null},removeCourse(e){const t=this.selectedCoursesPreview.findIndex(s=>s.value===e.value);if(t!==-1){const s=this.selectedCoursesPreview.splice(t,1)[0];this.targetCourseOptions.push(s)}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t},async handleConfirm(){if(!(!this.turma||this.selectedCoursesPreview.length===0))try{this.$emit("loading",!0);const e=this.turma.nome,t=parseInt(this.turma.id,10);if(isNaN(t))throw new Error("ID da turma inválido");this.duplicatingCourses=!0,this.totalToDuplicate=this.selectedCoursesPreview.length,this.duplicatedCount=0;const s=[];for(const i of this.selectedCoursesPreview){const n=parseInt(i.value,10);if(isNaN(n)){console.error(`ID inválido para o curso ${i.label}`);continue}try{this.$emit("loading",!0,`Duplicando para ${i.label} (${this.duplicatedCount+1}/${this.totalToDuplicate})`);const a=await f0(t,n);s.push({turmaNome:e,targetCourseName:i.label,turmaId:t,targetCourseId:n,result:a}),this.duplicatedCount++}catch(a){console.error(`Erro ao duplicar para o curso ${i.label}:`,a),this.$emit("error",`Erro ao duplicar para o curso ${i.label}: ${a.message}`)}}if(this.duplicatingCourses=!1,s.length>0)this.$emit("success",{turmaNome:e,duplicatedCount:s.length,totalSelected:this.totalToDuplicate,results:s}),this.resetForm(),this.$emit("close");else throw new Error("Nenhuma turma foi duplicada com sucesso.")}catch(e){this.$emit("error",e.message||"Erro ao duplicar turmas.")}finally{this.duplicatingCourses=!1,this.$emit("loading",!1)}}}},qO={class:"modal-header"},zO={class:"modal-title"},WO={class:"modal-body"},GO={class:"search-section"},KO={class:"search-group"},YO={class:"search-group"},QO={class:"table-container"},ZO={key:0,class:"empty-preview-message"},JO={class:"action-buttons"},XO=["onClick"],eS={class:"modal-footer"},tS=["disabled"];function sS(e,t,s,i,n,a){var m;const u=te("Autocomplete"),c=te("CustomTable"),h=te("Pagination");return s.show?(O(),S("div",{key:0,class:"modal-backdrop",onClick:t[8]||(t[8]=p=>e.$emit("close"))},[f("div",{class:"modal-container",onClick:t[7]||(t[7]=Ut(()=>{},["stop"]))},[f("div",qO,[f("h3",zO,'Duplicar Turma "'+q((m=s.turma)==null?void 0:m.nome)+'"',1),f("button",{class:"close-button",onClick:t[0]||(t[0]=p=>e.$emit("close"))},t[9]||(t[9]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",WO,[t[12]||(t[12]=f("h3",{class:"section-title"},"SELECIONAR CURSO",-1)),f("div",GO,[f("div",KO,[k(u,{modelValue:n.selectedCategory,"onUpdate:modelValue":t[1]||(t[1]=p=>n.selectedCategory=p),items:n.categoryOptions,label:"Categoria",placeholder:"Pesquisar...","input-max-width":250,loading:n.loadingCategories,"show-filter-tags":!1,"show-selected-in-input":!0,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada",onSelect:a.handleCategorySelect},null,8,["modelValue","items","loading","no-results-text","onSelect"])]),f("div",YO,[k(u,{modelValue:n.selectedCourse,"onUpdate:modelValue":t[2]||(t[2]=p=>n.selectedCourse=p),items:n.targetCourseOptions,label:"Curso",placeholder:"Pesquisar...","input-max-width":250,disabled:!n.selectedCategory,loading:n.loadingCourses||n.loadingMoreCourses,"auto-open":!0,"has-search-icon":!0,"max-label-length":25,"no-results-text":a.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","disabled","loading","no-results-text","onSelect","onLoadMore","onSearch"])])]),f("div",QO,[n.selectedCoursesPreview.length===0?(O(),S("div",ZO,t[10]||(t[10]=[f("p",null,"Selecione cursos acima para duplicar a turma",-1)]))):(O(),Rt(c,{key:1,headers:n.tableHeaders,items:a.filteredCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort},{"item-actions":Ne(({item:p})=>[f("div",JO,[f("button",{class:"btn-action btn-delete",onClick:v=>a.removeCourse(p),title:"Remover da lista"},t[11]||(t[11]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,XO)])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]))]),n.selectedCoursesPreview.length>0?(O(),Rt(h,{key:0,"current-page":n.currentPage,"onUpdate:currentPage":t[3]||(t[3]=p=>n.currentPage=p),"per-page":n.perPage,"onUpdate:perPage":t[4]||(t[4]=p=>n.perPage=p),total:n.selectedCoursesPreview.length},null,8,["current-page","per-page","total"])):X("",!0)]),f("div",eS,[f("button",{class:"btn-primary",onClick:t[5]||(t[5]=(...p)=>a.handleConfirm&&a.handleConfirm(...p)),disabled:n.selectedCoursesPreview.length===0}," Duplicar ",8,tS),f("button",{class:"btn-secondary",onClick:t[6]||(t[6]=p=>e.$emit("close"))},"Cancelar")])])])):X("",!0)}const rS=He(HO,[["render",sS],["__scopeId","data-v-7ebf3397"]]),XR="",nS={name:"HelpIcon",props:{title:{type:String,default:"Ajuda"},text:{type:String,required:!0},postion:{type:String,default:"right"}},computed:{content(){return`<div class="no-overflow"><p class="mb-0">${this.text}</p></div>`}}},oS=["data-content","aria-label"],iS=["title","aria-label"];function aS(e,t,s,i,n,a){return O(),S("a",{class:"btn btn-link p-0","data-container":"body","data-toggle":"popover","data-placement":"auto","data-content":a.content,"data-html":"true",tabindex:"0","data-trigger":"focus","aria-label":s.title,role:"button"},[f("i",{class:"icon fa fa-question-circle text-info fa-fw",title:s.title,"aria-label":s.title,role:"img"},null,8,iS)],8,oS)}const yu=He(nS,[["render",aS]]),e3="",lS={name:"EnrolTypeModal",components:{CustomSelect:mr,HelpIcon:yu},props:{show:{type:Boolean,default:!1},title:{type:String,default:"Tipo de Inscrição"},size:{type:String,default:"md",validator:e=>["sm","md","lg","xl"].includes(e)},closeOnBackdrop:{type:Boolean,default:!0},confirmButtonText:{type:String,default:"Continuar"},cancelButtonText:{type:String,default:"Cancelar"},offercourseid:{type:[Number,String],required:!0},offerid:{type:[Number,String],required:!1,default:"0"}},emits:["close","confirm"],data(){return{selectedEnrolType:"",enrolmentMethods:[],loading:!1}},mounted(){this.$nextTick(()=>{this.initializePopovers()})},watch:{show(e){e&&(this.loadEnrolmentMethods(),this.$nextTick(()=>{this.initializePopovers()}))}},methods:{async loadEnrolmentMethods(){this.loading=!0;const e=await np(!0);e&&Array.isArray(e.data)&&(this.enrolmentMethods=e.data.map(t=>({value:t.enrol,label:t.name}))),this.loading=!1},handleConfirm(){this.selectedEnrolType&&this.$emit("confirm",{enrolType:this.selectedEnrolType,offercourseid:this.offercourseid,offerid:this.offerid})},initializePopovers(){typeof $<"u"&&typeof $.fn.popover<"u"?$('[data-toggle="popover"]').popover({container:"body",trigger:"focus",html:!0}):console.warn("jQuery ou Bootstrap não estão disponíveis para inicializar popovers")}}},uS={class:"modal-header"},cS={class:"modal-title"},dS={class:"modal-body"},fS={class:"enrol-type-modal"},hS={class:"form-group mb-3"},pS={class:"label-with-help"},mS={class:"limited-width-input",style:{"max-width":"280px"}},gS={class:"modal-footer"},vS={class:"footer-buttons"},_S=["disabled"];function yS(e,t,s,i,n,a){const u=te("HelpIcon"),c=te("CustomSelect");return s.show?(O(),S("div",{key:0,class:"modal-backdrop",onClick:t[5]||(t[5]=h=>s.closeOnBackdrop?e.$emit("close"):null)},[f("div",{class:he(["modal-container",[`modal-${s.size}`]]),onClick:t[4]||(t[4]=Ut(()=>{},["stop"]))},[f("div",uS,[f("h3",cS,q(s.title),1),f("button",{class:"modal-close",onClick:t[0]||(t[0]=h=>e.$emit("close"))},t[6]||(t[6]=[f("i",{class:"fas fa-times"},null,-1)]))]),f("div",dS,[f("div",fS,[t[9]||(t[9]=f("p",{class:"modal-description"}," Selecione o tipo de inscrição para esta turma. Esta configuração não poderá ser alterada posteriormente. ",-1)),f("div",hS,[f("div",pS,[t[7]||(t[7]=f("label",{class:"form-label"},"Método de inscrição",-1)),t[8]||(t[8]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw m-0",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),k(u,{title:"Ajuda com método de inscrição",text:`Inscrição automática por público-alvo em ofertas: Nesta opção o usuário será inscrito automaticamente nos cursos que forem atribuídos para o público-alvo dele.
Inscrição manual em ofertas: Nesta opção o usuário será matriculado manualmente (em lote ou individualmente) por um perfil autorizado, através da página 'Usuários matriculados' contida em cada turma.
Autoisncrição em ofertas: Nesta opção o usuário visualizará os cursos que forem atribuídos para o público-alvo dele, porém, ele precisa clicar no curso para fazer sua matrícula em uma turma. Ou seja, ele não será inscrito automaticamente como no Tipo de inscrição 'Inscrição por público-alvo em ofertas'.`})]),f("div",mS,[k(c,{modelValue:n.selectedEnrolType,"onUpdate:modelValue":t[1]||(t[1]=h=>n.selectedEnrolType=h),options:[{value:"",label:"Selecione um método..."},...n.enrolmentMethods],width:280,required:""},null,8,["modelValue","options"])])])])]),f("div",gS,[t[10]||(t[10]=f("div",{class:"form-info"},[f("span",{style:{color:"#f8f9fa","font-size":"15px"}},"Este formulário contém campos obrigatórios marcados com"),f("i",{class:"fas fa-exclamation-circle",style:{color:"#dc3545","font-size":"0.85rem","vertical-align":"middle"}})],-1)),f("div",vS,[f("button",{class:"btn btn-primary",onClick:t[2]||(t[2]=(...h)=>a.handleConfirm&&a.handleConfirm(...h)),disabled:!n.selectedEnrolType},q(s.confirmButtonText),9,_S),f("button",{class:"btn btn-secondary",onClick:t[3]||(t[3]=h=>e.$emit("close"))},q(s.cancelButtonText),1)])])],2)])):X("",!0)}const bS=He(lS,[["render",yS],["__scopeId","data-v-4b89966a"]]),wS="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxwYXRoIGQ9Ik03IDE0cy0xIDAtMS0xIDEtNCA1LTQgNSAzIDUgNC0xIDEtMSAxSDd6bTQtNmEzIDMgMCAxIDAgMC02IDMgMyAwIDAgMCAwIDZ6IiBmaWxsPSIjZmZmIi8+CiAgICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0iTTUuMjE2IDE0QTIuMjM4IDIuMjM4IDAgMCAxIDUgMTNjMC0xLjM1NS42OC0yLjc1IDEuOTM2LTMuNzJBNi4zMjUgNi4zMjUgMCAwIDAgNSA5Yy00IDAtNSAzLTUgNHMxIDEgMSAxaDQuMjE2eiIgZmlsbD0iI2ZmZiIvPgogICAgPHBhdGggZD0iTTQuNSA4YTIuNSAyLjUgMCAxIDAgMC01IDIuNSAyLjUgMCAwIDAgMCA1eiIgZmlsbD0iI2ZmZiIvPgo8L3N2Zz4K",ES="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNOCAyYS41LjUgMCAwIDEgLjUuNXY1aDVhLjUuNSAwIDAgMSAwIDFoLTV2NWEuNS41IDAgMCAxLTEgMHYtNWgtNWEuNS41IDAgMSAxIDAtMWg1di01QS41LjUgMCAwIDEgOCAyeiIgZmlsbD0iI2ZmZiIvPgo8L3N2Zz4K",t3="",CS={name:"NewOfferView",components:{CustomTable:hn,CustomSelect:mr,CustomInput:Lo,CustomButton:Fn,Pagination:pn,CollapsibleTable:mO,PageHeader:sa,BackButton:_u,Autocomplete:$o,TextEditor:ap,CustomCheckbox:Xi,FilterRow:ea,FilterGroup:ta,FilterTag:Bo,FilterTags:ra,AddCourseModal:jO,ConfirmationModal:gu,Toast:Fo,HelpIcon:yu,DuplicateClassModal:rS,EnrolTypeModal:bS,LFLoading:mu},setup(){const e=Zi(),t=Zh();return{router:e,route:t}},directives:{tooltip:{mounted(e,t){e.setAttribute("title",t.value)},updated(e,t){e.setAttribute("title",t.value)}}},data(){return{icons:{users:wS,plus:ES},showAddCourseModalVisible:!1,showCourseStatusModal:!1,selectedCourse:null,showWarning:!0,isEditing:!1,offerId:null,showDeleteCourseModal:!1,courseToDelete:null,showDeleteClassModal:!1,classToDelete:null,classParentCourse:null,showClassStatusModal:!1,showDuplicateClassModal:!1,showEnrolTypeModal:!1,selectedClass:null,classToDuplicate:null,classToDuplicateParentCourse:null,selectedCourseForClass:null,loading:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,offer:{id:null,name:"",offerType:"",description:"",status:0},formErrors:{name:{hasError:!1,message:"Nome da oferta é obrigatório"},audiences:{hasError:!1,message:"Selecione pelo menos um público-alvo"}},offerTypeOptions:[],typeOptionsEnabled:!1,audienceTypeOptions:[],allAudiences:[],selectedAudiences:[],categoryOptions:[],courseOptions:[],selectedCategory:null,selectedCourse:null,inputFilters:{course:"",category:"",onlyActive:!1},appliedFilters:{course:"",category:"",onlyActive:!1},courseTableHeaders:[{text:"NOME DO CURSO",value:"name",sortable:!0},{text:"CATEGORIA",value:"category",sortable:!0},{text:"NÚMERO DE TURMAS",value:"turmasCount",sortable:!0},{text:"STATUS DO CURSO",value:"status",sortable:!0},{text:"AÇÕES",value:"actions",sortable:!1}],selectedCourses:[],currentPage:1,perPage:5,totalItems:0,sortBy:"id",sortDesc:!1,coursesPage:1,coursesPerPage:20,coursesTotalPages:1,hasMoreCourses:!1,loadingCourses:!1,loadingMoreCourses:!1,courseNoResultsText:"Nenhum curso encontrado"}},async mounted(){await this.loadInitialData(),this.offerId?(this.isEditing=!0,await this.loadOfferData(),await this.loadCategoryOptions(),await this.loadCourseOptions()):this.isEditing=!1,await this.loadTypeOptions(),await this.loadAllAudiences(),this.showWarning=!0},computed:{canManageCourses(){return this.isEditing&&this.offerId!==null},hasActiveFilters(){return this.appliedFilters.course}},methods:{getEnrolTypeLabel(e){if(!e)return"-";if(typeof e!="string")return String(e);const t=e.toLowerCase(),s={offer_manual:"Manual",offer_self:"Autoinscrição",manual:"Manual",self:"Autoinscrição",guest:"Visitante",cohort:"Coorte",database:"Base de dados",flatfile:"Arquivo plano",ldap:"LDAP",lti:"LTI",meta:"Meta-curso",mnet:"MNet",paypal:"PayPal",shibboleth:"Shibboleth"};if(s[e])return s[e];for(const[i,n]of Object.entries(s))if(i.toLowerCase()===t)return n;if(t==="offer_manual")return"Manual";if(t==="offer_self")return"Autoinscrição";for(const[i,n]of Object.entries(s))if(t.includes(i.toLowerCase()))return n;return e},formatDate(e){return e?new Date(e*1e3).toLocaleDateString("pt-BR"):"-"},async loadInitialData(){},async loadOfferData(){if(this.offerId){this.loading=!0;try{const e=await Xh(this.offerId);e&&e.data&&(this.offer={id:e.data.id,name:e.data.name,offerType:e.data.typeid,description:e.data.description||"",status:e.data.status||0},this.selectedAudiences=e.data.audiences?e.data.audiences.map(t=>({value:t.id,label:t.name})):[],await this.loadCourses())}catch{this.showErrorMessage("Erro ao carregar dados da oferta.")}finally{this.loading=!1}}},async loadTypeOptions(){try{const e=await du();if(e&&e.data){const{enabled:t,types:s}=e.data;this.typeOptionsEnabled=!!t,t&&Array.isArray(s)?this.offerTypeOptions=s.map(i=>({value:i,label:i.charAt(0).toUpperCase()+i.slice(1)})):(this.offerTypeOptions=[],this.typeOptionsEnabled=!1)}else this.offerTypeOptions=[],this.typeOptionsEnabled=!1}catch{this.typeOptionsEnabled=!1}},async loadAllAudiences(){if(this.allAudiences.length>0){this.audienceTypeOptions=[...this.allAudiences];return}this.loading=!0;try{const e=await tp("");e&&e.items&&(this.allAudiences=e.items.map(t=>({value:t.id,label:t.name})),this.audienceTypeOptions=[...this.allAudiences])}catch{this.showErrorMessage("Erro ao carregar públicos-alvo.")}finally{this.loading=!1}},async applyFilters(){this.appliedFilters=JSON.parse(JSON.stringify(this.inputFilters));try{this.loading=!0,await this.loadCourses()}catch{this.showErrorMessage("Erro ao aplicar filtros. Por favor, tente novamente.")}finally{this.loading=!1}this.currentPage=1},clearFilters(){this.inputFilters={course:"",category:"",onlyActive:!1},this.appliedFilters={course:"",category:"",onlyActive:!1},this.selectedCategory=null,this.selectedCourse=null,this.loadCourses(),this.loadCourseOptions()},async removeFilter(e){try{this.loading=!0,this.inputFilters[e]="",this.appliedFilters[e]="",e==="category"?(this.selectedCategory=null,await this.loadCourseOptions()):e==="course"&&(this.selectedCourse=null),this.hasActiveFilters?await this.applyFilters():await this.loadCourses()}catch{this.showErrorMessage("Erro ao remover filtro. Por favor, tente novamente.")}finally{this.loading=!1}},async loadCategoryOptions(){if(this.offerId)try{this.loading=!0;const e=await Uo("",this.offerId);e&&e.data&&Array.isArray(e.data)?this.categoryOptions=e.data.map(t=>({value:t.id,label:t.name})):this.categoryOptions=[]}catch{this.showErrorMessage("Erro ao carregar categorias.")}finally{this.loading=!1}},async loadCourseOptions(e=null,t=!0){if(this.offerId)try{if(this.loading=!0,e){await this.updateCourseOptionsByCategory(e);return}t?(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[]):this.loadingMoreCourses=!0;const s=await fu(this.offerId,{onlyActive:!1,page:this.coursesPage,perPage:this.coursesPerPage,sortBy:this.sortBy,sortDesc:this.sortDesc});if(s&&s.data&&s.data.courses){const{page:i,total_pages:n,courses:a}=s.data;this.coursesPage=i||this.coursesPage,this.coursesTotalPages=n||1,this.hasMoreCourses=this.coursesPage<this.coursesTotalPages;const u=a.map(c=>({value:c.courseid||c.id,label:c.fullname}));t?this.courseOptions=u:this.courseOptions=[...this.courseOptions,...u],this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado"}else if(s&&s.data&&Array.isArray(s.data)){const i=s.data.map(n=>({value:n.id||n.courseid,label:n.fullname}));t?this.courseOptions=i:this.courseOptions=[...this.courseOptions,...i],this.hasMoreCourses=!1,this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso disponível":"Nenhum curso encontrado"}else t&&(this.courseOptions=[]),this.hasMoreCourses=!1,this.courseNoResultsText="Nenhum curso disponível"}catch{this.showErrorMessage("Erro ao carregar cursos."),t&&(this.courseOptions=[]),this.hasMoreCourses=!1}finally{t?this.loadingCourses=!1:this.loadingMoreCourses=!1,this.loading=!1}},async loadMoreCourses(){this.hasMoreCourses&&!this.loadingMoreCourses&&!this.loadingCourses&&(this.coursesPage+=1,this.selectedCategory?await this.updateCourseOptionsByCategory({value:this.selectedCategory,label:this.inputFilters.category},!1):await this.loadCourseOptions(null,!1))},async handleCourseSearch(e){this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[];try{this.selectedCategory&&(this.selectedCategory=null);const t=await r0(this.offerId,e);t&&t.data&&Array.isArray(t.data)&&(this.courseOptions=t.data.map(s=>({value:s.courseid||s.id,label:s.fullname}))),this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso encontrado para a busca":"Nenhum curso encontrado"}catch{this.showErrorMessage("Erro ao buscar cursos."),this.courseOptions=[]}finally{this.loadingCourses=!1}},async updateCourseOptionsByCategory(e,t=!0){if(!(!this.offerId||!e)){this.loading=!0;try{t?(this.coursesPage=1,this.coursesTotalPages=1,this.hasMoreCourses=!1,this.loadingCourses=!0,this.courseOptions=[]):this.loadingMoreCourses=!0;let s=e.value;if(isNaN(parseInt(s))){const n=await Uo(e.label,this.offerId);if(n&&n.data&&n.data.length>0){const a=n.data.find(u=>u.name.toLowerCase()===e.label.toLowerCase());if(a)s=a.id;else{this.showErrorMessage("Erro ao identificar a categoria selecionada."),t?this.loadingCourses=!1:this.loadingMoreCourses=!1;return}}else{this.showErrorMessage("Erro ao buscar categorias."),t?this.loadingCourses=!1:this.loadingMoreCourses=!1;return}}const i=await rp(this.offerId,s);if(i&&i.data&&Array.isArray(i.data)){const n=i.data.map(a=>({value:a.id||a.courseid,label:a.fullname}));t?(this.courseOptions=n,this.selectedCourse=null,this.inputFilters.course="",this.appliedFilters.course=""):this.courseOptions=[...this.courseOptions,...n],this.hasMoreCourses=!1,this.courseNoResultsText=this.courseOptions.length===0?"Nenhum curso disponível nesta categoria":"Nenhum curso encontrado"}else t&&(this.courseOptions=[],this.selectedCourse=null,this.inputFilters.course="",this.appliedFilters.course=""),this.hasMoreCourses=!1,this.courseNoResultsText="Nenhum curso disponível nesta categoria"}catch{this.showErrorMessage("Erro ao carregar cursos da categoria."),t&&(this.courseOptions=[]),this.hasMoreCourses=!1}finally{t?this.loadingCourses=!1:this.loadingMoreCourses=!1,this.loading=!1}}},async handleCategorySelect(e){e&&(this.inputFilters.category=e.label,this.currentPage=1,await this.applyFilters(),await this.updateCourseOptionsByCategory(e,!0))},async handleCourseSelect(e){e&&(this.inputFilters.course=e,this.currentPage=1,this.selectedCategory=null,await this.applyFilters())},handleOnlyActiveChange(){this.appliedFilters.onlyActive=this.inputFilters.onlyActive,this.currentPage=1,this.loadCourses()},goBack(){this.router.push({name:"listar-ofertas"})},async searchCategories(){if(this.offerId)try{this.loading=!0;const e=await Uo(this.appliedFilters.category,this.offerId);if(e&&e.data&&e.data.length>0){let t=[];for(const s of e.data){const i=s.id;if(!i)continue;const n=await rp(this.offerId,i);if(n&&n.data){const a=n.data.map(u=>({id:u.id||u.courseid,offerCourseId:u.id,name:u.fullname,category:s.name||"-",turmasCount:Array.isArray(u.turmas)?u.turmas.length:0,status:u.status===1||u.status==="1"?"Ativo":"Inativo",can_delete:u.can_delete!==void 0?u.can_delete:!0,can_activate:u.can_activate!==void 0?u.can_activate:!0,turmas:Array.isArray(u.turmas)?u.turmas.map(c=>({id:c.id,nome:c.name,enrol_type:c.enrol_type||"-",vagas:c.max_users?c.max_users:"Ilimitado",inscritos:c.enrolled_users||0,dataInicio:c.start_date||"-",dataFim:c.end_date||"-"})):[]}));t=[...t,...a]}}this.selectedCourses=t}else this.selectedCourses=[]}catch{this.showErrorMessage("Erro ao buscar categorias. Por favor, tente novamente.")}finally{this.loading=!1}},showAddCourseModal(){this.showAddCourseModalVisible=!0},async handleAddCourseConfirm(e){try{this.loading=!0;for(const t of e)await Jw(this.offerId,t);await this.loadCourses(),await this.loadCategoryOptions(),await this.loadCourseOptions(),this.showSuccessMessage("Curso(s) adicionado(s) com sucesso à oferta.")}catch(t){this.showErrorMessage(t.message||"Ocorreu um erro ao adicionar os cursos.")}finally{this.loading=!1}},handleTableSort({sortBy:e,sortDesc:t}){this.sortBy=e,this.sortDesc=t,this.loadCourses()},handlePageChange(e){this.currentPage=e,this.loadCourses()},addTurma(e){this.selectedCourseForClass=e,this.showEnrolTypeModal=!0},handleEnrolTypeConfirm(e){this.showEnrolTypeModal=!1,this.router.push({name:"NewClass",params:{offercourseid:e.offercourseid,offerid:e.offerid||this.offerId||"0"},query:{enrol_type:e.enrolType}})},editTurma(e){const t=this.selectedCourses.find(s=>s.turmas&&s.turmas.some(i=>i.id===e.id));t?this.router.push({name:"EditClass",params:{offercourseid:t.offerCourseId,classid:e.id}}):this.showErrorMessage("Não foi possível editar a turma. Curso pai não encontrado.")},toggleClassStatus(e){this.selectedClass={...e,status:e.status||"Ativo"},this.showClassStatusModal=!0},async confirmToggleClassStatus(){if(this.selectedClass)try{this.loading=!0;const e=this.selectedClass.nome,t=this.selectedClass.status!=="Ativo";await p0(this.selectedClass.id,t);const s=this.selectedCourses.findIndex(i=>i.turmas.some(n=>n.id===this.selectedClass.id));if(s!==-1){const i=this.selectedCourses[s],n=i.turmas.findIndex(a=>a.id===this.selectedClass.id);n!==-1&&(i.turmas[n].status=t?"Ativo":"Inativo")}await this.loadCourses(),this.showSuccessMessage(t?`Turma "${e}" ativada com sucesso.`:`Turma "${e}" inativada com sucesso.`),this.selectedClass=null,this.showClassStatusModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao alterar status da turma.")}finally{this.loading=!1}},removeTurma(e,t){const s=e.turmas[t];s.can_delete&&(this.classToDelete=s,this.classParentCourse=e,this.showDeleteClassModal=!0)},viewRegisteredUsers(e){this.router.push({name:"usuarios-matriculados",params:{offerclassid:parseInt(e.id)}})},duplicateTurma(e,t){this.classToDuplicate=e,this.classToDuplicateParentCourse=t,this.showDuplicateClassModal=!0},async handleDuplicateSuccess(e){await this.loadCourses(),e.duplicatedCount?this.showSuccessMessage(`Turma "${e.turmaNome}" duplicada com sucesso para ${e.duplicatedCount} curso(s).`):this.showSuccessMessage(`Turma "${e.turmaNome}" duplicada com sucesso para o curso "${e.targetCourseName}".`)},async confirmDeleteClass(){if(!(!this.classToDelete||!this.classParentCourse))try{this.loading=!0;const e=this.classToDelete.nome;await u0(this.classToDelete.id);const t=this.classParentCourse.turmas.findIndex(s=>s.id===this.classToDelete.id);t!==-1&&(this.classParentCourse.turmas.splice(t,1),this.classParentCourse.turmasCount=this.classParentCourse.turmas.length),this.showSuccessMessage(`Turma ${e} excluída com sucesso.`),this.classToDelete=null,this.classParentCourse=null,this.showDeleteClassModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao excluir turma.")}finally{this.loading=!1}},updateTurmasCount(e){const t=this.selectedCourses.findIndex(s=>s.id===e);if(t!==-1){const s=this.selectedCourses[t];s.turmasCount=Array.isArray(s.turmas)?s.turmas.length:0,this.selectedCourses[t]={...s}}},async loadCourses(){if(this.offerId)try{this.loading=!0;const e={onlyActive:this.appliedFilters.onlyActive,page:this.currentPage,perPage:this.perPage,sortBy:this.sortBy,sortDesc:this.sortDesc};this.appliedFilters.course&&(e.courseIds=[this.appliedFilters.course.value]),this.appliedFilters.courseSearch&&(e.courseSearch=this.appliedFilters.course.label),this.appliedFilters.category&&(e.categorySearch=this.appliedFilters.category);const t=await fu(this.offerId,e);if(t&&t.data){let s=[],i=this.currentPage,n=1,a=0;t.data.courses&&({page:i,total_pages:n,total_items:a,courses:s}=t.data),this.currentPage=i||this.currentPage;const u=[];for(const c of s)try{const h=await a0(c.id);let m=[];h&&typeof h=="object"&&h.error===!1&&Array.isArray(h.data)&&h.data.length>0&&(m=h.data.map(p=>{let v=p.enrol_type||p.enrol||"-";return{id:p.id,nome:p.name,enrol_type:v,vagas:p.max_users?p.max_users:"Ilimitado",inscritos:p.enrolled_users||0,dataInicio:this.formatDate(p.startdate),dataFim:this.formatDate(p.enddate),status:p.status===0||p.status==="0"?"Inativo":"Ativo",can_activate:p.can_activate!==void 0?p.can_activate:!0,can_delete:p.can_delete!==void 0?p.can_delete:!0}})),u.push({id:c.courseid||c.id,offerCourseId:c.id,name:c.fullname,category:c.category_name||c.category||"-",turmasCount:m.length,status:c.status===1||c.status==="1"?"Ativo":"Inativo",can_delete:c.can_delete!==void 0?c.can_delete:!0,can_activate:c.can_activate!==void 0?c.can_activate:!0,turmas:m})}catch{u.push({id:c.courseid||c.id,offerCourseId:c.id,name:c.fullname,category:c.category_name||c.category||"-",turmasCount:0,status:c.status===1||c.status==="1"?"Ativo":"Inativo",can_delete:c.can_delete!==void 0?c.can_delete:!0,can_activate:c.can_activate!==void 0?c.can_activate:!0,turmas:[]})}this.selectedCourses=u,a!=null?this.totalItems=a:n>0?this.totalItems=n*this.perPage:this.totalItems=u.length,await this.loadCategoryOptions(),await this.loadCourseOptions()}else this.selectedCourses=[],this.totalItems=0,this.categoryOptions=[],this.courseOptions=[]}catch{this.showErrorMessage("Erro ao carregar cursos da oferta. Por favor, tente novamente."),this.selectedCourses=[],this.totalItems=0}finally{this.loading=!1}},async loadOffer(e){try{this.loading=!0,this.inputFilters={course:"",category:""},this.appliedFilters={course:"",category:""},this.selectedCategory=null,this.selectedCourse=null;const[t,s]=await Promise.all([Xh(e),tp("")]),i=Array.isArray(t)?t[0]:t;if(!i.error&&i.data){const n=i.data;if(this.offer={name:n.name,offerType:n.type,description:n.description,id:n.id,status:n.status},s&&Array.isArray(s.items)){const a=n.audiences||[];this.selectedAudiences=s.items.filter(u=>a.includes(u.id)).map(u=>({value:u.id,label:u.name.toUpperCase()}))}this.isEditing=!0,await this.loadCourses()}else throw new Error(i.message||"Erro ao carregar dados da oferta")}catch(t){this.showErrorMessage(t.message||"Erro ao carregar oferta.")}finally{this.loading=!1}},handleSelectAllAudiences(){const e=new Set(this.allAudiences.map(i=>i.value)),t=new Set(this.selectedAudiences.map(i=>i.value));let s=!0;for(const i of e)if(!t.has(i)){s=!1;break}s&&this.selectedAudiences.length===this.allAudiences.length?this.selectedAudiences=[]:this.selectedAudiences=[...this.allAudiences]},validate(){Object.keys(this.formErrors).forEach(t=>{this.formErrors[t].hasError=!1});let e=!1;return this.offer.name||(this.formErrors.name.hasError=!0,e=!0),this.selectedAudiences.length===0&&(this.formErrors.audiences.hasError=!0,e=!0),e&&this.showErrorMessage("Há campos obrigatórios a serem preenchidos."),!e},validateField(e){switch(e){case"name":this.formErrors.name.hasError=!this.offer.name;break;case"audiences":this.formErrors.audiences.hasError=this.selectedAudiences.length===0;break}return!this.formErrors[e].hasError},async saveOffer(){var e;if(this.loading=!0,!this.validate()){this.loading=!1;return}try{const t={name:this.offer.name,description:this.offer.description,type:this.offer.offerType,status:this.offer.status,audiences:this.selectedAudiences.map(i=>i.value)};let s;if(this.isEditing&&this.offerId)if(t.id=this.offerId,s=await ep(t),s&&!s.error)if(await this.updateOfferAudiences())this.showSuccessMessage("Oferta atualizada com sucesso!");else throw new Error("Oferta atualizada, mas houve falha ao atualizar públicos-alvo.");else{const i=(s==null?void 0:s.message)||(s==null?void 0:s.error)||"Falha ao atualizar oferta.";throw new Error(i)}else if(s=await ep(t),s&&s.data&&s.data.id){const i=s.data.id;this.offerId=i,this.offer.id=i,this.isEditing=!0,this.showSuccessMessage("Oferta salva com sucesso!");const n=`/edit-offer/${this.offerId}`;this.router.replace(n)}else{const i=(s==null?void 0:s.message)||((e=s==null?void 0:s[0])==null?void 0:e.message)||"Falha ao salvar a oferta.";throw new Error(i)}}catch(t){this.showErrorMessage(t.message||"Erro ao salvar oferta. Verifique os dados e tente novamente.")}finally{this.loading=!1}},async updateOfferAudiences(){if(!this.offerId)return!1;try{const e=this.selectedAudiences.map(s=>s.value),t=await t0(this.offerId,e);return!!(t&&!t.error)}catch{return this.showErrorMessage("Erro ao atualizar públicos-alvo."),!1}finally{}},toggleCourseStatus(e){e.can_activate&&(this.selectedCourse=e,this.showCourseStatusModal=!0)},getStatusButtonTitle(e){return e.status==="Ativo"?e.can_activate?"Inativar":"Não é possível inativar este curso":e.can_activate?"Ativar":"Não é possível ativar este curso"},async confirmToggleCourseStatus(){if(this.selectedCourse)try{this.loading=!0;const e=this.selectedCourse.status!=="Ativo",t=this.selectedCourse.name,s=this.selectedCourse.offerCourseId||this.selectedCourse.id;await e0(this.offerId,s,e);const i=this.selectedCourses.findIndex(n=>n.id===this.selectedCourse.id);i!==-1&&(this.selectedCourses[i].status=e?"Ativo":"Inativo"),this.showCourseStatusModal=!1,this.selectedCourse=null,await this.loadCourses(),await this.loadCategoryOptions(),await this.loadCourseOptions(),this.showSuccessMessage(e?`Curso "${t}" ativado com sucesso.`:`Curso "${t}" inativado com sucesso.`)}catch(e){this.showErrorMessage(e.message||"Erro ao alterar status do curso.")}finally{this.loading=!1}},deleteCourse(e){e.can_delete&&(this.courseToDelete=e,this.showDeleteCourseModal=!0)},async confirmDeleteCourse(){if(this.courseToDelete)try{this.loading=!0;const e=this.courseToDelete.name,t=this.courseToDelete.offerCourseId||this.courseToDelete.id;await Xw(this.offerId,t),this.selectedCourses=this.selectedCourses.filter(s=>s.id!==this.courseToDelete.id),await this.loadCourses(),await this.loadCategoryOptions(),await this.loadCourseOptions(),this.showSuccessMessage(`Curso "${e}" excluído com sucesso.`),this.courseToDelete=null,this.showDeleteCourseModal=!1}catch(e){this.showErrorMessage(e.message||"Erro ao remover curso.")}finally{this.loading=!1}},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})}},async created(){try{const t=await du();if(t&&t.data){const{enabled:s,types:i,default:n}=t.data;this.typeOptionsEnabled=s,s&&Array.isArray(i)&&(this.offerTypeOptions=i.map(a=>({value:a,label:a})),n&&!this.isEditing&&(this.offer.offerType=n))}}catch(t){this.showErrorMessage(t.message||"Erro ao carregar opções de tipos.")}const e=this.route.params.id;e&&(this.offerId=parseInt(e),this.loadOffer(this.offerId))},watch:{"inputFilters.course"(e,t){e.length===0&&t.length>0&&this.loadCourses()},"inputFilters.category"(e,t){e.length===0&&t.length>0&&this.loadCourses()},selectedCategory(e){e||(this.inputFilters.category="",this.appliedFilters.category&&(this.appliedFilters.category="",this.loadCourses()),this.loadCourseOptions())},selectedCourse(e){e||(this.inputFilters.course="",this.appliedFilters.course.id&&(this.appliedFilters.course.id="",this.loadCourses()))},currentPage(){this.loadCourses()},perPage(){this.currentPage=1,this.loadCourses()}}},DS={id:"new-offer-component",class:"new-offer"},xS={key:0,class:"alert alert-warning"},OS={class:"section-container"},SS={class:"form-row mb-3"},TS={class:"form-group"},NS={class:"input-container"},IS={key:0,class:"form-group"},AS={class:"input-container"},MS={class:"form-row mb-3",style:{"margin-bottom":"1.5rem"}},PS={class:"form-group"},kS={class:"label-container"},VS={class:"label-with-help"},RS={class:"input-container"},US={class:"form-group text-editor-container"},LS={class:"limited-width-editor"},FS={key:0,class:"section-title"},BS={key:1,class:"message-container"},$S={key:2},jS={class:"filters-left-group"},HS={class:"filters-right-group"},qS={class:"empty-state"},zS={class:"no-results"},WS=["title"],GS={class:"action-buttons"},KS=["onClick"],YS=["src"],QS=["onClick","disabled","title"],ZS=["onClick","disabled","title"],JS={class:"turmas-container"},XS={class:"turmas-content"},eT={key:0},tT={class:"turma-col"},sT=["title"],rT={class:"turma-col"},nT={class:"turma-col"},oT={class:"turma-col"},iT={class:"turma-col"},aT={class:"turma-col"},lT={class:"turma-col"},uT={class:"action-buttons"},cT=["onClick"],dT=["src"],fT=["onClick"],hT=["onClick"],pT=["title","onClick"],mT=["onClick","disabled","title"],gT={key:1,class:"empty-turmas"},vT={class:"d-flex justify-content-between align-items-center"},_T={class:"actions-container offer-actions"};function yT(e,t,s,i,n,a){var be,ue,Ge,vt,mt,ft,Oe,we,Lt,es,yt;const u=te("BackButton"),c=te("PageHeader"),h=te("CustomInput"),m=te("CustomSelect"),p=te("HelpIcon"),v=te("Autocomplete"),w=te("TextEditor"),D=te("FilterGroup"),V=te("CustomCheckbox"),F=te("FilterTag"),se=te("FilterTags"),A=te("FilterRow"),re=te("CollapsibleTable"),Y=te("Pagination"),ye=te("CustomButton"),Z=te("AddCourseModal"),fe=te("ConfirmationModal"),_e=te("DuplicateClassModal"),Ae=te("EnrolTypeModal"),ae=te("LFLoading"),I=te("Toast");return O(),S("div",DS,[k(c,{title:n.isEditing?`Editar oferta: ${n.offer.name}`:"Nova oferta"},{actions:Ne(()=>[k(u,{onClick:a.goBack},null,8,["onClick"])]),_:1},8,["title"]),n.showWarning?(O(),S("div",xS,t[21]||(t[21]=[f("i",{class:"fas fa-exclamation-triangle"},null,-1),Je(" Para que uma instância de oferta seja ativada e disponibilize os cursos para os públicos-alvo configurados, é necessário garantir que pelo menos um curso, um grupo de público-alvo, e uma turma estejam configurados à instância da oferta. ")]))):X("",!0),f("div",OS,[t[27]||(t[27]=f("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS",-1)),f("div",SS,[f("div",TS,[t[22]||(t[22]=f("div",{class:"label-container"},[f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Nome da Oferta"),f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})])],-1)),f("div",NS,[k(h,{modelValue:n.offer.name,"onUpdate:modelValue":t[0]||(t[0]=ce=>n.offer.name=ce),placeholder:"Oferta 0001",width:280,required:"","has-error":n.formErrors.name.hasError,"error-message":n.formErrors.name.message,onValidate:t[1]||(t[1]=ce=>a.validateField("name"))},null,8,["modelValue","has-error","error-message"])])]),n.typeOptionsEnabled?(O(),S("div",IS,[t[23]||(t[23]=f("div",{class:"label-container"},[f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Tipo da oferta")])],-1)),f("div",AS,[k(m,{modelValue:n.offer.offerType,"onUpdate:modelValue":t[2]||(t[2]=ce=>n.offer.offerType=ce),options:n.offerTypeOptions,width:280},null,8,["modelValue","options"])])])):X("",!0)]),f("div",MS,[f("div",PS,[f("div",kS,[f("div",VS,[t[24]||(t[24]=f("label",{class:"form-label"},"Público-alvo",-1)),t[25]||(t[25]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),k(p,{title:"Ajuda com público-alvo",text:"Selecione pelo menos um público-alvo para a oferta."})])]),f("div",RS,[k(v,{class:"autocomplete-audiences",modelValue:n.selectedAudiences,"onUpdate:modelValue":[t[3]||(t[3]=ce=>n.selectedAudiences=ce),t[4]||(t[4]=ce=>a.validateField("audiences"))],items:n.audienceTypeOptions,placeholder:"Pesquisar público-alvo...","input-max-width":218,required:!0,"show-all-option":!0,"has-error":n.formErrors.audiences.hasError,"error-message":n.formErrors.audiences.message,onSelectAll:a.handleSelectAllAudiences},null,8,["modelValue","items","has-error","error-message","onSelectAll"])])])]),f("div",US,[t[26]||(t[26]=f("div",{class:"label-container"},[f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Descrição da oferta")])],-1)),f("div",LS,[k(w,{modelValue:n.offer.description,"onUpdate:modelValue":t[5]||(t[5]=ce=>n.offer.description=ce),placeholder:"Digite a descrição da oferta aqui...",rows:5},null,8,["modelValue"])])])]),f("div",{class:he(["section-container",{"no-title-section":!n.isEditing}])},[n.isEditing?(O(),S("h2",FS,"CURSOS")):X("",!0),!a.canManageCourses||!n.isEditing?(O(),S("div",BS,t[28]||(t[28]=[f("div",{class:"lock-message"},[f("i",{class:"fas fa-lock lock-icon"}),f("span",null,"Salve a oferta primeiro para gerenciar os cursos")],-1)]))):X("",!0),n.isEditing&&a.canManageCourses?(O(),S("div",$S,[k(A,{inline:"",class:"courses-filter-row"},{default:Ne(()=>[f("div",jS,[k(D,null,{default:Ne(()=>[k(v,{modelValue:n.selectedCategory,"onUpdate:modelValue":t[6]||(t[6]=ce=>n.selectedCategory=ce),items:n.categoryOptions,placeholder:"Pesquisar...",label:"Categoria","input-max-width":218,"has-search-icon":!0,"auto-open":!1,"show-filter-tags":!1,"show-selected-in-input":!0,"no-results-text":n.categoryOptions.length===0?"Nenhuma categoria disponível":"Nenhuma categoria encontrada",onSelect:a.handleCategorySelect},null,8,["modelValue","items","no-results-text","onSelect"])]),_:1}),k(D,null,{default:Ne(()=>[k(v,{modelValue:n.selectedCourse,"onUpdate:modelValue":t[7]||(t[7]=ce=>n.selectedCourse=ce),items:n.courseOptions,placeholder:"Pesquisar...",label:"Curso","input-max-width":218,"has-search-icon":!0,"auto-open":!0,loading:n.loadingCourses||n.loadingMoreCourses,"no-results-text":n.courseNoResultsText,onSelect:a.handleCourseSelect,onLoadMore:a.loadMoreCourses,onSearch:a.handleCourseSearch,ref:"courseAutocomplete"},null,8,["modelValue","items","loading","no-results-text","onSelect","onLoadMore","onSearch"])]),_:1}),k(D,{isCheckbox:!0,class:"checkbox-filter-group"},{default:Ne(()=>[k(V,{modelValue:n.inputFilters.onlyActive,"onUpdate:modelValue":t[8]||(t[8]=ce=>n.inputFilters.onlyActive=ce),id:"onlyActive",label:"Não exibir inativos",onChange:a.handleOnlyActiveChange},null,8,["modelValue","onChange"])]),_:1}),n.appliedFilters.course?(O(),Rt(se,{key:0,class:"mt-3"},{default:Ne(()=>[k(F,{onRemove:t[9]||(t[9]=ce=>a.removeFilter("course"))},{default:Ne(()=>[Je(" Curso: "+q(n.appliedFilters.course.label),1)]),_:1})]),_:1})):X("",!0)]),f("div",HS,[f("button",{class:"btn btn-primary",onClick:t[10]||(t[10]=(...ce)=>a.showAddCourseModal&&a.showAddCourseModal(...ce))}," Adicionar curso ")])]),_:1}),k(re,{headers:n.courseTableHeaders,items:n.selectedCourses,"sort-by":n.sortBy,"sort-desc":n.sortDesc,onSort:a.handleTableSort,expandable:!0},{"empty-state":Ne(()=>[f("div",qS,[f("span",zS,q(n.loading?"Carregando registros...":"Não existem registros"),1)])]),"item-name":Ne(({item:ce})=>[f("span",{title:ce.name},q(ce.name.length>50?ce.name.slice(0,50)+"...":ce.name),9,WS)]),"item-actions":Ne(({item:ce})=>[f("div",GS,[f("button",{class:"btn-action btn-add",onClick:ze=>a.addTurma(ce),title:"Adicionar turma"},[f("img",{src:n.icons.plus,alt:"Adicionar turma"},null,8,YS)],8,KS),f("button",{class:he(["btn-action",ce.status==="Ativo"?"btn-deactivate":"btn-activate"]),onClick:ze=>a.toggleCourseStatus(ce),disabled:ce.status==="Inativo"&&!ce.can_activate||!ce.can_activate,title:a.getStatusButtonTitle(ce)},[f("i",{class:he(ce.status==="Ativo"?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,QS),f("button",{class:"btn-action btn-delete",onClick:ze=>a.deleteCourse(ce),disabled:!ce.can_delete,title:ce.can_delete?"Excluir":"Não é possível excluir este curso"},t[29]||(t[29]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,ZS)])]),"expanded-content":Ne(({item:ce})=>[f("div",JS,[t[34]||(t[34]=f("div",{class:"turmas-header"},[f("div",{class:"turma-col"},"NOME DA TURMA"),f("div",{class:"turma-col"},"TIPO DE INSCRIÇÃO"),f("div",{class:"turma-col"},"QTD. DE VAGAS"),f("div",{class:"turma-col"},"QTD. DE ALUNOS INSCRITOS"),f("div",{class:"turma-col"},"DATA INÍCIO DA TURMA"),f("div",{class:"turma-col"},"DATA FIM DA TURMA"),f("div",{class:"turma-col"},"AÇÕES")],-1)),f("div",XS,[ce.turmas&&ce.turmas.length>0?(O(),S("div",eT,[(O(!0),S(Ie,null,at(ce.turmas,(ze,hs)=>(O(),S("div",{class:"turmas-row",key:hs},[f("div",tT,[f("span",{title:ze.nome},q(ze.nome.length>20?ze.nome.slice(0,20)+"...":ze.nome),9,sT)]),f("div",rT,q(a.getEnrolTypeLabel(ze.enrol_type)),1),f("div",nT,q(ze.vagas),1),f("div",oT,q(ze.inscritos),1),f("div",iT,q(ze.dataInicio),1),f("div",aT,q(ze.dataFim),1),f("div",lT,[f("div",uT,[f("button",{class:"btn-action btn-users",onClick:zt=>a.viewRegisteredUsers(ze),title:"Usuários Matriculados"},[f("img",{src:n.icons.users,alt:"Usuários Matriculados"},null,8,dT)],8,cT),f("button",{class:"btn-action btn-edit",onClick:zt=>a.editTurma(ze),title:"Editar"},t[30]||(t[30]=[f("i",{class:"fas fa-pencil-alt"},null,-1)]),8,fT),f("button",{class:"btn-action btn-duplicate",onClick:zt=>a.duplicateTurma(ze,ce),title:"Duplicar Turma"},t[31]||(t[31]=[f("i",{class:"fas fa-copy"},null,-1)]),8,hT),f("button",{class:he(["btn-action",ze.status==="Ativo"?"btn-deactivate":"btn-activate"]),title:ze.status==="Ativo"?"Inativar":"Ativar",onClick:zt=>a.toggleClassStatus(ze)},[f("i",{class:he(ze.status==="Ativo"?"fas fa-eye":"fas fa-eye-slash")},null,2)],10,pT),f("button",{class:"btn-action btn-delete",onClick:zt=>a.removeTurma(ce,hs),disabled:!ze.can_delete,title:ze.can_delete?"Excluir":"Não é possível excluir esta turma"},t[32]||(t[32]=[f("i",{class:"fa fa-trash fa-fw"},null,-1)]),8,mT)])])]))),128))])):(O(),S("div",gT,t[33]||(t[33]=[f("span",null,"Nenhuma turma encontrada para este curso",-1)])))])])]),_:1},8,["headers","items","sort-by","sort-desc","onSort"]),k(Y,{ref:"pagination","current-page":n.currentPage,"onUpdate:currentPage":[t[11]||(t[11]=ce=>n.currentPage=ce),a.handlePageChange],"per-page":n.perPage,"onUpdate:perPage":t[12]||(t[12]=ce=>n.perPage=ce),total:n.totalItems},null,8,["current-page","per-page","total","onUpdate:currentPage"])])):X("",!0)],2),t[36]||(t[36]=f("hr",null,null,-1)),f("div",vT,[t[35]||(t[35]=f("div",{class:"required-fields-message"},[f("div",{class:"form-info"},[Je(" Este formulário contém campos obrigatórios marcados com "),f("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),f("div",_T,[k(ye,{variant:"primary",label:"Salvar",onClick:a.saveOffer},null,8,["onClick"]),k(ye,{variant:"secondary",label:"Cancelar",onClick:a.goBack},null,8,["onClick"])])]),k(Z,{modelValue:n.showAddCourseModalVisible,"onUpdate:modelValue":t[13]||(t[13]=ce=>n.showAddCourseModalVisible=ce),"offer-id":n.offerId,onConfirm:a.handleAddCourseConfirm},null,8,["modelValue","offer-id","onConfirm"]),k(fe,{show:n.showCourseStatusModal,title:((be=n.selectedCourse)==null?void 0:be.status)==="Ativo"?"Ao inativar este curso da oferta, o curso e as turmas associadas serão tratados da seguinte forma:":"Confirmar Ativação",message:((ue=n.selectedCourse)==null?void 0:ue.status)==="Ativo"?"":"Tem certeza que deseja ativar este curso?","list-items":((Ge=n.selectedCourse)==null?void 0:Ge.status)==="Ativo"?["O curso não será mais disponibilizados na oferta, mas as turmas e matrículas permanecerão ativas.","Alunos já inscritos continuarão tendo acesso normalmente até o encerramento da turma.","Novos alunos não poderão ser inscritos através da oferta."]:[],"confirm-button-text":((vt=n.selectedCourse)==null?void 0:vt.status)==="Ativo"?"Inativar curso":"Ativar","cancel-button-text":"Cancelar",icon:((mt=n.selectedCourse)==null?void 0:mt.status)==="Ativo"?"warning":"question",onClose:t[14]||(t[14]=ce=>n.showCourseStatusModal=!1),onConfirm:a.confirmToggleCourseStatus},null,8,["show","title","message","list-items","confirm-button-text","icon","onConfirm"]),k(fe,{show:n.showDeleteCourseModal,title:"A exclusão deste curso da instância de oferta é uma ação irreversível",message:"Ele será desassociado e as turmas relacionadas serão removidas. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir curso","cancel-button-text":"Cancelar",icon:"warning",onClose:t[15]||(t[15]=ce=>n.showDeleteCourseModal=!1),onConfirm:a.confirmDeleteCourse},null,8,["show","onConfirm"]),k(fe,{show:n.showDeleteClassModal,title:"A exclusão desta turma é uma ação irreversível",message:"Todas as configurações realizadas serão excluídas e a turma será removida do curso. Tem certeza de que deseja continuar?","confirm-button-text":"Excluir Turma","cancel-button-text":"Cancelar",icon:"warning",onClose:t[16]||(t[16]=ce=>n.showDeleteClassModal=!1),onConfirm:a.confirmDeleteClass},null,8,["show","onConfirm"]),k(fe,{show:n.showClassStatusModal,title:((ft=n.selectedClass)==null?void 0:ft.status)==="Ativo"?"Ao inativar esta turma, as matrículas e o curso associados serão tratados da seguinte forma:":"Confirmar Ativação",message:((Oe=n.selectedClass)==null?void 0:Oe.status)==="Ativo"?"":"Tem certeza que deseja ativar esta turma?","list-items":((we=n.selectedClass)==null?void 0:we.status)==="Ativo"?["Se o curso não possuir outra turma disponível, ele não será mais disponibilizado para novos usuários da oferta. No entanto, matrículas já realizadas permanecerão ativas.","Usuários já matriculados manterão o acesso ao curso normalmente até o encerramento da turma ou da sua matrícula.","Novos alunos não poderão ser matriculados através da oferta."]:[],"confirm-button-text":((Lt=n.selectedClass)==null?void 0:Lt.status)==="Ativo"?"Inativar Turma":"Ativar","cancel-button-text":"Cancelar",icon:((es=n.selectedClass)==null?void 0:es.status)==="Ativo"?"warning":"question",onClose:t[17]||(t[17]=ce=>n.showClassStatusModal=!1),onConfirm:a.confirmToggleClassStatus},null,8,["show","title","message","list-items","confirm-button-text","icon","onConfirm"]),k(_e,{show:n.showDuplicateClassModal,turma:n.classToDuplicate,parentCourse:n.classToDuplicateParentCourse,offerId:n.offerId,onClose:t[18]||(t[18]=ce=>n.showDuplicateClassModal=!1),onSuccess:a.handleDuplicateSuccess,onLoading:t[19]||(t[19]=ce=>n.loading=ce),onError:a.showErrorMessage},null,8,["show","turma","parentCourse","offerId","onSuccess","onError"]),k(Ae,{show:n.showEnrolTypeModal,offercourseid:(yt=n.selectedCourseForClass)==null?void 0:yt.offerCourseId,offerid:n.offerId||"0",onClose:t[20]||(t[20]=ce=>n.showEnrolTypeModal=!1),onConfirm:a.handleEnrolTypeConfirm},null,8,["show","offercourseid","offerid","onConfirm"]),k(ae,{"is-loading":n.loading},null,8,["is-loading"]),k(I,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])])}const lp=He(CS,[["render",yT],["__scopeId","data-v-0341c5c8"]]),s3="",bT={name:"NewClassView",components:{CustomInput:Lo,CustomSelect:mr,CustomButton:Fn,PageHeader:sa,BackButton:_u,Autocomplete:$o,TextEditor:ap,CustomCheckbox:Xi,FilterRow:ea,FilterGroup:ta,Toast:Fo,HelpIcon:yu,FilterTag:Bo,FilterTags:ra},setup(){const e=Zi(),t=Zh();return{router:e,route:t}},directives:{tooltip:{mounted(e,t){e.setAttribute("title",t.value)},updated(e,t){e.setAttribute("title",t.value)}}},props:{offercourseid:{type:Number,required:!0},classid:{type:Number,required:!1,default:null}},data(){return{loading:!1,showToast:!1,toastMessage:"",toastType:"success",toastTimeout:null,isEditing:!1,enrolmentMethods:[],classData:{enrol:"",offercourseid:null,classname:"",startdate:"",teachers:[],optional_fields:{enableenddate:!1,enddate:"",enablepreenrolment:!1,preenrolmentstartdate:"",preenrolmentenddate:"",description:"",enableenrolperiod:!1,enrolperiod:null,minusers:null,maxusers:null,roleid:null,enablereenrol:!1,reenrolmentsituations:[],enableextension:!1,extensionperiod:null,extensiondaysavailable:null,extensionmaxrequests:null,extensionallowedsituations:[]}},selectedTeachers:[],teacherSearchTerm:"",debounceTimer:null,teacherList:[],showTeacherDropdown:!1,highlightedIndex:-1,extensionSituations:[],reenrolSituations:[],roleOptions:[],situationList:[],offerCourse:null,formErrors:{enrol:{hasError:!1,message:"Método de inscrição é obrigatório"},classname:{hasError:!1,message:"Nome da turma é obrigatório"},startdate:{hasError:!1,message:"Data de início é obrigatória"},roleid:{hasError:!1,message:"Papel padrão é obrigatório"},enddate:{hasError:!1,message:"Data fim da turma é obrigatória quando habilitada"},preenrolmentstartdate:{hasError:!1,message:"Data início de pré-inscrição é obrigatória quando habilitada"},preenrolmentenddate:{hasError:!1,message:"Data fim de pré-inscrição é obrigatória quando habilitada"},enrolperiod:{hasError:!1,message:"Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma"},extensionperiod:{hasError:!1,message:"Dias para prorrogação é obrigatório quando habilitada"},extensiondaysavailable:{hasError:!1,message:"Dias antes do término é obrigatório quando habilitada"},extensionmaxrequests:{hasError:!1,message:"Máximo de solicitações é obrigatório quando habilitada"},extensionsituations:{hasError:!1,message:"É necessário selecionar pelo menos uma situação de matrícula para prorrogação"},minusers:{hasError:!1,message:"Mínimo de usuários deve ser maior ou igual a zero"},maxusers:{hasError:!1,message:"Máximo de usuários deve ser maior ou igual a zero"}},validationAlert:{show:!1,message:"Há campos obrigatórios a serem preenchidos. Por favor, verifique os campos destacados."}}},async created(){if(!this.offercourseid)throw new Error("offercourseid não foi definido.");this.classid?this.isEditing=!0:this.route.query.classid&&this.route.query.edit==="true"&&(this.isEditing=!0,this.$nextTick(()=>{this.router.replace({name:"EditClass",params:{offercourseid:this.offercourseid,classid:this.classid}})})),await this.loadInitialData(),this.isEditing&&this.classid&&(await this.loadClassData(),this.$nextTick(()=>{this.restartComponent()}))},mounted(){window.scrollTo(0,0),document.addEventListener("click",this.handleClickOutside)},beforeUnmount(){document.removeEventListener("click",this.handleClickOutside),this.debounceTimer&&clearTimeout(this.debounceTimer)},computed:{extensionSituationList(){let e=[0,1];return this.situationList.filter(t=>e.includes(t.value))},reenrolSituationList(){let e=[6,7,8,4,5];return this.situationList.filter(t=>e.includes(t.value))},maxEnrolPeriod(){if(this.classData.startdate&&this.classData.optional_fields.enddate&&this.classData.optional_fields.enableenddate){const e=this.calculateDaysDifference(this.classData.startdate,this.classData.optional_fields.enddate);return e>=1?e:1}return null},isOneDayClass(){return this.classData.startdate&&this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&this.classData.startdate===this.classData.optional_fields.enddate},shouldDisableEnrolPeriod(){return this.isOneDayClass}},watch:{extensionSituations:{handler(e){this.classData.optional_fields.extensionallowedsituations=e.map(t=>t.value)},deep:!0},reenrolSituations:{handler(e){this.classData.optional_fields.reenrolmentsituations=e.map(t=>t.value)},deep:!0},"classData.optional_fields.enableenrolperiod":function(e){!e&&this.classData.optional_fields.enableextension&&(this.classData.optional_fields.enableextension=!1,this.showWarningMessage("Prorrogação de matrícula foi desabilitada automaticamente porque o Prazo de conclusão da turma foi desabilitado.")),this.validateField("extensionsituations")},"classData.startdate":function(){this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&this.validateField("enddate"),this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod"),this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"classData.optional_fields.enddate":function(){this.classData.optional_fields.enableenddate&&this.validateField("enddate"),this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod"),this.classData.optional_fields.enablepreenrolment&&this.classData.optional_fields.enableenddate&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"classData.optional_fields.enableenddate":function(e){e&&this.classData.optional_fields.enddate&&this.validateField("enddate"),!e&&this.classData.optional_fields.enableenrolperiod&&this.classData.optional_fields.enrolperiod&&this.validateField("enrolperiod"),this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates(),this.checkAndDisableEnrolPeriodForOneDayClass()},"classData.optional_fields.enrolperiod":function(e){this.classData.optional_fields.enableenrolperiod&&e&&this.validateField("enrolperiod")},"classData.optional_fields.preenrolmentstartdate":function(){this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()},"classData.optional_fields.preenrolmentenddate":function(){this.classData.optional_fields.enablepreenrolment&&this.validatePreenrolmentDates()},"classData.optional_fields.minusers":function(){this.validateField("minusers"),this.classData.optional_fields.maxusers!==null&&this.classData.optional_fields.maxusers!==void 0&&this.validateField("maxusers")},"classData.optional_fields.maxusers":function(){this.validateField("maxusers"),this.classData.optional_fields.minusers!==null&&this.classData.optional_fields.minusers!==void 0&&this.validateField("minusers")}},methods:{calculateDaysDifference(e,t){if(!e||!t)return 0;const s=new Date(e),i=new Date(t);if(isNaN(s.getTime())||isNaN(i.getTime()))return 0;if(s.getTime()===i.getTime())return 1;const n=Math.abs(i-s);return Math.ceil(n/(1e3*60*60*24))},checkAndDisableEnrolPeriodForOneDayClass(){this.isOneDayClass&&this.classData.optional_fields.enableenrolperiod&&(this.classData.optional_fields.enableenrolperiod=!1,this.classData.optional_fields.enrolperiod=null,this.classData.optional_fields.enableextension&&(this.classData.optional_fields.enableextension=!1,this.classData.optional_fields.extensionperiod=null,this.classData.optional_fields.extensiondaysavailable=null,this.classData.optional_fields.extensionmaxrequests=null,this.classData.optional_fields.extensionallowedsituations=[],this.extensionSituations=[]),this.showWarningMessage("Prazo de conclusão foi desabilitado automaticamente porque a turma tem duração de apenas um dia (data início = data fim)."))},async loadInitialData(){try{this.loading=!0,this.isEditing||(this.classData.offercourseid=this.offercourseid),await this.loadOfferCourse(),await this.loadRoles(),await this.loadSituations(),await this.loadEnrolmentMethods()}catch{this.showErrorMessage("Alguns dados não puderam ser carregados.")}finally{this.loading=!1}},async loadOfferCourse(){try{const e=await i0(this.offercourseid);this.offerCourse=e==null?void 0:e.data}catch{this.showErrorMessage("Erro ao carregar informações do curso da oferta.")}},async loadRoles(){const e=await pu(this.offercourseid);if(e.data&&(this.roleOptions=e.data.map(t=>({value:t.id,label:t.name})),!this.classData.optional_fields.roleid)){const t=this.roleOptions.find(s=>s.value===5);this.classData.optional_fields.roleid=(t==null?void 0:t.value)??this.roleOptions[0].value}},async loadSituations(){const e=await d0();e.data&&(this.situationList=e.data.map(t=>({value:t.id,label:t.name})))},async loadEnrolmentMethods(){const e=this.route.query.enrol_type;!this.isEditing&&e&&(this.classData.enrol=e);const t=await np(!0);t&&Array.isArray(t)&&(this.enrolmentMethods=t.map(s=>({value:s.enrol,label:s.name})))},validate(){Object.keys(this.formErrors).forEach(n=>{this.formErrors[n].hasError=!1}),this.validationAlert.show=!1;let e=!1;this.classData.classname||(this.formErrors.classname.hasError=!0,e=!0),this.classData.startdate?this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&new Date(this.classData.startdate)>new Date(this.classData.optional_fields.enddate)?(this.formErrors.startdate.message="Data de início deve ser igual ou anterior à data fim da turma",this.formErrors.startdate.hasError=!0,e=!0):this.formErrors.startdate.hasError=!1:(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!0,e=!0),this.classData.optional_fields.roleid||(this.formErrors.roleid.hasError=!0,e=!0),this.classData.optional_fields.enableenddate&&(this.classData.optional_fields.enddate?this.classData.startdate&&new Date(this.classData.optional_fields.enddate)<new Date(this.classData.startdate)?(this.formErrors.enddate.message="Data fim da turma deve ser igual ou posterior à data de início",this.formErrors.enddate.hasError=!0,e=!0):this.formErrors.enddate.hasError=!1:(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!0,e=!0)),this.classData.optional_fields.enablepreenrolment&&(this.classData.optional_fields.preenrolmentstartdate||(this.formErrors.preenrolmentstartdate.hasError=!0,e=!0),this.classData.optional_fields.preenrolmentenddate||(this.formErrors.preenrolmentenddate.hasError=!0,e=!0)),this.validatePreenrolmentDates()||(e=!0),this.classData.optional_fields.enableenrolperiod&&(this.classData.optional_fields.enrolperiod?this.maxEnrolPeriod!==null&&parseInt(this.classData.optional_fields.enrolperiod)>this.maxEnrolPeriod&&(this.formErrors.enrolperiod.message=`Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`,this.formErrors.enrolperiod.hasError=!0,e=!0):(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado",this.formErrors.enrolperiod.hasError=!0,e=!0)),this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&(this.classData.optional_fields.extensionperiod||(this.formErrors.extensionperiod.hasError=!0,e=!0),this.classData.optional_fields.extensiondaysavailable||(this.formErrors.extensiondaysavailable.hasError=!0,e=!0),this.classData.optional_fields.extensionmaxrequests||(this.formErrors.extensionmaxrequests.hasError=!0,e=!0),(!this.extensionSituations||this.extensionSituations.length===0)&&(this.formErrors.extensionsituations.hasError=!0,e=!0));const s=this.validateField("minusers"),i=this.validateField("maxusers");return(!s||!i)&&(e=!0),e&&(this.validationAlert.show=!0,this.showErrorMessage(this.validationAlert.message),window.scrollTo({top:0,behavior:"smooth"})),!e},validateField(e){switch(e){case"enrol":this.formErrors.enrol.hasError=!1;break;case"classname":this.formErrors.classname.hasError=!this.classData.classname;break;case"startdate":const s=this.classData.startdate,i=s&&this.classData.optional_fields.enableenddate&&this.classData.optional_fields.enddate&&new Date(this.classData.startdate)>new Date(this.classData.optional_fields.enddate);s?i?(this.formErrors.startdate.message="Data de início deve ser igual ou anterior à data fim da turma",this.formErrors.startdate.hasError=!0):(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!1):(this.formErrors.startdate.message="Data de início é obrigatória",this.formErrors.startdate.hasError=!0);break;case"roleid":this.formErrors.roleid.hasError=!this.classData.optional_fields.roleid;break;case"enddate":const n=this.classData.optional_fields.enableenddate,a=this.classData.optional_fields.enddate,u=n&&a&&this.classData.startdate&&new Date(this.classData.optional_fields.enddate)<new Date(this.classData.startdate);n&&!a?(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!0):u?(this.formErrors.enddate.message="Data fim da turma deve ser igual ou posterior à data de início",this.formErrors.enddate.hasError=!0):(this.formErrors.enddate.message="Data fim da turma é obrigatória quando habilitada",this.formErrors.enddate.hasError=!1);break;case"preenrolmentstartdate":this.formErrors.preenrolmentstartdate.hasError=this.classData.optional_fields.enablepreenrolment&&!this.classData.optional_fields.preenrolmentstartdate,this.validatePreenrolmentDates();break;case"preenrolmentenddate":this.formErrors.preenrolmentenddate.hasError=this.classData.optional_fields.enablepreenrolment&&!this.classData.optional_fields.preenrolmentenddate,this.validatePreenrolmentDates();break;case"enrolperiod":const c=this.classData.optional_fields.enableenrolperiod,h=this.classData.optional_fields.enrolperiod!==null&&this.classData.optional_fields.enrolperiod!==void 0&&this.classData.optional_fields.enrolperiod!=="",m=this.maxEnrolPeriod!==null&&h&&parseInt(this.classData.optional_fields.enrolperiod)>this.maxEnrolPeriod;c&&!h?(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado",this.formErrors.enrolperiod.hasError=!0):c&&m?(this.formErrors.enrolperiod.message=`Prazo de conclusão não pode exceder ${this.maxEnrolPeriod} dias (período entre as datas de início e fim da turma)`,this.formErrors.enrolperiod.hasError=!0):(this.formErrors.enrolperiod.message="Prazo de conclusão é obrigatório quando habilitado e não pode exceder o período entre as datas de início e fim da turma",this.formErrors.enrolperiod.hasError=!1);break;case"extensionperiod":this.formErrors.extensionperiod.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensionperiod;break;case"extensiondaysavailable":this.formErrors.extensiondaysavailable.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensiondaysavailable;break;case"extensionmaxrequests":this.formErrors.extensionmaxrequests.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&!this.classData.optional_fields.extensionmaxrequests;break;case"extensionsituations":this.formErrors.extensionsituations.hasError=this.classData.optional_fields.enableextension&&this.classData.optional_fields.enableenrolperiod&&(!this.extensionSituations||this.extensionSituations.length===0);break;case"minusers":this.validateMinUsers();break;case"maxusers":this.validateMaxUsers();break}const t=Object.values(this.formErrors).some(s=>s.hasError);return this.validationAlert.show=t,!this.formErrors[e].hasError},validatePreenrolmentDates(){let e=!0;if(this.formErrors.preenrolmentstartdate.hasError=!1,this.formErrors.preenrolmentenddate.hasError=!1,this.classData.optional_fields.enablepreenrolment){const t=this.classData.startdate,s=this.classData.optional_fields.enableenddate?this.classData.optional_fields.enddate:null,i=this.classData.optional_fields.preenrolmentstartdate,n=this.classData.optional_fields.preenrolmentenddate,a=this.offerCourse.startdate,u=this.offerCourse.enddate;i||(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início de pré-inscrição é obrigatória",e=!1),n||(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim de pré-inscrição é obrigatória",e=!1),new Date(n)<new Date(i)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser posterior à data início",e=!1),new Date(i)>new Date(t)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início deve ser igual ou anterior à data início da turma",e=!1),new Date(i)<new Date(a)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data início deve ser igual ou posterior à data início do curso",e=!1),u&&new Date(i)>new Date(u)&&(this.formErrors.preenrolmentstartdate.hasError=!0,this.formErrors.preenrolmentstartdate.message="Data de início deve ser igual ou anterior à data fim do curso",e=!1),s&&new Date(n)>=new Date(s)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser anterior à data fim da turma",e=!1),u&&new Date(n)>new Date(u)&&(this.formErrors.preenrolmentenddate.hasError=!0,this.formErrors.preenrolmentenddate.message="Data fim deve ser igual ou anterior à data fim do curso",e=!1)}return e},validateMinUsers(){const e=this.classData.optional_fields.minusers,t=this.classData.optional_fields.maxusers,s=parseInt(e),i=parseInt(t);return this.formErrors.minusers.hasError=!1,s===0?!0:i>0&&s>i?(this.formErrors.minusers.message="Mínimo de usuários inscritos deve ser menor que o máximo de usuários inscritos",this.formErrors.minusers.hasError=!0,!1):!0},validateMaxUsers(){const e=this.classData.optional_fields.minusers,t=this.classData.optional_fields.maxusers,s=parseInt(t),i=parseInt(e);return this.formErrors.maxusers.hasError=!1,s===0?!0:i>0&&s<i?(this.formErrors.maxusers.message="Máximo de usuários inscritos deve ser maior que o mínimo de usuários inscritos",this.formErrors.maxusers.hasError=!0,!1):!0},async loadClassData(){this.loading=!0;const e=await hu(this.classid);if(e.error==!0)throw new Error(e.exception);this.classData=e.data,e.data.optional_fields&&this.processOptionalFields(e.data.optional_fields),e.data.teachers&&Array.isArray(e.data.teachers)&&(this.selectedTeachers=e.data.teachers),this.updateUIAfterLoading(),document.title=`Editar Turma: ${this.classData.classname}`,this.loading=!1},processOptionalFields(e){this.processDateFields(e),this.processEnrolmentFields(e),this.processUserLimits(e),this.processDescriptionAndRole(e),this.processReenrolment(e),this.processExtensionFields(e)},processDateFields(e){e.enableenddate&&(this.classData.optional_fields.enableenddate=!0,this.classData.optional_fields.enddate=e.enddate||null),e.enablepreenrolment&&(this.classData.optional_fields.enablepreenrolment=!0,this.classData.optional_fields.preenrolmentstartdate=e.preenrolmentstartdate||null,this.classData.optional_fields.preenrolmentenddate=e.preenrolmentenddate||null)},processEnrolmentFields(e){e.enableenrolperiod?(this.classData.optional_fields.enableenrolperiod=!0,this.classData.optional_fields.enrolperiod=e.enrolperiod>0?e.enrolperiod:null):this.classData.optional_fields.enrolperiod=null},processUserLimits(e){this.classData.optional_fields.minusers=e.minusers>0?e.minusers:null,this.classData.optional_fields.maxusers=e.maxusers>0?e.maxusers:null},processDescriptionAndRole(e){this.classData.optional_fields.roleid=e.roleid||null,this.classData.optional_fields.description=e.description||""},processReenrolment(e){e.enablereenrol?(this.classData.optional_fields.enablereenrol=!0,this.classData.optional_fields.reenrolmentsituations=e.reenrolmentsituations||[],Array.isArray(e.reenrolmentsituations)&&(this.reenrolSituations=e.reenrolmentsituations.map(t=>this.situationList.find(s=>s.value===parseInt(t))))):this.classData.optional_fields.reenrolmentsituations=[]},processExtensionFields(e){e.enableextension&&e.enableenrolperiod?(this.classData.optional_fields.enableextension=!0,this.processExtensionPeriods(e),this.processExtensionSituations(e)):this.resetExtensionFields()},processExtensionPeriods(e){this.classData.optional_fields.extensionperiod=e.extensionperiod>0?e.extensionperiod:null,this.classData.optional_fields.extensiondaysavailable=e.extensiondaysavailable>0?e.extensiondaysavailable:null,this.classData.optional_fields.extensionmaxrequests=e.extensionmaxrequests>0?e.extensionmaxrequests:null},processExtensionSituations(e){Array.isArray(e.extensionallowedsituations)&&(this.extensionSituations=e.extensionallowedsituations.map(t=>this.situationList.find(s=>s.value===t)))},resetExtensionFields(){this.classData.optional_fields.extensionperiod=null,this.classData.optional_fields.extensiondaysavailable=null,this.classData.optional_fields.extensionmaxrequests=null,this.classData.optional_fields.extensionallowedsituations=[],this.extensionSituations=[]},handleTeacherInput(){const e=this.teacherSearchTerm.trim();this.debounceTimer&&clearTimeout(this.debounceTimer),e.length>=3?(this.showTeacherDropdown=!0,this.highlightedIndex=-1,this.debounceTimer=setTimeout(async()=>{await this.fetchPotentialTeachers(e)},500)):(this.showTeacherDropdown=!1,this.teacherList=[])},async fetchPotentialTeachers(e){let t=this.selectedTeachers.map(i=>i.id||i.value)??[];const s=await c0(this.offercourseid,this.classid,e,t);this.teacherList=!s.error&&s.data?s.data:[]},removeTeacher(e){this.selectedTeachers=this.selectedTeachers.filter(t=>t.id!==e)},handleTeacherInputFocus(){this.teacherSearchTerm.length>=3&&this.teacherList.length>0&&(this.showTeacherDropdown=!0)},selectTeacher(e){this.selectedTeachers.push({id:e.id,value:e.id,fullname:e.fullname,email:e.email}),this.teacherSearchTerm="",this.showTeacherDropdown=!1,this.highlightedIndex=-1,this.teacherList=[],this.$nextTick(()=>{this.$refs.teacherSearchInput&&this.$refs.teacherSearchInput.focus()})},handleKeydown(e){if(!(!this.showTeacherDropdown||this.teacherList.length===0))switch(e.key){case"ArrowDown":e.preventDefault(),this.highlightedIndex=Math.min(this.highlightedIndex+1,this.teacherList.length-1);break;case"ArrowUp":e.preventDefault(),this.highlightedIndex=Math.max(this.highlightedIndex-1,0);break;case"Enter":e.preventDefault(),this.highlightedIndex>=0&&this.highlightedIndex<this.teacherList.length&&this.selectTeacher(this.teacherList[this.highlightedIndex]);break;case"Escape":e.preventDefault(),this.showTeacherDropdown=!1,this.highlightedIndex=-1;break}},handleClickOutside(e){this.$refs.teacherSearchContainer&&!this.$refs.teacherSearchContainer.contains(e.target)&&(this.showTeacherDropdown=!1,this.highlightedIndex=-1)},updateUIAfterLoading(){this.$nextTick(()=>{this.updateFormFields(),this.$forceUpdate(),(!this.classData.classname||!this.classData.enrol)&&this.showErrorMessage("Dados incompletos após carregamento.")})},updateFormFields(){this.updateSelectField("enrolSelect",this.classData.enrol),this.updateInputField("classnameInput",this.classData.classname),this.updateInputField("startdateInput",this.classData.startdate)},updateSelectField(e,t){if(this.$refs[e]){this.$refs[e].value=t;const s=new Event("change");this.$refs[e].$el.dispatchEvent(s)}},updateInputField(e,t){if(this.$refs[e]&&t){this.$refs[e].value=t;const s=new Event("input");this.$refs[e].$el.dispatchEvent(s)}},async saveClass(){if(!this.validate())return;this.loading=!0;const e=JSON.parse(JSON.stringify(this.classData));e.teachers=this.selectedTeachers.map(n=>n.id),!e.optional_fields.enableextension||!e.optional_fields.enableenrolperiod?(e.optional_fields.extensionperiod=void 0,e.optional_fields.extensiondaysavailable=void 0,e.optional_fields.extensionmaxrequests=void 0,e.optional_fields.extensionallowedsituations=[],e.optional_fields.enableenrolperiod||(e.optional_fields.enableextension=!1)):e.optional_fields.extensionallowedsituations=this.extensionSituations.map(n=>n.value),e.optional_fields.enableenrolperiod||(e.optional_fields.enrolperiod=void 0),e.optional_fields.enablereenrol?e.optional_fields.reenrolmentsituations=this.reenrolSituations.map(n=>n.value):e.optional_fields.reenrolmentsituations=[],["enrolperiod","extensionperiod","extensiondaysavailable","extensionmaxrequests","minusers","maxusers"].forEach(n=>{const a=e.optional_fields[n];(a===0||a===null||a===""||a===void 0)&&(e.optional_fields[n]=void 0)}),this.isEditing&&"enrol"in e&&delete e.enrol;const i=(this.isEditing?["offercourseid","classname","startdate"]:["offercourseid","classname","startdate","enrol"]).filter(n=>!e[n]);if(i.length>0){this.showErrorMessage(`Campos obrigatórios ausentes: ${i.join(", ")}`),this.loading=!1;return}if(e.offercourseid=parseInt(e.offercourseid),this.isEditing&&this.classid){e.offerclassid=this.classid;let n=await l0(e);!n.error&&n.data?(this.showSuccessMessage(n.data.message),this.loadClassData()):this.showErrorMessage(n.exception.message)}else{let n=await o0(e);!n.error&&n.data?(this.showSuccessMessage(n.data.message),this.isEditing=!0,this.router.push({name:"EditClass",params:{offercourseid:this.offercourseid,classid:n.data.offerclassid}})):this.showErrorMessage(n.exception.message)}this.loading=!1},goBack(){this.offerCourse.offerid?this.router.push({name:"editar-oferta",params:{id:this.offerCourse.offerid}}):this.router.push({name:"listar-ofertas"})},showErrorMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="error",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showSuccessMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="success",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},showWarningMessage(e){this.toastTimeout&&(clearTimeout(this.toastTimeout),this.toastTimeout=null),this.showToast=!1,this.$nextTick(()=>{this.toastMessage=e,this.toastType="warning",this.showToast=!0,this.toastTimeout=setTimeout(()=>{this.showToast=!1},3e3)})},updateFormFields(){if(this.$refs.enrolSelect&&this.classData.enrol)try{this.$refs.enrolSelect.value=this.classData.enrol;const e=new Event("change");this.$refs.enrolSelect.$el.dispatchEvent(e),this.$refs.enrolSelect.$emit("input",this.classData.enrol),this.$refs.enrolSelect.$forceUpdate()}catch{}if(this.$refs.classnameInput&&this.classData.classname)try{this.$refs.classnameInput.value=this.classData.classname;const e=new Event("input");this.$refs.classnameInput.$el.dispatchEvent(e),this.$refs.classnameInput.$emit("input",this.classData.classname),this.$refs.classnameInput.$forceUpdate()}catch{}if(this.$refs.startdateInput&&this.classData.startdate)try{this.$refs.startdateInput.value=this.classData.startdate;const e=new Event("input");this.$refs.startdateInput.$el.dispatchEvent(e),this.$refs.startdateInput.$emit("input",this.classData.startdate),this.$refs.startdateInput.$forceUpdate()}catch{}this.$forceUpdate()},handleSelectAllExtensionSituations(){const e=this.extensionSituationList.every(t=>this.extensionSituations.some(s=>s.value===t.value));this.extensionSituations=e?[]:[...this.extensionSituationList],this.classData.optional_fields.extensionallowedsituations=this.extensionSituations.map(t=>t.value)},handleSelectAllReenrolSituations(){const e=this.reenrolSituationList.every(t=>this.reenrolSituations.some(s=>s.value===t.value));this.reenrolSituations=e?[]:[...this.reenrolSituationList],this.classData.optional_fields.reenrolmentsituations=this.reenrolSituations.map(t=>t.value)},restartComponent(){window.scrollTo(0,0),this.updateFormFields(),this.$forceUpdate(),setTimeout(()=>{this.updateFormFields(),this.$forceUpdate(),window.scrollTo(0,0)},500)}}},wT={class:"new-class",ref:"classView"},ET={class:"page-header-container"},CT={key:0,class:"validation-alert"},DT={class:"section-container"},xT={class:"form-group mb-3"},OT={class:"label-with-help"},ST={class:"limited-width-input",style:{"max-width":"280px"}},TT={class:"form-row mb-3"},NT={class:"form-group"},IT={class:"label-with-help"},AT={class:"limited-width-input"},MT={class:"label-with-help"},PT={class:"input-with-checkbox"},kT={class:"limited-width-input"},VT={class:"form-row mb-3"},RT={class:"label-with-help"},UT={class:"label-with-help"},LT={key:2,class:"form-group"},FT={class:"form-group mb-3"},BT={class:"label-with-help"},$T={class:"limited-width-editor"},jT={class:"form-row mb-3"},HT={key:0,class:"form-group"},qT={class:"label-with-help"},zT={class:"limited-width-input"},WT={key:1,class:"form-group"},GT={class:"label-with-help"},KT={class:"limited-width-input"},YT={class:"form-group"},QT={class:"label-with-help"},ZT={class:"limited-width-input"},JT={class:"form-row mb-3"},XT={class:"label-with-help"},eN={class:"input-with-checkbox"},tN={class:"limited-width-input"},sN={class:"section-container"},rN={class:"form-row mb-3"},nN={class:"label-with-help"},oN={class:"form-row mb-3"},iN={class:"limited-width-input"},aN={class:"form-row mb-3"},lN={class:"limited-width-input"},uN={class:"form-row mb-3"},cN={class:"limited-width-input"},dN={class:"limited-width-select"},fN={key:0,class:"text-danger"},hN={key:1,class:"section-container"},pN={class:"form-row mb-3"},mN={class:"form-group"},gN={class:"label-with-help"},vN={class:"limited-width-select"},_N={class:"section-container"},yN={class:"form-group mb-3"},bN={class:"label-with-help"},wN={class:"limited-width-select"},EN={class:"position-relative",ref:"teacherSearchContainer"},CN={class:"input-wrapper with-icon"},DN={key:0,class:"dropdown-menu show position-absolute w-100 border rounded-bottom shadow-sm",style:{top:"100%",left:"0",right:"0","max-height":"200px","overflow-y":"auto","z-index":"1000","border-top":"none","border-radius":"0 0 0.375rem 0.375rem"},ref:"teacherDropdown"},xN=["onClick","onMouseenter"],ON={key:0,class:"text-muted small"},SN={key:1,class:"dropdown-menu show position-absolute w-100 border rounded-bottom shadow-sm",style:{top:"100%",left:"0",right:"0","z-index":"1000","border-top":"none","border-radius":"0 0 0.375rem 0.375rem"}},TN={class:"my-4"},NN=["onClick"],IN={class:"actions-container"},AN={key:2,class:"loading"};function MN(e,t,s,i,n,a){const u=te("BackButton"),c=te("PageHeader"),h=te("HelpIcon"),m=te("CustomInput"),p=te("CustomCheckbox"),v=te("TextEditor"),w=te("CustomSelect"),D=te("Autocomplete"),V=te("CustomButton"),F=te("Toast"),se=p_("tooltip");return O(),S("div",wT,[f("div",ET,[k(c,{title:n.isEditing?"Editar turma":"Nova turma"},{actions:Ne(()=>[k(u,{onClick:a.goBack},null,8,["onClick"])]),_:1},8,["title"])]),n.validationAlert.show?(O(),S("div",CT,[t[36]||(t[36]=f("i",{class:"fas fa-exclamation-triangle"},null,-1)),f("span",null,q(n.validationAlert.message),1)])):X("",!0),f("div",DT,[t[53]||(t[53]=f("h2",{class:"section-title"},"CONFIGURAÇÕES GERAIS",-1)),f("div",xT,[f("div",OT,[t[37]||(t[37]=f("label",{class:"form-label"},"Nome da turma",-1)),t[38]||(t[38]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),k(h,{title:"Ajuda com nome da turma",text:"Insira um nome para a turma. Exemplo: Turma ADM 2025."})]),f("div",ST,[k(m,{modelValue:n.classData.classname,"onUpdate:modelValue":t[0]||(t[0]=A=>n.classData.classname=A),placeholder:"Digite o nome da turma",width:280,required:"",ref:"classnameInput","has-error":n.formErrors.classname.hasError,"error-message":n.formErrors.classname.message,onValidate:t[1]||(t[1]=A=>a.validateField("classname"))},null,8,["modelValue","has-error","error-message"])])]),f("div",TT,[f("div",NT,[f("div",IT,[t[39]||(t[39]=f("label",{class:"form-label"},"Data início da turma",-1)),t[40]||(t[40]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),k(h,{title:"Ajuda com data início da turma",text:"Insira uma data de início para a turma. Exemplo: 16/03/2025."})]),f("div",AT,[k(m,{modelValue:n.classData.startdate,"onUpdate:modelValue":t[2]||(t[2]=A=>n.classData.startdate=A),type:"date",width:180,required:"",class:"date-input",ref:"startdateInput","has-error":n.formErrors.startdate.hasError,"error-message":n.formErrors.startdate.message,onValidate:t[3]||(t[3]=A=>a.validateField("startdate"))},null,8,["modelValue","has-error","error-message"])])]),f("div",{class:he(["form-group",{disabled:!n.classData.optional_fields.enableenddate}])},[f("div",MT,[t[41]||(t[41]=f("label",{class:"form-label"},"Data fim da turma",-1)),t[42]||(t[42]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),k(h,{title:"Ajuda com data fim da turma",text:"Insira uma data fim para a turma. Exemplo: 16/12/2025."})]),f("div",PT,[f("div",kT,[k(m,{modelValue:n.classData.optional_fields.enddate,"onUpdate:modelValue":t[4]||(t[4]=A=>n.classData.optional_fields.enddate=A),type:"date",width:180,disabled:!n.classData.optional_fields.enableenddate,required:"",class:"date-input","has-error":n.formErrors.enddate.hasError,"error-message":n.formErrors.enddate.message,onValidate:t[5]||(t[5]=A=>a.validateField("enddate"))},null,8,["modelValue","disabled","has-error","error-message"])]),k(p,{modelValue:n.classData.optional_fields.enableenddate,"onUpdate:modelValue":t[6]||(t[6]=A=>n.classData.optional_fields.enableenddate=A),id:"enableEndDate",label:"Habilitar data fim da turma",class:"inline-checkbox",disabled:!1},null,8,["modelValue"])])],2)]),f("div",VT,[n.classData.enrol=="offer_self"?(O(),S("div",{key:0,class:he(["form-group",{disabled:!n.classData.optional_fields.enablepreenrolment}])},[f("div",RT,[t[43]||(t[43]=f("label",{class:"form-label"},"Data início pré-inscrição",-1)),k(h,{title:"Ajuda com data início pré-inscrição",text:"Data de início do período de pré-inscrição."})]),k(m,{modelValue:n.classData.optional_fields.preenrolmentstartdate,"onUpdate:modelValue":t[7]||(t[7]=A=>n.classData.optional_fields.preenrolmentstartdate=A),type:"date",width:180,disabled:!n.classData.optional_fields.enablepreenrolment,class:"date-input","has-error":n.formErrors.preenrolmentstartdate.hasError,"error-message":n.formErrors.preenrolmentstartdate.message,onValidate:t[8]||(t[8]=A=>a.validateField("preenrolmentstartdate"))},null,8,["modelValue","disabled","has-error","error-message"])],2)):X("",!0),n.classData.enrol=="offer_self"?(O(),S("div",{key:1,class:he(["form-group",{disabled:!n.classData.optional_fields.enablepreenrolment}])},[f("div",UT,[t[44]||(t[44]=f("label",{class:"form-label"},"Data fim pré-inscrição",-1)),k(h,{title:"Ajuda com data fim pré-inscrição",text:"Data de término do período de pré-inscrição."})]),k(m,{modelValue:n.classData.optional_fields.preenrolmentenddate,"onUpdate:modelValue":t[9]||(t[9]=A=>n.classData.optional_fields.preenrolmentenddate=A),type:"date",width:180,disabled:!n.classData.optional_fields.enablepreenrolment,class:"date-input","has-error":n.formErrors.preenrolmentenddate.hasError,"error-message":n.formErrors.preenrolmentenddate.message,onValidate:t[10]||(t[10]=A=>a.validateField("preenrolmentenddate"))},null,8,["modelValue","disabled","has-error","error-message"])],2)):X("",!0),n.classData.enrol=="offer_self"?(O(),S("div",LT,[t[45]||(t[45]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"}," ")],-1)),k(p,{modelValue:n.classData.optional_fields.enablepreenrolment,"onUpdate:modelValue":t[11]||(t[11]=A=>n.classData.optional_fields.enablepreenrolment=A),id:"enablePreEnrolment",label:"Habilitar pré-inscrição",disabled:!1},null,8,["modelValue"])])):X("",!0)]),f("div",FT,[f("div",BT,[t[46]||(t[46]=f("label",{class:"form-label"},"Descrição da turma",-1)),k(h,{title:"Ajuda com descrição da turma",text:"Esta descrição estará disponível para os usuários na página intermediária do curso. Exemplo: Esta turma se destina a usuários com os cargos administrativos que foram selecionados para a realização dos cursos obrigatórios do ano de 2025."})]),f("div",$T,[k(v,{modelValue:n.classData.optional_fields.description,"onUpdate:modelValue":t[12]||(t[12]=A=>n.classData.optional_fields.description=A),placeholder:"Digite a descrição da turma aqui...",rows:5,disabled:!1},null,8,["modelValue"])])]),f("div",jT,[n.classData.enrol=="offer_self"?(O(),S("div",HT,[f("div",qT,[t[47]||(t[47]=f("label",{class:"form-label"},"Mínimo de usuários inscritos",-1)),k(h,{title:"Ajuda com mínimo de usuários",text:"Número mínimo de usuários para a turma."})]),f("div",zT,[k(m,{modelValue:n.classData.optional_fields.minusers,"onUpdate:modelValue":t[13]||(t[13]=A=>n.classData.optional_fields.minusers=A),type:"number",width:180,"has-error":n.formErrors.minusers.hasError,"error-message":n.formErrors.minusers.message,onValidate:t[14]||(t[14]=A=>a.validateField("minusers")),min:0},null,8,["modelValue","has-error","error-message"])])])):X("",!0),n.classData.enrol=="offer_self"?(O(),S("div",WT,[f("div",GT,[t[48]||(t[48]=f("label",{class:"form-label"},"Máximo de usuários inscritos",-1)),k(h,{title:"Ajuda com máximo de usuários",text:"Número máximo de usuários para a turma."})]),f("div",KT,[k(m,{modelValue:n.classData.optional_fields.maxusers,"onUpdate:modelValue":t[15]||(t[15]=A=>n.classData.optional_fields.maxusers=A),type:"number",width:180,"has-error":n.formErrors.maxusers.hasError,"error-message":n.formErrors.maxusers.message,onValidate:t[16]||(t[16]=A=>a.validateField("maxusers")),min:0},null,8,["modelValue","has-error","error-message"])])])):X("",!0),f("div",YT,[f("div",QT,[t[49]||(t[49]=f("label",{class:"form-label"},"Papel atribuído por padrão",-1)),t[50]||(t[50]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),k(h,{title:"Ajuda com papel atribuído por padrão",text:"O papel atribuído será o papel que o usuário receberá dentro do curso após sua matrícula na turma. Exemplo: Estudante, Professor, Coordenador, etc…"})]),f("div",ZT,[k(w,{modelValue:n.classData.optional_fields.roleid,"onUpdate:modelValue":t[17]||(t[17]=A=>n.classData.optional_fields.roleid=A),options:n.roleOptions,width:180,required:"","has-error":n.formErrors.roleid.hasError,"error-message":n.formErrors.roleid.message,onValidate:t[18]||(t[18]=A=>a.validateField("roleid"))},null,8,["modelValue","options","has-error","error-message"])])])]),f("div",JT,[f("div",{class:he(["form-group",{disabled:!n.classData.optional_fields.enableenrolperiod}])},[f("div",XT,[t[51]||(t[51]=f("label",{class:"form-label"},"Prazo de conclusão da turma",-1)),t[52]||(t[52]=f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"},null,-1)),k(h,{title:"Ajuda com prazo de conclusão da turma",text:"O prazo de conclusão refere-se a quantidade de dias que um usuário terá para realizar o curso. Após esse período, se o usuário não obtiver uma situação de êxito na matrícula (Aprovado ou Concluído) durante seu progresso no curso, o sistema encerrará sua matrícula."+(a.maxEnrolPeriod?` O valor máximo permitido é de ${a.maxEnrolPeriod} dias, que corresponde ao período entre as datas de início e fim da turma.`:"")},null,8,["text"])]),f("div",eN,[f("div",tN,[k(m,{modelValue:n.classData.optional_fields.enrolperiod,"onUpdate:modelValue":t[19]||(t[19]=A=>n.classData.optional_fields.enrolperiod=A),type:"number",width:180,disabled:!n.classData.optional_fields.enableenrolperiod,required:"","has-error":n.formErrors.enrolperiod.hasError,"error-message":n.formErrors.enrolperiod.message,max:a.maxEnrolPeriod,onValidate:t[20]||(t[20]=A=>a.validateField("enrolperiod"))},null,8,["modelValue","disabled","has-error","error-message","max"])]),ut(k(p,{modelValue:n.classData.optional_fields.enableenrolperiod,"onUpdate:modelValue":t[21]||(t[21]=A=>n.classData.optional_fields.enableenrolperiod=A),id:"enableEnrolPeriod",label:"Habilitar prazo de conclusão",class:"inline-checkbox",disabled:a.shouldDisableEnrolPeriod},null,8,["modelValue","disabled"]),[[se,a.shouldDisableEnrolPeriod?"Não é possível habilitar prazo de conclusão para turmas de um dia (data início = data fim)":""]])])],2)])]),f("div",sN,[f("div",rN,[f("div",{class:he(["form-group",{disabled:!n.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[f("div",nN,[t[54]||(t[54]=f("label",{class:"form-label"},"Prorrogação de matrícula",-1)),k(h,{title:"Ajuda com prorrogação de matrícula",text:"A prorrogação estende o Prazo de conclusão do usuário, permitindo sua permanência na turma enquanto ela estiver ativa. No entanto, não redefine seu progresso, garantindo que ele retome o curso de onde parou. Nota: A prorrogação só pode ser habilitada quando o Prazo de conclusão da turma estiver habilitado."})]),ut(k(p,{modelValue:n.classData.optional_fields.enableextension,"onUpdate:modelValue":t[22]||(t[22]=A=>n.classData.optional_fields.enableextension=A),id:"enableExtension",label:"Habilitar Prorrogação de matrícula",disabled:!n.classData.optional_fields.enableenrolperiod},null,8,["modelValue","disabled"]),[[se,n.classData.optional_fields.enableenrolperiod?"":"É necessário habilitar o Prazo de conclusão da turma primeiro"]])],2)]),f("div",oN,[f("div",{class:he(["form-group",{disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[55]||(t[55]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Quantos dias serão acrescentados para prorrogação?"),f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),f("div",iN,[k(m,{modelValue:n.classData.optional_fields.extensionperiod,"onUpdate:modelValue":t[23]||(t[23]=A=>n.classData.optional_fields.extensionperiod=A),type:"number",width:180,disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,required:"","has-error":n.formErrors.extensionperiod.hasError,"error-message":n.formErrors.extensionperiod.message,onValidate:t[24]||(t[24]=A=>a.validateField("extensionperiod"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),f("div",aN,[f("div",{class:he(["form-group",{disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[56]||(t[56]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Quantos dias antes do término do prazo de matrícula o botão de prorrogação deve ser exibido?"),f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),f("div",lN,[k(m,{modelValue:n.classData.optional_fields.extensiondaysavailable,"onUpdate:modelValue":t[25]||(t[25]=A=>n.classData.optional_fields.extensiondaysavailable=A),type:"number",width:180,disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,required:"","has-error":n.formErrors.extensiondaysavailable.hasError,"error-message":n.formErrors.extensiondaysavailable.message,onValidate:t[26]||(t[26]=A=>a.validateField("extensiondaysavailable"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),f("div",uN,[f("div",{class:he(["form-group",{disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[57]||(t[57]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Quantas vezes o usuário pode pedir prorrogação?"),f("i",{class:"icon fa fa-exclamation-circle text-danger fa-fw",title:"Obrigatório",role:"img","aria-label":"Obrigatório"})],-1)),f("div",cN,[k(m,{modelValue:n.classData.optional_fields.extensionmaxrequests,"onUpdate:modelValue":t[27]||(t[27]=A=>n.classData.optional_fields.extensionmaxrequests=A),type:"number",width:180,disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,required:"","has-error":n.formErrors.extensionmaxrequests.hasError,"error-message":n.formErrors.extensionmaxrequests.message,onValidate:t[28]||(t[28]=A=>a.validateField("extensionmaxrequests"))},null,8,["modelValue","disabled","has-error","error-message"])])],2)]),f("div",{class:he(["form-group mb-3",{disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,"dependent-field":!0}])},[t[58]||(t[58]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Para quais situações de matrícula é permitida a prorrogação?")],-1)),f("div",dN,[k(D,{modelValue:n.extensionSituations,"onUpdate:modelValue":t[29]||(t[29]=A=>n.extensionSituations=A),items:a.extensionSituationList,placeholder:"Selecione as situações...",disabled:!n.classData.optional_fields.enableextension||!n.classData.optional_fields.enableenrolperiod,width:280,"show-all-option":!0,"auto-open":!1,onSelectAll:a.handleSelectAllExtensionSituations},null,8,["modelValue","items","disabled","onSelectAll"]),n.formErrors.extensionsituations.hasError?(O(),S("div",fN,q(n.formErrors.extensionsituations.message),1)):X("",!0)])],2)]),n.classData.enrol=="offer_self"?(O(),S("div",hN,[f("div",pN,[f("div",mN,[f("div",gN,[t[59]||(t[59]=f("label",{class:"form-label"},"Habilitar rematrícula",-1)),k(h,{title:"Ajuda com habilitar rematrícula",text:"Permite que usuários se matriculem novamente na turma após concluírem ou saírem dela."})]),k(p,{modelValue:n.classData.optional_fields.enablereenrol,"onUpdate:modelValue":t[30]||(t[30]=A=>n.classData.optional_fields.enablereenrol=A),id:"enableReenrol",label:"Habilitar rematrícula",disabled:!1},null,8,["modelValue"])])]),f("div",{class:he(["form-group mb-3",{disabled:!n.classData.optional_fields.enablereenrol}])},[t[60]||(t[60]=f("div",{class:"label-with-help"},[f("label",{class:"form-label"},"Quais situações de matrícula permitem rematrícula?")],-1)),f("div",vN,[k(D,{modelValue:n.reenrolSituations,"onUpdate:modelValue":t[31]||(t[31]=A=>n.reenrolSituations=A),items:a.reenrolSituationList,placeholder:"Selecione as situações...",disabled:!n.classData.optional_fields.enablereenrol,width:280,"show-all-option":!0,"auto-open":!1,onSelectAll:a.handleSelectAllReenrolSituations},null,8,["modelValue","items","disabled","onSelectAll"])])],2)])):X("",!0),f("div",_N,[f("div",yN,[f("div",bN,[t[61]||(t[61]=f("label",{class:"form-label"},"Atribuir corpo docente",-1)),k(h,{title:"Ajuda com atribuir corpo docente",text:"Ao selecionar usuários para a composição do corpo docente, ele será matriculado na turma com o papel “Professor”."})]),f("div",wN,[f("div",EN,[f("div",CN,[ut(f("input",{type:"text","onUpdate:modelValue":t[32]||(t[32]=A=>n.teacherSearchTerm=A),placeholder:"Pesquisar ...",class:"form-control custom-input",onInput:t[33]||(t[33]=(...A)=>a.handleTeacherInput&&a.handleTeacherInput(...A)),onFocus:t[34]||(t[34]=(...A)=>a.handleTeacherInputFocus&&a.handleTeacherInputFocus(...A)),onKeydown:t[35]||(t[35]=(...A)=>a.handleKeydown&&a.handleKeydown(...A)),ref:"teacherSearchInput"},null,544),[[Xt,n.teacherSearchTerm]])]),n.showTeacherDropdown&&n.teacherList.length>0?(O(),S("div",DN,[(O(!0),S(Ie,null,at(n.teacherList,(A,re)=>(O(),S("div",{key:A.id,class:he(["dropdown-item",{active:n.highlightedIndex===re}]),onClick:Y=>a.selectTeacher(A),onMouseenter:Y=>n.highlightedIndex=re},[f("div",null,[f("div",null,q(A.fullname),1),A.email?(O(),S("div",ON,q(A.email),1)):X("",!0)])],42,xN))),128))],512)):X("",!0),n.showTeacherDropdown&&n.teacherSearchTerm.length>=3&&n.teacherList.length===0?(O(),S("div",SN,t[62]||(t[62]=[f("div",{class:"dropdown-item-text text-center fst-italic"},"Nenhum professor encontrado",-1)]))):X("",!0)],512),f("div",TN,[(O(!0),S(Ie,null,at(n.selectedTeachers,A=>(O(),S("a",{key:A.id,class:"tag badge bg-primary text-white p-2 cursor-pointer mr-2",onClick:re=>a.removeTeacher(A.id)},[t[63]||(t[63]=f("i",{class:"fas fa-times mr-1"},null,-1)),Je(" "+q(A.fullname),1)],8,NN))),128))])])])]),t[65]||(t[65]=f("div",{class:"required-fields-message"},[f("div",{class:"form-info"},[Je(" Este formulário contém campos obrigatórios marcados com "),f("i",{class:"fa fa-exclamation-circle text-danger"})])],-1)),f("div",IN,[k(V,{variant:"primary",label:"Salvar",loading:n.loading,onClick:a.saveClass},null,8,["loading","onClick"]),k(V,{variant:"secondary",label:"Cancelar",onClick:a.goBack},null,8,["onClick"])]),n.loading?(O(),S("div",AN,t[64]||(t[64]=[f("div",{class:"spinner-border",role:"status"},[f("span",{class:"sr-only"},"Carregando...")],-1)]))):X("",!0),k(F,{show:n.showToast,message:n.toastMessage,type:n.toastType,duration:3e3},null,8,["show","message","type"])],512)}const up=He(bT,[["render",MN],["__scopeId","data-v-74d0f2e4"]]),PN=[{path:"/",name:"listar-ofertas",component:Z1,meta:{title:"Gerenciar Ofertas"}},{path:"/new-offer",name:"nova-oferta",component:lp,meta:{title:"Nova Oferta"}},{path:"/edit-offer/:id",name:"editar-oferta",component:lp,props:!0,meta:{title:"Editar Oferta"}},{path:"/new-class/:offercourseid",name:"NewClass",component:up,props:!0,meta:{title:"Nova Turma"}},{path:"/edit-class/:offercourseid/:classid",name:"EditClass",component:up,props:!0,meta:{title:"Editar Turma"}},{path:"/new-subscribed-users/:offerclassid",name:"usuarios-matriculados",component:eO,props:!0,meta:{title:"Usuários matriculados"}},{path:"/:pathMatch(.*)*",redirect:"/"}],jo=Gw({history:nw("/local/offermanager/"),routes:PN,scrollBehavior(){return{top:0}}});jo.beforeEach((e,t,s)=>{document.title=e.meta.title||"Gerenciar Ofertas",s()}),jo.onError(e=>{console.error("Erro de navegação:",e),(e.name==="NavigationDuplicated"||e.message.includes("No match")||e.message.includes("missing required param"))&&jo.push("/")});const r3="",kN=()=>{const e=document.createElement("link");e.rel="stylesheet",e.href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css",document.head.appendChild(e)};return{init:(e,t={})=>{kN();const s=Wy(Tb);if(s.use(Db()),s.use(jo),t&&t.route){let n={"new-offer":"/new-offer","edit-offer":"/edit-offer","new-subscribed-users":"/new-subscribed-users","new-class":"/new-class","edit-class":"/edit-class"}[t.route]||"/";t.route==="edit-offer"&&t.offerId&&(n=`/edit-offer/${t.offerId}`),t.route==="new-subscribed-users"&&t.subscribeId&&(n=`/new-subscribed-users/${t.subscribeId}`),t.route==="new-class"&&t.offercourseid&&(n=`/new-class/${t.offercourseid}`),t.route==="edit-class"&&t.offercourseid&&t.classid&&(n=`/edit-class/${t.offercourseid}/${t.classid}`),jo.replace(n)}return s.mount(e),s}}});
//# sourceMappingURL=app-lazy.min.js.map
